package com.hnbp.local.qyxxcx.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hnbp.local.sszbfx.model.QueryCondition;
import com.hnbp.common.core.utils.zhzs.paging.PageParameter;
import com.hnbp.local.qyxxcx.mapper.CompanyInformationMapper;
import com.hnbp.local.qyxxcx.service.CompanyInformationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;

/**
 * <AUTHOR>
 * @Description: 企业信息查询ServiceImpl
 * @date 2025-07-03
 */
@Service
public class CompanyInformationServiceImpl implements CompanyInformationService {

    @Autowired
    private CompanyInformationMapper companyInformationMapper;

    /**
     * 指定企业税收查询
     * @param pagingParameter
     * @return
     */
    @Override
    public PageInfo zdnsrsscx(PageParameter pagingParameter) throws SQLException {
        QueryCondition queryCondition = (QueryCondition) pagingParameter.getParams();
        PageHelper.startPage(pagingParameter.getPage(), pagingParameter.getLimit());
        return new PageInfo(companyInformationMapper.zdnsrsscx(queryCondition));
    }

}
