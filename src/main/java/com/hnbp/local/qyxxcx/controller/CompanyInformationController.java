package com.hnbp.local.qyxxcx.controller;

import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.common.core.utils.zhzs.paging.PageParameter;
import com.hnbp.local.sszbfx.util.ParameterUtil;
import com.hnbp.local.qyxxcx.service.CompanyInformationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 企业信息查询
 * @date 2025-07-03
 */
@Controller
public class CompanyInformationController {

    @Autowired
    private CompanyInformationService companyInformationService;

    /**
     * 指定企业税收查询
     * @param parameterMap
     * @return ResultMsg
     * @throws IOException
     * @throws SQLException
     */
    @RequestMapping("zdnsrsscx_bzb")
    @ResponseBody
    public ResultMsg zdnsrsscx_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {
        //参数处理
        PageInfo pageInfo = companyInformationService.zdnsrsscx((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

}
