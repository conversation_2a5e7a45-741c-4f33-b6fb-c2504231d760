package com.hnbp.local.sszbfx.service;

import com.github.pagehelper.PageInfo;
import com.hnbp.local.sszbfx.model.QueryCondition;
import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarChart;
import com.hnbp.common.core.utils.zhzs.echarts.line.LineChart;
import com.hnbp.common.core.utils.zhzs.echarts.pie.PieChart;
import com.hnbp.common.core.utils.zhzs.paging.PageParameter;
import com.hnbp.local.sszbfx.model.*;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: service
 * @date 2024-09-14
 */

public interface IndustryAnalysisService {

    /**
     * 按税种统计整体分析(饼图)
     *
     * @return PageInfo
     * @Title taxStructureAnalysis
     * <AUTHOR>
     */
    public PieChart taxStructureAnalysis(QueryCondition queryCondition) throws SQLException;
    public PieChart taxStructureAnalysisV2(QueryCondition queryCondition) throws SQLException;


    public PieChart rankingByTaxpayerEchar(QueryCondition queryCondition) throws SQLException;

    public BarChart rankingByTaxpayerEcharline(QueryCondition queryCondition) throws SQLException;


    /**
     * 按税种统计整体分析(表)
     *
     * @return PageInfo
     * @Title byTaxStatistics
     * <AUTHOR>
     */
    public PageInfo byTaxStatistics(QueryBusinessStatistics pageParameter) throws SQLException;

    /**
     * 按税种统计整体分析(历年分月柱状图)
     *
     * @return PageInfo
     * @Title changesOverTheYears
     * <AUTHOR>
     */
    public BarChart changesOverTheYears(QueryCondition queryCondition) throws SQLException;
    public BarChart changesOverTheYearsV2(QueryCondition queryCondition) throws SQLException;

    /**
     * 按地区统计(表)
     *
     * @param pageParameter
     * @return PageInfo
     * @Title regionalStatistics
     * @date 2019/7/11 18:06
     * <AUTHOR>
     */
    public PageInfo regionalStatistics(QueryBusinessStatistics pageParameter) throws SQLException;

    /**
     * 按纳税人排名统计
     *
     * @param pageParameter
     * @return PageInfo
     * @Title rankingByTaxpayer
     * @date 2019/7/12 13:26
     * <AUTHOR>
     */
    public PageInfo rankingByTaxpayer(QueryBusinessStatistics pageParameter) throws SQLException;

    public PageInfo findFinancialResources(PageParameter pageParameter) throws SQLException;

    public PageInfo findFinancialResourcesTax(PageParameter pageParameter) throws SQLException;


    /**
     * 按行业统计
     *
     * @param pageParameter
     * @return PageInfo
     * @Title statisticsByIndustry
     * @date 2019/7/12 14:10
     * <AUTHOR>
     */
    public PageInfo statisticsByIndustry(PageParameter pageParameter) throws SQLException;
    public PageInfo statisticsByIndustryV2(PageParameter pageParameter) throws SQLException;

    /**
     * 按登记类型统计
     *
     * @param pageParameter
     * @return PageInfo
     * @Title statisticsByRegistrationType
     * @date 2019/7/12 14:13
     * <AUTHOR>
     */
    public PageInfo statisticsByRegistrationType(QueryBusinessStatistics pageParameter) throws SQLException;


    /**
     * 通用查询
     */

    /**
     * 纳税净入库通用查询
     *
     * @param pageParameter
     * @return PageInfo
     * @Title warehousingGeneralQuery
     * @date 2019/7/15 11:09
     * <AUTHOR>
     */
    public PageInfo warehousingGeneralQuery(PageParameter pageParameter) throws SQLException;

    /**
     * 分户分税种明细查询
     *
     * @param pageParameter
     * @return PageInfo
     * @Title detailsOfHouseholdTaxInquiry
     * @date 2019/7/15 11:09
     * <AUTHOR>
     */
    public PageInfo detailsOfHouseholdTaxInquiry(PageParameter pageParameter) throws SQLException;


    /*
     *
     * @Title
     * @date  2019/7/17 9:27
     * @param
     * @return
     * <AUTHOR>
     */

    /**
     * 税收总额排名 ----
     *
     * @param pageParameter
     * @return PageInfo
     * @Title totalTaxRanking
     * @date 2019/7/17 9:10
     * <AUTHOR>
     */
    public PageInfo totalTaxRanking(PageParameter pageParameter) throws SQLException;

    /**
     * 企业利润变化情况表 ----
     *
     * @param pageParameter
     * @return PageInfo
     * @Title TotalTaxRanking
     * @date 2019/7/17 9:10
     * <AUTHOR>
     */
    public PageInfo enterpriseProfitChanges(QueryBusinessStatistics pageParameter) throws SQLException;

    /**
     * 增值税分行业情况(饼图)
     *
     * @param queryCondition
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.pie.PieChartsSeriesData>
     * @Title overallStreetConditionsChart
     * @date 2019/7/17 9:12
     * <AUTHOR>
     */
    public PieChart VATSectorInformationEcharts(QueryCondition queryCondition) throws SQLException;

    /**
     * 增值税分行业情况明细(饼图)
     *
     * @param queryCondition
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.pie.PieChartsSeriesData>
     * @Title overallStreetConditionsChart
     * @date 2019/7/17 9:12
     * <AUTHOR>
     */
    public PieChart detailsOfVATIndustryEcharts(QueryCondition queryCondition) throws SQLException;

    /**
     * 增值税分行业情况明细(表格)
     *
     * @param pageParameter
     * @return PageInfo
     * @Title detailsOfVATIndustryTable
     * @date 2019/7/17 9:15
     * <AUTHOR>
     */
    public PageInfo detailsOfVATIndustryTable(PageParameter pageParameter) throws SQLException;

    /**
     * 注册企业增长分析
     *
     * @param pageParameter
     * @return PageInfo
     * @Title registeredBusinessGrowthAnalysis
     * @date 2019/7/17 9:15
     * <AUTHOR>
     */
    public PageInfo registeredBusinessGrowthAnalysis(PageParameter pageParameter) throws SQLException;

    /**
     * 重点税源企业分析
     *
     * @param pageParameter
     * @return PageInfo
     * @Title analysisOfKeyTaxSources
     * @date 2019/7/17 9:16
     * <AUTHOR>
     */
    public PageInfo analysisOfKeyTaxSources(QueryBusinessStatistics pageParameter) throws SQLException;

    /**
     * 个人所得税增收品目分析 (饼图)
     *
     * @param queryCondition
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.pie.PieChartsSeriesData>
     * @Title overallStreetConditionsChart
     * @date 2019/7/17 9:12
     * <AUTHOR>
     */
    public PieChart analysisOfAdditionalItemsEcharts(QueryCondition queryCondition) throws SQLException;

    /**
     * 个人所得税增收品目分析明细 (饼图)
     *
     * @param queryCondition
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.pie.PieChartsSeriesData>
     * @Title analysisOfAdditionalItemsDetailEchart
     * @date 2019/7/17 9:20
     * <AUTHOR>
     */
    public PieChart analysisOfAdditionalItemsDetailEchart(QueryCondition queryCondition) throws SQLException;

    /**
     * 个人所得税增收品目分析(table)
     *
     * @param pageParameter
     * @return PageInfo
     * @Title analysisOfAdditionalItemsTable
     * @date 2019/7/18 11:27
     * <AUTHOR>
     */
    public PageInfo analysisOfAdditionalItemsTable(PageParameter pageParameter) throws SQLException;

    /**
     * 企业所得税分行业(饼图)
     *
     * @param queryCondition
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.pie.PieChartsSeriesData>
     * @Title enterpriseIncomeTaxSubsectorsEchart
     * @date 2019/7/17 9:21
     * <AUTHOR>
     */
    public PieChart enterpriseIncomeTaxSubsectorsEchart(QueryCondition queryCondition) throws SQLException;

    /**
     * 企业所得税分行业明细(饼图)
     *
     * @param queryCondition
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.pie.PieChartsSeriesData>
     * @Title detailsEnterpriseIncomeTaxSubsectorsEchart
     * @date 2019/7/17 9:22
     * <AUTHOR>
     */
    public PieChart detailsEnterpriseIncomeTaxSubsectorsEchart(QueryCondition queryCondition) throws SQLException;

    /**
     * 企业所得税分行业明细(表格)
     *
     * @param pageParameter
     * @return PageInfo
     * @Title detailsEnterpriseIncomeTaxSubsectorsTable
     * @date 2019/7/17 9:23
     * <AUTHOR>
     */
    public PageInfo detailsEnterpriseIncomeTaxSubsectorsTable(QueryBusinessStatistics pageParameter) throws SQLException;


    /**
     * 企业纳税规模分析(表格)
     *
     * @param pageParameter
     * @return PageInfo
     * @Title enterpriseScaleAnalysis
     * @date 2019/7/17 9:23
     * <AUTHOR>
     */
    public PageInfo enterpriseScaleAnalysis(PageParameter pageParameter) throws SQLException;


    public PageInfo subIndustryAnalysis(PageParameter pageParameter) throws SQLException;

    public PageInfo revenueByTaxTypeTable(PageParameter pagingParameter);
    public PageInfo revenueByTaxTypeTableV2(PageParameter pagingParameter);

    public PageInfo revenueByTaxTypeMxTable(PageParameter pagingParameter);

    public PageInfo revenueByTaxTypeMxTableXzsy(PageParameter pagingParameter);


    public PageInfo subCompaniesIndustryAnalysis(PageParameter pagingParameter) throws SQLException;

    public PieChart taxStructureAnalysis_fsz(QueryCondition queryCondition) throws SQLException;

    public PageInfo AnlzByZspm(PageParameter pagingParameter);

    public PageInfo AnlzByZsxm(PageParameter pagingParameter);

    public PageInfo taxpayerTaxTrendAnlzMx(PageParameter pagingParameter);

    public LineChart taxStructureAnalysis_five(QueryCondition queryCondition) throws SQLException;

    public PieChart industryAnalysis(QueryCondition queryCondition) throws SQLException;
    public PieChart industryAnalysisV2(QueryCondition queryCondition) throws SQLException;

    public PageInfo overallStatisticsByHyml(PageParameter pagingParameter);

    public PageInfo districtAndCountyCompletionTable_v2(PageParameter pageParameter);


    public PageInfo findTssyInfo(PageParameter pageParameter);

    /**
     * 查询行业大类条件
     */
    List<HashMap<String,Object>> findCy_hyml_hydl(String fhyml) throws SQLException;

    /**
     * 查询注册登记类型条件
     */
    List<HashMap<String,Object>> findZcdjlx() throws SQLException;

    /**
     * 查询预算科目条件
     */
    List<HashMap<String,Object>> findYskm() throws SQLException;

    /**
     * 查询所属税务机关条件
     */
    List<HashMap<String,Object>> findFssswjg() throws SQLException;

    /**
     * 查询征收项目条件
     */
    List<HashMap<String,Object>> findZsxm() throws SQLException;

    PageInfo statisticsByEconomicType(PageParameter pagingParameter);

    PageInfo declareDetailQuery(PageParameter pagingParameter);

    List<HashMap<String,Object>> findFyskm();

    PageInfo incomeByCity(PageParameter pagingParameter) throws SQLException;

    /**
     * 查询纳税人状态条件
     *
     * @return
     */
    List<HashMap<String,Object>> findFnsrzt() throws SQLException;

    /**
     * 查询所属区域条件
     *
     * @return
     */
    List<HashMap<String,Object>> findFssqy() throws SQLException;

    /**
     * 查询所属单位
     *
     * @return
     */
    List<HashMap<String,Object>> findFssdw() throws SQLException;

    public PageInfo enterpriseScaleAnalysis_2(PageParameter pageParameter);

    public PageInfo enterpriseScaleAnalysisMx(PageParameter pageParameter);


    public PageInfo revenueByIndustryMxTable(PageParameter pageParameter);

    public PageInfo revenueByAreaMxTable(PageParameter pagingParameter);

    public PageInfo Sszbfxztfx_table(PageParameter pagingParameter);

    public BarChart Sszbfxztfx_Chart(QueryCondition queryCondition);

    public BarChart Sszbfxztfxfdq_Chart(QueryCondition queryCondition);

    public PieChart Sszbfxztfxfsz_Chart(QueryCondition queryCondition) throws SQLException;

    public BarChart Sszbfxztfxfhy_Chart(QueryCondition queryCondition);

    public PageInfo Sszbfxxzsy_table(PageParameter pagingParameter);

    public PageInfo Sszbfxycsy_table(PageParameter pagingParameter);

    public BarChart Sszbfxztfxsysr_Chart(QueryCondition queryCondition);

    public BarChart Sszbfxztfxsyhs_Chart(QueryCondition queryCondition);

    public PieChart Sszbfxztfxsyfdq_Chart(QueryCondition queryCondition) throws SQLException;

    public PieChart Sszbfxztfxsysrfdq_Chart(QueryCondition queryCondition) throws SQLException;

    public PieChart Sszbfxztfxsyfhy_Chart(QueryCondition queryCondition) throws SQLException;

    public PieChart Sszbfxztfxsyfhysr_Chart(QueryCondition queryCondition) throws SQLException;

    public PieChart Sszbfxztfxsyfzclx_Chart(QueryCondition queryCondition) throws SQLException;

    public PieChart Sszbfxztfxsyfzclxsr_Chart(QueryCondition queryCondition) throws SQLException;

    /**
     * 税种分行业
     * @param pageParameter
     * @return
     * @throws SQLException
     */
    public PageInfo subIndustryAnalysis_new_bzb(PageParameter pageParameter) throws SQLException;


}
