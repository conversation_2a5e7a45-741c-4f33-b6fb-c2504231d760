package com.hnbp.local.sszbfx.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hnbp.local.sszbfx.model.QueryCondition;
import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarChart;
import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesData;
import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData;
import com.hnbp.common.core.utils.zhzs.echarts.ChartUtil;
import com.hnbp.common.core.utils.zhzs.echarts.line.LineChart;
import com.hnbp.common.core.utils.zhzs.echarts.line.LineSeriesInitData;
import com.hnbp.common.core.utils.zhzs.echarts.pie.PieChart;
import com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData;
import com.hnbp.common.core.utils.zhzs.paging.PageParameter;
import com.hnbp.local.sszbfx.mapper.IndustryAnalysisMapper;
import com.hnbp.local.sszbfx.model.NewTaxSource5Year;
import com.hnbp.local.sszbfx.model.QueryBusinessStatistics;
import com.hnbp.local.sszbfx.service.IndustryAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @Description: service实现层
 * @date 2024-09-14
 */
@Service
public class IndustryAnalysisServiceImpl implements IndustryAnalysisService {

    @Autowired
    private IndustryAnalysisMapper industryAnalysisMapper;

    /**
     * 按税种统计整体分析(饼图)
     *
     * @return PageInfo
     * @Title taxStructureAnalysis
     * <AUTHOR>
     */
    @Override
    public PieChart taxStructureAnalysis(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = null;
        PieChart pieChart = new PieChart();
        //饼图标题
        pieChart.setTitle_text(queryCondition.getStart_year() + "年" +
                (queryCondition.getFstartmonth().indexOf("0") == 0 ? queryCondition.getFstartmonth().replace("0", "") : queryCondition.getFstartmonth())
                + "-" +
                (queryCondition.getFendmonth().indexOf("0") == 0 ? queryCondition.getFendmonth().replace("0", "") : queryCondition.getFendmonth()) +
                "月税种结构分析");
        //查询数据
        pieChartsSeriesData = industryAnalysisMapper.taxStructureAnalysis(queryCondition);
        //饼图数据封装
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }

    @Override
    public PieChart taxStructureAnalysisV2(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = null;
        PieChart pieChart = new PieChart();
        //饼图标题
        pieChart.setTitle_text(queryCondition.getStart_year() + "年" +
                (queryCondition.getFstartmonth().indexOf("0") == 0 ? queryCondition.getFstartmonth().replace("0", "") : queryCondition.getFstartmonth())
                + "-" +
                (queryCondition.getFendmonth().indexOf("0") == 0 ? queryCondition.getFendmonth().replace("0", "") : queryCondition.getFendmonth()) +
                "月税种结构分析");
        //查询数据
        pieChartsSeriesData = industryAnalysisMapper.taxStructureAnalysisV2(queryCondition);
        //饼图数据封装
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }

    @Override
    public PieChart rankingByTaxpayerEchar(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = null;
        PieChart pieChart = new PieChart();
        //饼图标题
        pieChart.setTitle_text(queryCondition.getStart_year() + "年" +
                (queryCondition.getFstartmonth().indexOf("0") == 0 ? queryCondition.getFstartmonth().replace("0", "") : queryCondition.getFstartmonth())
                + "-" +
                (queryCondition.getFendmonth().indexOf("0") == 0 ? queryCondition.getFendmonth().replace("0", "") : queryCondition.getFendmonth()) +
                "月税种结构分析");
        //查询数据
        pieChartsSeriesData = industryAnalysisMapper.rankingByTaxpayerEchar(queryCondition);
        //饼图数据封装
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }

    @Override
    public BarChart rankingByTaxpayerEcharline(QueryCondition queryCondition) throws SQLException {
        //获取柱状图数据
        List<BarSeriesInitData> barSeriesInitData = industryAnalysisMapper.rankingByTaxpayerEcharline(queryCondition);
        //柱状图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        BarChart barChart = ChartUtil.BieCharDataAssembly(barSeriesInitData, extraParameter);
        barChart.setTitle_text(queryCondition.getFzsxm() + "历年变化情况");
        return barChart;
    }


    /**
     * 按税种统计整体分析(表)
     *
     * @return PageInfo
     * @Title byTaxStatistics
     * <AUTHOR>
     */
    @Override
    public PageInfo byTaxStatistics(QueryBusinessStatistics pageParameter) throws SQLException {

        PageHelper.startPage(pageParameter.getPage(), pageParameter.getLimit());
        return new PageInfo(industryAnalysisMapper.byTaxStatistics(pageParameter));
    }

    /**
     * 按税种统计整体分析(历年分月柱状图)
     *
     * @return PageInfo
     * @Title changesOverTheYears
     * <AUTHOR>
     */
    @Override
    public BarChart changesOverTheYears(QueryCondition queryCondition) throws SQLException {
        //获取柱状图数据
        List<BarSeriesInitData> barSeriesInitData = industryAnalysisMapper.changesOverTheYears(queryCondition);
        //柱状图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        BarChart barChart = ChartUtil.BieCharDataAssembly(barSeriesInitData, extraParameter);
        barChart.setTitle_text(queryCondition.getFzsxm() + "历年变化情况");
        return barChart;
    }

    @Override
    public BarChart changesOverTheYearsV2(QueryCondition queryCondition) throws SQLException {
        //获取柱状图数据
        List<BarSeriesInitData> barSeriesInitData = industryAnalysisMapper.changesOverTheYearsV2(queryCondition);
        //柱状图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        BarChart barChart = ChartUtil.BieCharDataAssembly(barSeriesInitData, extraParameter);
        barChart.setTitle_text(queryCondition.getFzsxm() + "历年变化情况");
        return barChart;
    }

    /**
     * 按地区统计(表)
     *
     * @param pageParameter
     * @return PageInfo
     * @Title regionalStatistics
     * @date 2019/7/11 18:06
     * <AUTHOR>
     */
    @Override
    public PageInfo regionalStatistics(QueryBusinessStatistics pageParameter) throws SQLException {

        PageHelper.startPage(pageParameter.getPage(), pageParameter.getLimit());
        return new PageInfo(industryAnalysisMapper.regionalStatistics(pageParameter));
    }

    /**
     * 按纳税人排名统计
     *
     * @param pageParameter
     * @return PageInfo
     * @Title rankingByTaxpayer
     * @date 2019/7/12 13:26
     * <AUTHOR>
     */
    @Override
    public PageInfo rankingByTaxpayer(QueryBusinessStatistics pageParameter) throws SQLException {

        PageHelper.startPage(pageParameter.getPage(), pageParameter.getLimit());
        return new PageInfo(industryAnalysisMapper.rankingByTaxpayer(pageParameter));
    }


    @Override
    public PageInfo findFinancialResources(PageParameter pageParameter) throws SQLException {

        String fjzny = Optional.ofNullable(((Map) pageParameter.getParams()).get("fsj"))
                .map(yearMonth -> yearMonth + "-01")
                .orElse(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        ((Map) pageParameter.getParams()).put("fjzny", fjzny);
        HashMap params = (HashMap) pageParameter.getParams();
        List<Map<String, Object>> financialResources = industryAnalysisMapper.findFinancialResources(params);
        if(financialResources == null){
            return new PageInfo(new ArrayList());
        }else {
            return new PageInfo(financialResources);
        }
    }

    @Override
    public PageInfo findFinancialResourcesTax(PageParameter pageParameter) throws SQLException {

        String fjzny = Optional.ofNullable(((Map) pageParameter.getParams()).get("fsj"))
                .map(yearMonth -> yearMonth + "-01")
                .orElse(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        String fssnf = (String) Optional.ofNullable(((Map) pageParameter.getParams()).get("fssnf")).orElse(String.valueOf(LocalDate.now().getYear()));

        ((Map) pageParameter.getParams()).put("fssnf", fssnf);
        ((Map) pageParameter.getParams()).put("fjzny", fjzny);

        HashMap params = (HashMap) pageParameter.getParams();
        List<Map<String, Object>> financialResources = industryAnalysisMapper.findFinancialResourcesTax(params);
        if(financialResources == null){
            return new PageInfo(new ArrayList());
        }else {
            return new PageInfo(financialResources);
        }
    }

    @Override
    public PageInfo subIndustryAnalysis(PageParameter pageParameter) throws SQLException {
        QueryCondition queryCondition = (QueryCondition) pageParameter.getParams();
//        PageHelper.startPage(queryCondition.getPage(), queryCondition.getLimit());
        return new PageInfo(industryAnalysisMapper.subIndustryAnalysis(queryCondition));
    }

    @Override
    public PageInfo revenueByTaxTypeTable(PageParameter pagingParameter) {
        HashMap params = (HashMap) pagingParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.revenueByTaxTypeTable(params));
    }

    @Override
    public PageInfo revenueByTaxTypeTableV2(PageParameter pagingParameter) {
        HashMap params = (HashMap) pagingParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.revenueByTaxTypeTableV2(params));
    }

    @Override
    public PageInfo revenueByTaxTypeMxTable(PageParameter pagingParameter) {
        HashMap params = (HashMap) pagingParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));

        //纳税规模分析跳转详情页
        return new PageInfo(industryAnalysisMapper.revenueByTaxTypeMxTable(params));
    }

    @Override
    public PageInfo revenueByTaxTypeMxTableXzsy(PageParameter pagingParameter) {
        HashMap params = (HashMap) pagingParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.revenueByTaxTypeMxTableXzsy(params));
    }

    @Override
    public PageInfo subCompaniesIndustryAnalysis(PageParameter pagingParameter) throws SQLException {
        QueryCondition queryCondition = (QueryCondition) pagingParameter.getParams();
        PageHelper.startPage(queryCondition.getPage(), queryCondition.getLimit());
        return new PageInfo(industryAnalysisMapper.subCompaniesIndustryAnalysis(queryCondition));

    }

    @Override
    public PieChart taxStructureAnalysis_fsz(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = null;
        PieChart pieChart = new PieChart();
        //饼图标题
        pieChart.setTitle_text("税种结构分析");
        //查询数据
        pieChartsSeriesData = industryAnalysisMapper.taxStructureAnalysis_fsz(queryCondition);
        //饼图数据封装
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }

    @Override
    public PageInfo AnlzByZspm(PageParameter pagingParameter) {

        HashMap params = (HashMap) pagingParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.AnlzByZspm(params));
    }

    @Override
    public PageInfo AnlzByZsxm(PageParameter pagingParameter) {
        HashMap params = (HashMap) pagingParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.AnlzByZsxm(params));
    }

    @Override
    public PageInfo taxpayerTaxTrendAnlzMx(PageParameter pagingParameter) {
        HashMap params = (HashMap) pagingParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.taxpayerTaxTrendAnlzMx(params));
    }

    @Override
    public LineChart taxStructureAnalysis_five(QueryCondition queryCondition) throws SQLException {
        List<LineSeriesInitData> barSeriesInitData = industryAnalysisMapper.taxStructureAnalysis_five(queryCondition);
        HashMap extraParameter = new HashMap<>();
        LineChart barChart = ChartUtil.lineCharDataAssembly(barSeriesInitData, extraParameter);
        barChart.setTitle_text(queryCondition.getFzsxm() + "历年变化情况");
        return barChart;
    }

    @Override
    public PieChart industryAnalysis(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = null;
        PieChart pieChart = new PieChart();
        //饼图标题
        pieChart.setTitle_text(queryCondition.getStart_year() + "年" + Integer.valueOf(queryCondition.getFstartmonth()) + "-" + Integer.valueOf(queryCondition.getFendmonth()) + "月行业分析情况");
        //查询数据
        pieChartsSeriesData = industryAnalysisMapper.industryAnalysis(queryCondition);
        //饼图数据封装
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }

    @Override
    public PieChart industryAnalysisV2(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = null;
        PieChart pieChart = new PieChart();
        //饼图标题
        pieChart.setTitle_text(queryCondition.getStart_year() + "年" + Integer.valueOf(queryCondition.getFstartmonth()) + "-" + Integer.valueOf(queryCondition.getFendmonth()) + "月行业分析情况");
        //查询数据
        pieChartsSeriesData = industryAnalysisMapper.industryAnalysisV2(queryCondition);
        //饼图数据封装
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }

    @Override
    public PageInfo overallStatisticsByHyml(PageParameter pagingParameter) {
        HashMap params = (HashMap) pagingParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.overallStatisticsByHyml(params));
    }

    @Override
    public PageInfo districtAndCountyCompletionTable_v2(PageParameter pageParameter) {
        HashMap params = (HashMap) pageParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.districtAndCountyCompletionTable_v2(params));
    }

    @Override
    public PageInfo findTssyInfo(PageParameter pageParameter) {
        HashMap params = (HashMap) pageParameter.getParams();
        PageHelper.startPage(pageParameter.getPage(), pageParameter.getLimit());
        return new PageInfo(industryAnalysisMapper.findTssyInfo(params));
    }


    /**
     * 按行业统计
     *
     * @param pageParameter
     * @return PageInfo
     * @Title statisticsByIndustry
     * @date 2019/7/12 14:10
     * <AUTHOR>
     */
    @Override
    public PageInfo statisticsByIndustry(PageParameter pageParameter) throws SQLException {
        HashMap params = (HashMap) pageParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.statisticsByIndustry(params));
    }

    @Override
    public PageInfo statisticsByIndustryV2(PageParameter pageParameter) throws SQLException {
        HashMap params = (HashMap) pageParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.statisticsByIndustryV2(params));
    }

    /**
     * 按登记类型统计
     *
     * @param pageParameter
     * @return PageInfo
     * @Title statisticsByRegistrationType
     * @date 2019/7/12 14:13
     * <AUTHOR>
     */
    @Override
    public PageInfo statisticsByRegistrationType(QueryBusinessStatistics pageParameter) throws SQLException {

        PageHelper.startPage(pageParameter.getPage(), pageParameter.getLimit());
        return new PageInfo(industryAnalysisMapper.statisticsByRegistrationType(pageParameter));
    }

    /**
     * 纳税净入库通用查询
     *
     * @param pageParameter
     * @return PageInfo
     * @Title warehousingGeneralQuery
     * @date 2019/7/15 11:09
     * <AUTHOR>
     */
    @Override
    public PageInfo warehousingGeneralQuery(PageParameter pageParameter) throws SQLException {
        HashMap params = (HashMap) pageParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.warehousingGeneralQuery(params));
    }

    /**
     * 分户分税种明细查询
     *
     * @param pageParameter
     * @return PageInfo
     * @Title detailsOfHouseholdTaxInquiry
     * @date 2019/7/15 11:09
     * <AUTHOR>
     */
    @Override
    public PageInfo detailsOfHouseholdTaxInquiry(PageParameter pageParameter) throws SQLException {
        HashMap params = (HashMap) pageParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.detailsOfHouseholdTaxInquiry(params));
    }


    /**
     * 税收总额排名 ----
     *
     * @param pageParameter
     * @return PageInfo
     * @Title totalTaxRanking
     * @date 2019/7/17 9:10
     * <AUTHOR>
     */
    @Override
    public PageInfo totalTaxRanking(PageParameter pageParameter) throws SQLException {
        HashMap params = (HashMap) pageParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.totalTaxRanking(params));
    }

    /**
     * 企业利润变化情况表 ----
     *
     * @param pageParameter
     * @return PageInfo
     * @Title TotalTaxRanking
     * @date 2019/7/17 9:10
     * <AUTHOR>
     */
    @Override
    public PageInfo enterpriseProfitChanges(QueryBusinessStatistics pageParameter) throws SQLException {

        PageHelper.startPage(pageParameter.getPage(), pageParameter.getLimit());
        return new PageInfo(industryAnalysisMapper.enterpriseProfitChanges(pageParameter));
    }

    /**
     * 增值税分行业情况(饼图)
     *
     * @param queryCondition
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.pie.PieChartsSeriesData>
     * @Title overallStreetConditionsChart
     * @date 2019/7/17 9:12
     * <AUTHOR>
     */
    @Override
    public PieChart VATSectorInformationEcharts(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = industryAnalysisMapper.VATSectorInformationEcharts(queryCondition);
        PieChart pieChart = new PieChart();
        pieChart.setTitle_text(queryCondition.getStart_year() + "年" + Integer.valueOf(queryCondition.getFstartmonth()) + "-" + Integer.valueOf(queryCondition.getFendmonth()) + "月增值税分行业情况分析");
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }

    /**
     * 增值税分行业情况明细(饼图)
     *
     * @param queryCondition
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.pie.PieChartsSeriesData>
     * @Title overallStreetConditionsChart
     * @date 2019/7/17 9:12
     * <AUTHOR>
     */
    @Override
    public PieChart detailsOfVATIndustryEcharts(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = industryAnalysisMapper.detailsOfVATIndustryEcharts(queryCondition);
        PieChart pieChart = new PieChart();
        pieChart.setTitle_text(queryCondition.getFhyml() + "行业情况分析");
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }

    /**
     * 增值税分行业情况明细(表格)
     *
     * @param pageParameter
     * @return PageInfo
     * @Title detailsOfVATIndustryTable
     * @date 2019/7/17 9:15
     * <AUTHOR>
     */
    @Override
    public PageInfo detailsOfVATIndustryTable(PageParameter pageParameter) throws SQLException {
        HashMap params = (HashMap) pageParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.detailsOfVATIndustryTable(params));
    }

    /**
     * 注册企业增长分析
     *
     * @param pageParameter
     * @return PageInfo
     * @Title registeredBusinessGrowthAnalysis
     * @date 2019/7/17 9:15
     * <AUTHOR>
     */
    @Override
    public PageInfo registeredBusinessGrowthAnalysis(PageParameter pageParameter) throws SQLException {
        HashMap params = (HashMap) pageParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.registeredBusinessGrowthAnalysis(params));
    }

    /**
     * 重点税源企业分析
     *
     * @param pageParameter
     * @return PageInfo
     * @Title analysisOfKeyTaxSources
     * @date 2019/7/17 9:16
     * <AUTHOR>
     */
    @Override
    public PageInfo analysisOfKeyTaxSources(QueryBusinessStatistics pageParameter) throws SQLException {

        PageHelper.startPage(pageParameter.getPage(), pageParameter.getLimit());
        return new PageInfo(industryAnalysisMapper.analysisOfKeyTaxSources(pageParameter));
    }

    /**
     * 个人所得税增收品目分析 (饼图)
     *
     * @param queryCondition
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.pie.PieChartsSeriesData>
     * @Title overallStreetConditionsChart
     * @date 2019/7/17 9:12
     * <AUTHOR>
     */
    @Override
    public PieChart analysisOfAdditionalItemsEcharts(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = industryAnalysisMapper.analysisOfAdditionalItemsEcharts(queryCondition);
        PieChart pieChart = new PieChart();
        pieChart.setTitle_text(queryCondition.getStart_year() + "年" + Integer.valueOf(queryCondition.getFstartmonth()) + "-" + Integer.valueOf(queryCondition.getFendmonth()) + "月个人所得税征收品目情况");
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }

    /**
     * 个人所得税增收品目分析明细 (饼图)
     *
     * @param queryCondition
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.pie.PieChartsSeriesData>
     * @Title analysisOfAdditionalItemsDetailEchart
     * @date 2019/7/17 9:20
     * <AUTHOR>
     */
    @Override
    public PieChart analysisOfAdditionalItemsDetailEchart(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = industryAnalysisMapper.analysisOfAdditionalItemsDetailEchart(queryCondition);
        PieChart pieChart = new PieChart();
        pieChart.setTitle_text(queryCondition.getFzspm() + "情况分析");
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }

    /**
     * 个人所得税增收品目分析(table)
     *
     * @param pageParameter
     * @return PageInfo
     * @Title analysisOfAdditionalItemsTable
     * @date 2019/7/18 11:27
     * <AUTHOR>
     */
    @Override
    public PageInfo analysisOfAdditionalItemsTable(PageParameter pageParameter) throws SQLException {
        HashMap params = (HashMap) pageParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.analysisOfAdditionalItemsTable(params));
    }

    /**
     * 企业所得税分行业(饼图)
     *
     * @param queryCondition
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.pie.PieChartsSeriesData>
     * @Title enterpriseIncomeTaxSubsectorsEchart
     * @date 2019/7/17 9:21
     * <AUTHOR>
     */
    @Override
    public PieChart enterpriseIncomeTaxSubsectorsEchart(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = industryAnalysisMapper.enterpriseIncomeTaxSubsectorsEchart(queryCondition);
        PieChart pieChart = new PieChart();
        pieChart.setTitle_text(queryCondition.getStart_year() + "年" + Integer.valueOf(queryCondition.getFstartmonth()) + "-" + Integer.valueOf(queryCondition.getFendmonth()) + "月企业所得税分行业情况");
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }

    /**
     * 企业所得税分行业明细(饼图)
     *
     * @param queryCondition
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.pie.PieChartsSeriesData>
     * @Title detailsEnterpriseIncomeTaxSubsectorsEchart
     * @date 2019/7/17 9:22
     * <AUTHOR>
     */
    @Override
    public PieChart detailsEnterpriseIncomeTaxSubsectorsEchart(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = industryAnalysisMapper.detailsEnterpriseIncomeTaxSubsectorsEchart(queryCondition);
        PieChart pieChart = new PieChart();
        pieChart.setTitle_text(queryCondition.getFhyml() + "行业情况分析");
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }

    /**
     * 企业所得税分行业明细(表格)
     *
     * @param pageParameter
     * @return PageInfo
     * @Title detailsEnterpriseIncomeTaxSubsectorsTable
     * @date 2019/7/17 9:23
     * <AUTHOR>
     */
    @Override
    public PageInfo detailsEnterpriseIncomeTaxSubsectorsTable(QueryBusinessStatistics pageParameter) throws SQLException {

        PageHelper.startPage(pageParameter.getPage(), pageParameter.getLimit());
        return new PageInfo(industryAnalysisMapper.detailsEnterpriseIncomeTaxSubsectorsTable(pageParameter));
    }

    /**
     * 企业纳税规模分析(表格)
     *
     * @param pageParameter
     * @return PageInfo
     * @Title enterpriseScaleAnalysis
     * @date 2019/7/17 9:23
     * <AUTHOR>
     */
    @Override
    public PageInfo enterpriseScaleAnalysis(PageParameter pageParameter) throws SQLException {
        HashMap params = (HashMap) pageParameter.getParams();
//        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.enterpriseScaleAnalysis(params));
    }

    @Override
    public List<HashMap<String, Object>> findCy_hyml_hydl(String fhyml) throws SQLException {
        return industryAnalysisMapper.findCy_hyml_hydl(fhyml);
    }

    @Override
    public List<HashMap<String, Object>> findZcdjlx() throws SQLException {
        return industryAnalysisMapper.findZcdjlx();
    }

    @Override
    public List<HashMap<String, Object>> findYskm() throws SQLException {
        return industryAnalysisMapper.findYskm();
    }

    @Override
    public List<HashMap<String, Object>> findFssswjg() throws SQLException {
        return industryAnalysisMapper.findFssswjg();
    }

    @Override
    public List<HashMap<String, Object>> findZsxm() throws SQLException {
        return industryAnalysisMapper.findZsxm();
    }

    /**
     * 查询所属区域条件
     *
     * @return
     */
    @Override
    public List<HashMap<String, Object>> findFssqy() throws SQLException {
        return industryAnalysisMapper.findFssqy();
    }

    /**
     * 查询所属单位
     *
     * @return
     */
    @Override
    public List<HashMap<String, Object>> findFssdw() throws SQLException {
        return industryAnalysisMapper.findFssdw();
    }

    /**
     * 查询纳税人状态条件
     *
     * @return
     */
    @Override
    public List<HashMap<String, Object>> findFnsrzt() throws SQLException {
        return industryAnalysisMapper.findFnsrzt();
    }

    @Override
    public PageInfo statisticsByEconomicType(PageParameter pageParameter) {
        HashMap params = (HashMap) pageParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.statisticsByEconomicType(params));

    }

    @Override
    public PageInfo declareDetailQuery(PageParameter pagingParameter) {
        return null;
    }

    @Override
    public List<HashMap<String, Object>> findFyskm() {
        return null;
    }

    @Override
    public PageInfo incomeByCity(PageParameter pageParameter) throws SQLException {
        HashMap params = (HashMap) pageParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.incomeByCity(params));
    }


    private List<Map<String, Object>> mapKeysToUpperCase(List<HashMap<String, Object>> mapList) {
        List<Map<String, Object>> returnList = new ArrayList<>(mapList.size());
        mapList.forEach(oneMap -> {
            Map<String, Object> returnMap = new HashMap<>(oneMap.size());
            oneMap.forEach((key, val) -> returnMap.put(key.toString().toLowerCase(), val));
            returnList.add(returnMap);
        });
        return returnList;
    }


    @Override
    public PageInfo enterpriseScaleAnalysis_2(PageParameter pageParameter) {
        HashMap params = (HashMap) pageParameter.getParams();
//        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.enterpriseScaleAnalysis2(params));
    }

    @Override
    public PageInfo enterpriseScaleAnalysisMx(PageParameter pageParameter) {
        HashMap params = (HashMap) pageParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.enterpriseScaleAnalysisMx(params));

    }


    @Override
    public PageInfo revenueByIndustryMxTable(PageParameter pageParameter) {
        HashMap params = (HashMap) pageParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.revenueByIndustryMxTable(params));
    }

    @Override
    public PageInfo revenueByAreaMxTable(PageParameter pagingParameter) {
        HashMap params = (HashMap) pagingParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.revenueByAreaMxTable(params));
    }

    @Override
    public PageInfo Sszbfxztfx_table(PageParameter pagingParameter) {
        HashMap params = (HashMap) pagingParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.Sszbfxztfx_table(params));
    }

    @Override
    public BarChart Sszbfxztfx_Chart(QueryCondition queryCondition) {
        //获取柱状图数据
        List<BarSeriesInitData> barSeriesInitData = industryAnalysisMapper.Sszbfxztfx_Chart(queryCondition);
        //柱状图数据封装
        //柱状图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        extraParameter.put("markPoint", "max");
        BarChart barChart = ChartUtil.BieCharDataAssembly(barSeriesInitData, extraParameter);
        barChart.setTitle_text("稅收完成情況近年分析");
        List series = barChart.getSeries();
        List series_new = new ArrayList();
        if (series != null) {
            for (int i = 0; i < series.size(); i++) {
                BarSeriesData barSeriesData = (BarSeriesData) series.get(i);
                String fxmmc = barSeriesData.getName();
                if (fxmmc.equals("全口径税收增速")
                        || fxmmc.equals("地方收入增速")) {
                    barSeriesData.setType("line");
                }
                series_new.add(barSeriesData);
            }
        }
        barChart.setSeries(series_new);
        return barChart;
    }


    @Override
    public BarChart Sszbfxztfxfdq_Chart(QueryCondition queryCondition) {
        //获取柱状图数据
        List<BarSeriesInitData> barSeriesInitData = industryAnalysisMapper.Sszbfxztfxfdq_Chart(queryCondition);
        //柱状图数据封装
        //柱状图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        extraParameter.put("markPoint", "max");
        BarChart barChart = ChartUtil.BieCharDataAssembly(barSeriesInitData, extraParameter);
        barChart.setTitle_text("分地区税收情况分析");
        List series = barChart.getSeries();
        List series_new = new ArrayList();
        if (series != null) {
            for (int i = 0; i < series.size(); i++) {
                BarSeriesData barSeriesData = (BarSeriesData) series.get(i);
                String fxmmc = barSeriesData.getName();
                if (fxmmc.equals("全口径税收") || fxmmc == "全口径税收") {
                    barSeriesData.setType("line");
                }
                series_new.add(barSeriesData);
            }
        }
        barChart.setSeries(series_new);
        return barChart;
    }


    @Override
    public PieChart Sszbfxztfxfsz_Chart(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = null;
        PieChart pieChart = new PieChart();
        //饼图标题
        pieChart.setTitle_text("分税种税收情况分析");
        //查询数据
        pieChartsSeriesData = industryAnalysisMapper.Sszbfxztfxfsz_Chart(queryCondition);
        //饼图数据封装
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }

    public BarChart Sszbfxztfxfhy_Chart(QueryCondition queryCondition) {
        //获取柱状图数据
        List<BarSeriesInitData> barSeriesInitData = industryAnalysisMapper.Sszbfxztfxfhy_Chart(queryCondition);
        //柱状图数据封装
        //柱状图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        extraParameter.put("markPoint", "max");
        BarChart barChart = ChartUtil.BieCharDataAssembly(barSeriesInitData, extraParameter);
        barChart.setTitle_text("分行业税收情况分析");
        List series = barChart.getSeries();
        List series_new = new ArrayList();
        if (series != null) {
            for (int i = 0; i < series.size(); i++) {
                BarSeriesData barSeriesData = (BarSeriesData) series.get(i);
                String fxmmc = barSeriesData.getName();
                if (fxmmc.equals("全口径税收增速")
                        || fxmmc.equals("地方收入增速")) {
                    barSeriesData.setType("line");
                }
                series_new.add(barSeriesData);
            }
        }
        barChart.setSeries(series_new);
        return barChart;
    }

    @Override
    public PageInfo Sszbfxxzsy_table(PageParameter pagingParameter) {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month_e = calendar.get(Calendar.MONTH);
        int month_s = 1;

        Map<String, Object> m = (Map<String, Object>) pagingParameter.getParams();
        if ("".equals(m.get("fyearzc"))) {
            m.put("fyearzc", year);
            m.put("fmonthzc_s", month_s);
            m.put("fmonthzc_e", month_e);
            pagingParameter.setParams(m);
        }
        NewTaxSource5Year source5Year = industryAnalysisMapper.Sszbfxxzsy_table(m);

        Integer thisYear = Integer.parseInt(m.get("fyearzc").toString());

if(source5Year == null ){
    return new PageInfo(new ArrayList());
}else {
    return new PageInfo(source5Year.toVoList(thisYear));
}

    }




    /**
     * 异常税源整体情况分析
     *
     * @param pagingParameter
     * @return
     */
    @Override
    public PageInfo Sszbfxycsy_table(PageParameter pagingParameter) {
        HashMap params = (HashMap) pagingParameter.getParams();
        PageHelper.startPage(Integer.valueOf((String) params.get("page")), Integer.valueOf((String) params.get("limit")));
        return new PageInfo(industryAnalysisMapper.Sszbfxycsy_table(params));
    }

    public BarChart Sszbfxztfxsysr_Chart(QueryCondition queryCondition) {
        //获取柱状图数据
        List<BarSeriesInitData> barSeriesInitData = industryAnalysisMapper.Sszbfxztfxsysr_Chart(queryCondition);
        //柱状图数据封装
        //柱状图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        extraParameter.put("markPoint", "max");
        BarChart barChart = ChartUtil.BieCharDataAssembly(barSeriesInitData, extraParameter);
        barChart.setTitle_text("近年新增税源税收分析图");
        List series = barChart.getSeries();
        List series_new = new ArrayList();
        if (series != null) {
            for (int i = 0; i < series.size(); i++) {
                BarSeriesData barSeriesData = (BarSeriesData) series.get(i);
                String fxmmc = barSeriesData.getName();
                if (fxmmc.equals("新增税源同比增长")) {
                    barSeriesData.setType("line");
                }
                series_new.add(barSeriesData);
            }
        }
        barChart.setSeries(series_new);
        return barChart;
    }

    public BarChart Sszbfxztfxsyhs_Chart(QueryCondition queryCondition) {
        //Calendar calendar=Calendar.getInstance();
        //int year = calendar.get(Calendar.YEAR);
        //int month_e = calendar.get(Calendar.MONTH);
        //int month_s = 1;
        //
        //Map<String,Object> m = (Map<String, Object>) pagingParameter.getParams();
        //if(m.get("fyearzc").equals("")){
        //    m.put("fyearzc",year);
        //    m.put("fmonthzc_s",month_s);
        //    m.put("fmonthzc_e",month_e);
        //    pagingParameter.setParams(m);
        //}
        //获取柱状图数据
        List<BarSeriesInitData> barSeriesInitData = industryAnalysisMapper.Sszbfxztfxsyhs_Chart(queryCondition);
        //柱状图数据封装
        //柱状图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        extraParameter.put("markPoint", "max");
        BarChart barChart = ChartUtil.BieCharDataAssembly(barSeriesInitData, extraParameter);
        barChart.setTitle_text("近年新增税源户数分析图");
        List series = barChart.getSeries();
        List series_new = new ArrayList();
        if (series != null) {
            for (int i = 0; i < series.size(); i++) {
                BarSeriesData barSeriesData = (BarSeriesData) series.get(i);
                String fxmmc = barSeriesData.getName();
                if (fxmmc.equals("新增税源户数同比增长")) {
                    barSeriesData.setType("line");
                }
                series_new.add(barSeriesData);
            }
        }
        barChart.setSeries(series_new);
        return barChart;
    }

    @Override
    public PieChart Sszbfxztfxsyfdq_Chart(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = null;
        PieChart pieChart = new PieChart();
        //饼图标题
        pieChart.setTitle_text("新增税源户数分地区分析图");
        //查询数据
        pieChartsSeriesData = industryAnalysisMapper.Sszbfxztfxsyfdq_Chart(queryCondition);
        //饼图数据封装
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }

    @Override
    public PieChart Sszbfxztfxsysrfdq_Chart(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = null;
        PieChart pieChart = new PieChart();
        //饼图标题
        pieChart.setTitle_text("新增税源税收分地区分析图");
        //查询数据
        pieChartsSeriesData = industryAnalysisMapper.Sszbfxztfxsysrfdq_Chart(queryCondition);
        //饼图数据封装
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }

    @Override
    public PieChart Sszbfxztfxsyfhy_Chart(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = null;
        PieChart pieChart = new PieChart();
        //饼图标题
        pieChart.setTitle_text("新增税源户数分行业分析图");
        //查询数据
        pieChartsSeriesData = industryAnalysisMapper.Sszbfxztfxsyfhy_Chart(queryCondition);
        //饼图数据封装
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }


    @Override
    public PieChart Sszbfxztfxsyfhysr_Chart(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = null;
        PieChart pieChart = new PieChart();
        //饼图标题
        pieChart.setTitle_text("新增税源税收分行业分析图");
        //查询数据
        pieChartsSeriesData = industryAnalysisMapper.Sszbfxztfxsyfhysr_Chart(queryCondition);
        //饼图数据封装
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }


    @Override
    public PieChart Sszbfxztfxsyfzclx_Chart(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = null;
        PieChart pieChart = new PieChart();
        //饼图标题
        pieChart.setTitle_text("新增税源户数分注册类型分析图");
        //查询数据
        pieChartsSeriesData = industryAnalysisMapper.Sszbfxztfxsyfzclx_Chart(queryCondition);
        //饼图数据封装
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }

    @Override
    public PieChart Sszbfxztfxsyfzclxsr_Chart(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = null;
        PieChart pieChart = new PieChart();
        //饼图标题
        pieChart.setTitle_text("新增税源税收分注册类型分析图");
        //查询数据
        pieChartsSeriesData = industryAnalysisMapper.Sszbfxztfxsyfzclxsr_Chart(queryCondition);
        //饼图数据封装
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }

    @Override
    public PageInfo subIndustryAnalysis_new_bzb(PageParameter pageParameter) throws SQLException {
        QueryCondition queryCondition = (QueryCondition) pageParameter.getParams();
        getTableNameByType(queryCondition);
        return new PageInfo(industryAnalysisMapper.subIndustryAnalysis_new_bzb(queryCondition));
    }

    QueryCondition getTableNameByType(QueryCondition queryCondition){
        String type = queryCondition.getType();
        String typeTwo = queryCondition.getTypeTwo();
        if (type.equals("行业") && typeTwo.equals("税种")){
            queryCondition.setTableName("TB_DW_ZHZS_HYSZ_GEN");
        }
        if (type.equals("税种") && typeTwo.equals("行业")){
            queryCondition.setTableName("TB_DW_ZHZS_HYSZ_GEN");
        }
        if (type.equals("行业") && typeTwo.equals("地区")){
            queryCondition.setTableName("TB_DW_ZHZS_HYDQ_GEN");
        }
        if (type.equals("地区") && typeTwo.equals("行业")){
            queryCondition.setTableName("TB_DW_ZHZS_HYDQ_GEN");
        }
        if (type.equals("地区") && typeTwo.equals("税种")){
            queryCondition.setTableName("TB_DW_ZHZS_DQSZ_GEN");
        }
        if (type.equals("税种") && typeTwo.equals("地区")){
            queryCondition.setTableName("TB_DW_ZHZS_DQSZ_GEN");
        }
        return queryCondition;
    }

}
