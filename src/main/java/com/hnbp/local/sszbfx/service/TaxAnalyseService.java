package com.hnbp.local.sszbfx.service;

import com.github.pagehelper.PageInfo;
import com.hnbp.local.sszbfx.model.QueryCondition;
import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarChart;
import com.hnbp.common.core.utils.zhzs.echarts.line.LineChart;
import com.hnbp.common.core.utils.zhzs.echarts.pie.PieChart;
import com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartTest;
import com.hnbp.common.core.utils.zhzs.paging.PageParameter;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: service
 * @date 2024-09-23
 */
public interface TaxAnalyseService {
    /**
     * 查询税收整体情况
     *
     * @return
     */
    public List<PieChartTest> queryOverallTaxPicture() throws SQLException;

    /**
     * 查询街道整体情况
     *
     * @param queryCondition
     * @return
     * @throws SQLException
     */
    public PieChart overallStreetConditionsChart(QueryCondition queryCondition) throws SQLException;

    /**
     * 查询街道详细信息
     *
     * @param queryCondition
     * @return overallStreetConditionsTable
     * @throws SQLException
     */
    public BarChart queryStreetDetails(QueryCondition queryCondition) throws SQLException;

    /**
     * 区县完成情况图
     *
     * @param queryCondition
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.BarSeriesInitData>
     * @Title districtAndCountyCompletionChart
     * @date 2019/7/5 13:41
     * <AUTHOR>
     */
    public BarChart districtAndCountyCompletionChart(QueryCondition queryCondition) throws SQLException;

    public BarChart TjdistrictAndCountyCompletionChart_bzb(QueryCondition queryCondition) throws SQLException;

    /**
     * 区县完成情况表
     *
     * @param pageParameter
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.AreaCountyTax>
     * @Title districtAndCountyCompletionTable
     * @date 2019/7/5 15:37
     * <AUTHOR>
     */
    public PageInfo districtAndCountyCompletionTable(PageParameter pageParameter) throws SQLException;

    public PageInfo TjdistrictAndCountyCompletionTable(PageParameter pageParameter) throws SQLException;

    /**
     * 收入走势明细分析
     *
     * @param queryCondition
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.BarSeriesInitData>
     * @Title districtAndCountyCompletionChart
     * @date 2019/7/5 13:41
     * <AUTHOR>
     */
    public BarChart detailedSnalysisRevenueTrend(QueryCondition queryCondition) throws SQLException;


    /**
     * 行业情况分析明细(图)
     *
     * @param queryCondition
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.AreaCountyTax>
     * @Title industryAnalysisDetails
     * @date 2019/7/5 16:11
     * <AUTHOR>
     */
    public PieChart industryAnalysisDetails(QueryCondition queryCondition) throws SQLException;
    public PieChart industryAnalysisDetailsV2(QueryCondition queryCondition) throws SQLException;


    /**
     * 地区税收情况表
     *
     * @param pageParameter
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.AreaCountyTax>
     * @Title districtAndCountyCompletionTable
     * @date 2019/7/5 15:37
     * <AUTHOR>
     */
    public PageInfo regionalTaxSituation(PageParameter pageParameter) throws SQLException;

    /**
     * 单月税收情况表   MonthlyTaxSituation
     *
     * @param pageParameter
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.AreaCountyTax>
     * @Title districtAndCountyCompletionTable
     * @date 2019/7/5 15:37
     * <AUTHOR>
     */
    public PageInfo monthlyTaxSituation(PageParameter pageParameter) throws SQLException;

    /**
     * 行业纳税人情况(表)
     *
     * @param pageParameter
     * @return PageInfo
     * @Title industryTaxpayersTable
     * @date 2019/7/11 10:44
     * <AUTHOR>
     */
    public PageInfo industryTaxpayersTable(PageParameter pageParameter) throws SQLException;

    /**
     * 指定纳税人税收查询
     *
     * @param pageParameter
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.TaxRevenue>
     * @Title zdnsrsscx
     * @date 2019/7/25 15:37
     * <AUTHOR>
     */
    public PageInfo zdnsrsscx(PageParameter pageParameter) throws SQLException;

    /**
     * 新增可查询纳税人
     *
     * @param list<>
     * @return int
     * @Title addNsr
     * @date 2019/7/25 15:37
     * <AUTHOR>
     */
    public Integer addNsr(List<Map<String, Object>> list) throws SQLException;

    /**
     * 指定纳税人税新增
     *
     * @param
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.TaxRevenue>
     * @Title addNsr
     * @date 2019/7/25 15:37
     * <AUTHOR>
     */
    public void delNsr() throws SQLException;

    /**
     * @Description: 企业异常分析
     * @DateTime: 2024/4/3
     */
    public PageInfo enterpriseAbnormalAnalysis(PageParameter pageParameter) throws SQLException;

}
