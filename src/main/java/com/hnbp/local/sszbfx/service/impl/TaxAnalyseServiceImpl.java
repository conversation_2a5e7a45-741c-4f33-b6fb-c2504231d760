package com.hnbp.local.sszbfx.service.impl;

import cn.hutool.core.util.IdUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hnbp.local.sszbfx.model.QueryCondition;
import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarChart;
import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData;
import com.hnbp.common.core.utils.zhzs.echarts.ChartUtil;
import com.hnbp.common.core.utils.zhzs.echarts.line.LineChart;
import com.hnbp.common.core.utils.zhzs.echarts.line.LineSeriesInitData;
import com.hnbp.common.core.utils.zhzs.echarts.pie.PieChart;
import com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartTest;
import com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData;
import com.hnbp.common.core.utils.zhzs.paging.PageParameter;
import com.hnbp.local.sszbfx.mapper.TaxAnalyseMapper;
import com.hnbp.local.sszbfx.service.TaxAnalyseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ━━━━━━神兽保佑━━━━━━
 * 　　　┏┓　　　┏┓
 * 　　┏┛┻━━━┛┻┓
 * 　　┃　　　　　　　┃
 * 　　┃　　　━　　　┃
 * 　　┃　┳┛　┗┳　┃
 * 　　┃　　　　　　　┃
 * 　　┃　　　┻　　　┃
 * 　　┃　　　　　　　┃
 * 　　┗━┓　　　┏━┛
 * 　　　　┃　　　┃
 * 　　　　┃　　　┃
 * 　　　　┃　　　┗━━━┓
 * 　　　　┃　　　　　　　┣┓
 * 　　　　┃　　　　　　　┏┛
 * 　　　　┗┓┓┏━┳┓┏┛
 * 　　　　　┃┫┫　┃┫┫
 * 　　　　　┗┻┛　┗┻┛
 * ━━━━━━永无BUG━━━━━━
 *
 * <AUTHOR>
 * @date 2019/7/2 19:25
 * @Title
 */
@Service
public class TaxAnalyseServiceImpl implements TaxAnalyseService {
    @Autowired
    private TaxAnalyseMapper taxAnalyseMapper;

    /**
     * 查询税收整体情况
     *
     * @return
     */
    @Override
    public List<PieChartTest> queryOverallTaxPicture() throws SQLException {
        return taxAnalyseMapper.queryOverallTaxPicture();
    }

    /**
     * 查询街道整体情况
     *
     * @param queryCondition
     * @return
     * @throws SQLException
     */
    @Override
    public PieChart overallStreetConditionsChart(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = taxAnalyseMapper.overallStreetConditionsChart(queryCondition);
        PieChart pieChart = new PieChart();
        pieChart.setTitle_text(queryCondition.getStart_year() + "年" + Integer.valueOf(queryCondition.getFstartmonth()) + "-" + Integer.valueOf(queryCondition.getFendmonth()) + "月地区税收分析");
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }

    /**
     * 查询街道详细信息
     *
     * @param queryCondition
     * @return overallStreetConditionsTable
     * @throws SQLException
     */
    @Override
    public BarChart queryStreetDetails(QueryCondition queryCondition) throws SQLException {
        //获取柱状图数据
        List<BarSeriesInitData> barSeriesInitData = taxAnalyseMapper.queryStreetDetails(queryCondition);
        //柱状图数据封装
        BarChart barChart = ChartUtil.BieCharDataAssembly(barSeriesInitData, new HashMap());
        barChart.setTitle_text(queryCondition.getFssqy() + "详细情况");
        return barChart;
    }


    @Override
    public BarChart districtAndCountyCompletionChart(QueryCondition queryCondition) throws SQLException {
        //获取柱状图数据
        List<BarSeriesInitData> barSeriesInitData = taxAnalyseMapper.districtAndCountyCompletionChart(queryCondition);
        //柱状图数据封装
        //柱状图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        extraParameter.put("markPoint", "max");
        BarChart barChart = ChartUtil.BieCharDataAssembly(barSeriesInitData, extraParameter);
        barChart.setTitle_text("近三年" +
                (queryCondition.getFstartmonth().indexOf("0") == 0 ? queryCondition.getFstartmonth().replace("0", "") : queryCondition.getFstartmonth())
                + "-" +
                (queryCondition.getFendmonth().indexOf("0") == 0 ? queryCondition.getFendmonth().replace("0", "") : queryCondition.getFendmonth())
                + "月地区完成情况图");
        return barChart;
    }

    public BarChart TjdistrictAndCountyCompletionChart_bzb(QueryCondition queryCondition) throws SQLException {
        //获取柱状图数据
        List<BarSeriesInitData> barSeriesInitData = taxAnalyseMapper.TjdistrictAndCountyCompletionChart(queryCondition);
        //柱状图数据封装
        //柱状图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        extraParameter.put("markPoint", "max");
        BarChart barChart = ChartUtil.BieCharDataAssembly(barSeriesInitData, extraParameter);
        barChart.setTitle_text("近三年" +
                (queryCondition.getFstartmonth().indexOf("0") == 0 ? queryCondition.getFstartmonth().replace("0", "") : queryCondition.getFstartmonth())
                + "-" +
                (queryCondition.getFendmonth().indexOf("0") == 0 ? queryCondition.getFendmonth().replace("0", "") : queryCondition.getFendmonth())
                + "月地区完成情况图");
        return barChart;
    }


    @Override
    public PageInfo districtAndCountyCompletionTable(PageParameter pageParameter) throws SQLException {
        QueryCondition queryCondition = (QueryCondition) pageParameter.getParams();
        PageHelper.startPage(queryCondition.getPage(), queryCondition.getLimit());
        return new PageInfo<>(taxAnalyseMapper.districtAndCountyCompletionTable(queryCondition));
    }

    @Override
    public PageInfo TjdistrictAndCountyCompletionTable(PageParameter pageParameter) throws SQLException {
        QueryCondition queryCondition = (QueryCondition) pageParameter.getParams();
        PageHelper.startPage(queryCondition.getPage(), queryCondition.getLimit());
        return new PageInfo<>(taxAnalyseMapper.TjdistrictAndCountyCompletionTable(queryCondition));
    }

    /**
     * 收入走势明细分析
     *
     * @param queryCondition
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.BarSeriesInitData>
     * @Title districtAndCountyCompletionChart
     * @date 2019/7/5 13:41
     * <AUTHOR>
     */
    @Override
    public BarChart detailedSnalysisRevenueTrend(QueryCondition queryCondition) throws SQLException {
        //获取柱状图数据
        List<BarSeriesInitData> barSeriesInitData = null;
        //柱状图数据封装
        BarChart barChart = null;
        if ("01".equals(queryCondition.getType())) {//税收明细
            barSeriesInitData = taxAnalyseMapper.theTaxSubsidiary(queryCondition);
            //柱状图额外要求markPoint
            HashMap extraParameter = new HashMap<>();
            barChart = ChartUtil.BieCharDataAssembly(barSeriesInitData, extraParameter);
            barChart.setTitle_text(queryCondition.getFzsxm() + "历年变化情况");
        } else if ("02".equals(queryCondition.getType())) {//区县明细
            barSeriesInitData = taxAnalyseMapper.areaCountyDetail(queryCondition);
            //柱状图额外要求markPoint
            HashMap extraParameter = new HashMap<>();
            barChart = ChartUtil.BieCharDataAssembly(barSeriesInitData, extraParameter);
            barChart.setTitle_text(queryCondition.getFssqy() + "历年变化情况");
        } else if ("03".equals(queryCondition.getType())) {//按产业明细
            barSeriesInitData = taxAnalyseMapper.theIndustryOfSubsidiary(queryCondition);
            //柱状图额外要求markPoint
            HashMap extraParameter = new HashMap<>();
            barChart = ChartUtil.BieCharDataAssembly(barSeriesInitData, extraParameter);
            barChart.setTitle_text(queryCondition.getFcymc() + "历年变化情况");
        } else {
            throw new ArithmeticException("未携带参数类型,请求出错");
        }
        return barChart;
    }


    /**
     * 行业情况分析明细(图)
     *
     * @param queryCondition
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.AreaCountyTax>
     * @Title industryAnalysisDetails
     * @date 2019/7/5 16:11
     * <AUTHOR>
     */
    @Override
    public PieChart industryAnalysisDetails(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = null;
        PieChart pieChart = new PieChart();
        //饼图标题
        pieChart.setTitle_text(queryCondition.getFhyml() + "行业情况分析");
        //查询数据
        pieChartsSeriesData = taxAnalyseMapper.industryAnalysisDetails(queryCondition);
        //饼图数据封装
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }

    @Override
    public PieChart industryAnalysisDetailsV2(QueryCondition queryCondition) throws SQLException {
        List<PieChartsSeriesData> pieChartsSeriesData = null;
        PieChart pieChart = new PieChart();
        //饼图标题
        pieChart.setTitle_text(queryCondition.getFhyml() + "行业情况分析");
        //查询数据
        pieChartsSeriesData = taxAnalyseMapper.industryAnalysisDetailsV2(queryCondition);
        //饼图数据封装
        pieChart.setLegend_data(ChartUtil.pieLegendDataAssembly(pieChartsSeriesData));
        pieChart.setSeries_data(pieChartsSeriesData);
        return pieChart;
    }


    /**
     * 地区税收情况表
     *
     * @param pageParameter
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.AreaCountyTax>
     * @Title regionalTaxSituation
     * @date 2019/7/5 15:37
     * <AUTHOR>
     */
    @Override
    public PageInfo regionalTaxSituation(PageParameter pageParameter) throws SQLException {
        QueryCondition queryCondition = (QueryCondition) pageParameter.getParams();
        PageHelper.startPage(queryCondition.getPage(), queryCondition.getLimit());
        return new PageInfo(taxAnalyseMapper.regionalTaxSituation(pageParameter));
    }

    /**
     * 单月税收情况表   MonthlyTaxSituation
     *
     * @param pageParameter
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.AreaCountyTax>
     * @Title districtAndCountyCompletionTable
     * @date 2019/7/5 15:37
     * <AUTHOR>
     */
    @Override
    public PageInfo monthlyTaxSituation(PageParameter pageParameter) throws SQLException {
        QueryCondition queryCondition = (QueryCondition) pageParameter.getParams();
        PageHelper.startPage(queryCondition.getPage(), queryCondition.getLimit());
        return new PageInfo(taxAnalyseMapper.monthlyTaxSituation(pageParameter));
    }

    /**
     * 行业纳税人情况(表)
     *
     * @param pageParameter
     * @return PageInfo
     * @Title industryTaxpayersTable
     * @date 2019/7/11 10:44
     * <AUTHOR>
     */
    @Override
    public PageInfo industryTaxpayersTable(PageParameter pageParameter) throws SQLException {
        QueryCondition queryCondition = (QueryCondition) pageParameter.getParams();
        PageHelper.startPage(queryCondition.getPage(), queryCondition.getLimit());
        return new PageInfo(taxAnalyseMapper.industryTaxpayersTable(queryCondition));
    }

    /**
     * 指定纳税人税收查询
     *
     * @param pageParameter
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.TaxRevenue>
     * @Title zdnsrsscx
     * @date 2019/7/25 15:37
     * <AUTHOR>
     */
    @Override
    public PageInfo zdnsrsscx(PageParameter pageParameter) throws SQLException {
        // TODO Auto-generated method stub
        QueryCondition queryCondition = (QueryCondition) pageParameter.getParams();
        PageHelper.startPage(queryCondition.getPage(), queryCondition.getLimit());
        return new PageInfo(taxAnalyseMapper.zdnsrsscx(pageParameter));
    }

    /**
     * 指定纳税人税新增
     *
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.TaxRevenue>
     * @Title addNsr
     * @date 2019/7/25 15:37
     * <AUTHOR>
     */
    @Override
    public Integer addNsr(List<Map<String, Object>> list) throws SQLException {
        // TODO Auto-generated method stub
        int k = 0;
        for (Map<String, Object> m : list) {
            m.put("fid", IdUtil.getSnowflakeNextIdStr());
            if (StringUtils.isEmpty(m.get("fnsrsbh"))){
                m.put("fnsrsbh", null);
            }
            if (StringUtils.isEmpty(m.get("fnsrmc"))){
                m.put("fnsrmc", null);
            }
            taxAnalyseMapper.addNsr((HashMap<String, Object>) m);
        }
        return k;
    }

    /**
     * 指定纳税人税新增
     *
     * @param
     * @return java.util.List<com.hnbp.jagl.bzb.srfx.model.TaxRevenue>
     * @Title addNsr
     * @date 2019/7/25 15:37
     * <AUTHOR>
     */
    @Override
    public void delNsr() throws SQLException {
        taxAnalyseMapper.delNsr();
    }

    /**
     * @Description: 企业异常分析
     * @DateTime: 2024/4/3
     */
    @Override
    public PageInfo enterpriseAbnormalAnalysis(PageParameter pageParameter) throws SQLException {
        QueryCondition queryCondition = (QueryCondition) pageParameter.getParams();
        PageHelper.startPage(queryCondition.getPage(), queryCondition.getLimit());
        return new PageInfo(taxAnalyseMapper.enterpriseAbnormalAnalysis(pageParameter));
    }
}
