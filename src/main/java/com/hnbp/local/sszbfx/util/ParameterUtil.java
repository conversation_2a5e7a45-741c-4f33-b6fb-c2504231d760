package com.hnbp.local.sszbfx.util;

import com.hnbp.local.sszbfx.model.QueryCondition;
import com.hnbp.common.core.utils.zhzs.paging.PageParameter;
import com.hnbp.common.core.utils.zhzs.util.CommonUtil;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * ━━━━━━神兽保佑━━━━━━
 * 　　　┏┓　　　┏┓
 * 　　┏┛┻━━━┛┻┓
 * 　　┃　　　　　　　┃
 * 　　┃　　　━　　　┃
 * 　　┃　┳┛　┗┳　┃
 * 　　┃　　　　　　　┃
 * 　　┃　　　┻　　　┃
 * 　　┃　　　　　　　┃
 * 　　┗━┓　　　┏━┛
 * 　　　　┃　　　┃
 * 　　　　┃　　　┃
 * 　　　　┃　　　┗━━━┓
 * 　　　　┃　　　　　　　┣┓
 * 　　　　┃　　　　　　　┏┛
 * 　　　　┗┓┓┏━┳┓┏┛
 * 　　　　　┃┫┫　┃┫┫
 * 　　　　　┗┻┛　┗┻┛
 * ━━━━━━永无BUG━━━━━━
 *
 * <AUTHOR>
 * @date 2019/7/9 15:46
 * @Title 参数处理
 */
public class ParameterUtil {

    /**
     * 由于有时需要使用 ibatis 的 iterate 标签实现 MySQL 的行转列等功能，
     * 不能创建合适的实体类作为 DAO 层的参数，故使用 Map 传参。
     *
     * @param params
     * @return
     * @throws UnsupportedEncodingException
     */
    public static Map<String, Object> handleRequestParam(Map<String, Object> params) throws UnsupportedEncodingException {
        Map<String, Object> map = params;
        if (map.keySet().size() == 1 && map.get("data") != null) {
            map.put("data", handleRequestParam((Map<String, Object>) map.get("data")));
        }
        // 所属区域
        Optional.ofNullable((String) map.get("fssqy"))
                .filter(param -> !Objects.equals(param, ""))
                .map(param -> param.split(","))
                .map(Arrays::asList)
                .ifPresent(ssqyList -> map.put("fssqyList", ssqyList));

        // 行业大类
        Optional.ofNullable((String) map.get("fhydl"))
                .filter(param -> !Objects.equals(param, ""))
                .map(param -> param.split(","))
                .map(Arrays::asList)
                .ifPresent(hydlList -> map.put("fhydlList", hydlList));

        // 入库日期起止
        Optional.ofNullable((String) map.get("frkrq"))
                .filter(param -> !Objects.equals(param, ""))
                .map(param -> param.split("~"))
                .filter(paramArray -> paramArray.length > 0)
                .ifPresent(rkrqStartEndArray -> {
                    map.put("fStartRkrq", rkrqStartEndArray[0].trim());
                    map.put("fEndRkrq", rkrqStartEndArray[1].trim());
                    map.put("fStartRkrqLastYear", getLastYear(rkrqStartEndArray[0].trim()));
                    map.put("fEndRkrqLastYear", getLastYear(rkrqStartEndArray[1].trim()));
                });
        //税款所属期起止
        Optional.ofNullable((String) map.get("fskssqq"))
                .filter(param -> !Objects.equals(param, ""))
                .map(param -> param.split("~"))
                .filter(paramArray -> paramArray.length > 0)
                .ifPresent(skssqqStartEndArray -> {
                    map.put("fStartSkssqq", skssqqStartEndArray[0].trim());
                    map.put("fEndSkssqq", skssqqStartEndArray[1].trim());
                    map.put("fStartSkssqqLastYear", getLastYear(skssqqStartEndArray[0].trim()));
                    map.put("fEndSkssqqLastYear", getLastYear(skssqqStartEndArray[1].trim()));
                });
        // 征收项目
        Optional.ofNullable((String) map.get("fzsxm"))
                .filter(param -> !Objects.equals(param, ""))
                .map(param -> param.split(","))
                .map(Arrays::asList)
                .ifPresent(zsxmList -> map.put("fzsxmList", zsxmList));
        // 登记日期
        Optional.ofNullable((String) map.get("fdjrqqz"))
                .filter(param -> !Objects.equals(param, ""))
                .map(param -> param.split("~"))
                .filter(paramArray -> paramArray.length > 0)
                .ifPresent(djrqStartEndArray -> {
                    map.put("fStartDjrq", djrqStartEndArray[0].trim());
                    map.put("fEndDjrq", djrqStartEndArray[1].trim());
                });

        //注册登记类型
        Optional.ofNullable((String) map.get("fzcdjlx"))
                .filter(param -> !Objects.equals(param, ""))
                .ifPresent(fzcdjlx -> {
                    map.put("fzcdjlx", fzcdjlx);
                    map.put("fzcdjlxStr", CommonUtil.stringEncapsulation(fzcdjlx));
                });

        // 拼接入库日期
        Optional.ofNullable(map.get("start_year"))
                .ifPresent(param -> {
                    String[] rkrqStartEndArray = (String.valueOf(param)).split(" ~ ");
                    if (rkrqStartEndArray.length > 1) {
                        String frk_year = rkrqStartEndArray[0].substring(0, 4);
                        String frk_startMonth = rkrqStartEndArray[0].substring(5);
                        String frk_endMonth = rkrqStartEndArray[1].substring(5);
                        map.put("frk_year", frk_year);
                        map.put("frk_startMonth", frk_startMonth);
                        map.put("frk_endMonth", frk_endMonth);
                    }
                });
        return map;
    }

    /**
     * 由于无法确定前端传参的日期格式，只能用 String 做计算
     *
     * @param dataStr
     * @return
     */
    private static String getLastYear(String dataStr) {
        return Optional.ofNullable(dataStr)
                .filter(param -> !Objects.equals(param, ""))
                .map(param -> {
                    String yearStr = param.substring(0, 4);
                    int year = Integer.parseInt(yearStr);
                    int lastYear = year - 1;
                    return lastYear + param.substring(4);
                })
                .orElse(null);
    }

    public static PageParameter pageParameter(Integer page, Integer size, Object param) {
        PageParameter pageParameter = new PageParameter();
        pageParameter.setPage(page);
        pageParameter.setLimit(size);
        pageParameter.setParams(param);
        return pageParameter;

    }

    /**
     * 分页参数处理
     *
     * @param paremeter
     * @return PageParameter
     * @Title pagingParameter
     * @date 2019/7/9 15:51
     * <AUTHOR>
     */
    public static PageParameter pagingParameter(Map<String, Object> paremeter) {
        String userid = (String) paremeter.get("userId");
        PageParameter pageParameter = new PageParameter();
        //数量
        if (paremeter.containsKey("limit")) {
            String limit = String.valueOf(paremeter.get("limit"));
            if (!StringUtils.isEmpty(limit)) {
                pageParameter.setLimit(Integer.parseInt(limit));
            }
        }
        //页码
        if (paremeter.containsKey("page")) {
            String page = String.valueOf(paremeter.get("page"));
            if (!StringUtils.isEmpty(page)) {
                pageParameter.setPage(Integer.parseInt(page));
            }
        }
        //是否需要分页
        if (paremeter.containsKey("isPaging")) {
            String isPaging = String.valueOf(paremeter.get("isPaging"));
            if (!StringUtils.isEmpty(isPaging)) {
                pageParameter.setPaging(Boolean.parseBoolean(isPaging));
            }
        }
        //执行入口
        if (paremeter.containsKey("entrance")) { //IncomeAnalysis 业务统计
            String entrance = paremeter.get("entrance").toString();
            /*case "IncomeAnalysis":
                    QueryBusinessStatistics queryBusinessStatistics = null;
                    try {
                        queryBusinessStatistics = queryBusinessStatisticsParameterProcessing(paremeter);
                        queryBusinessStatistics.setUserid_skgk(userid);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    pageParameter.setParams(queryBusinessStatistics);
                    break;
                case "StandingBookAnalyze":
                    StandingBookParameter standingBookParameter = queryStandingBookParameter(paremeter);
                    pageParameter.setParams(standingBookParameter);
                    break;
                case "Warninganalysis":
                    QueryWarninganalysis queryWarninganalysis = queryWarninganalysisParameter(paremeter);
                    pageParameter.setParams(queryWarninganalysis);
                    break;
                case "IncomeAndExpend":
                    QueryIncomeAndExpend incomeAndExpend = queryIncomeAndExpendParameter(paremeter);
                    pageParameter.setParams(incomeAndExpend);
                    break;

                case "UserHabitInfo":
                    UserHabitQuery userHabitQuery = queryUserHabitInfoParameter(paremeter);
                    pageParameter.setParams(userHabitQuery);
                    break;*/
            if ("Map".equals(entrance)) {
                paremeter.put("userid_skgk", userid);
                if (paremeter.containsKey("fssqy")) {
                    String fssqy = String.valueOf(paremeter.get("fssqy"));
                    if (!StringUtils.isEmpty(fssqy)) {
                        paremeter.put("fssqy", fssqy);
                        paremeter.put("fssqyStr", CommonUtil.stringEncapsulation(fssqy));
                    }
                }

                if (paremeter.containsKey("fzsxm")) {
                    String fzsxm = String.valueOf(paremeter.get("fzsxm"));
                    if (!StringUtils.isEmpty(fzsxm)) {
                        paremeter.put("fzsxm", fzsxm);
                        paremeter.put("fzsxmStr", CommonUtil.stringEncapsulation(fzsxm));
                    }
                }

                if (paremeter.containsKey("fsz")) {
                    String fzsxm = String.valueOf(paremeter.get("fsz"));
                    if (!StringUtils.isEmpty(fzsxm)) {
                        paremeter.put("fzsxm", fzsxm);
                        paremeter.put("fzsxmStr", CommonUtil.stringEncapsulation(fzsxm));
                    }
                }

                if (paremeter.containsKey("fhydl")) {
                    String fhydl = String.valueOf(paremeter.get("fhydl"));
                    if (!StringUtils.isEmpty(fhydl)) {
                        paremeter.put("fhydl", fhydl);
                        paremeter.put("fhydlStr", CommonUtil.stringEncapsulation(fhydl));
                    }
                }

                if (paremeter.containsKey("fhyml")) {
                    String fhyml = String.valueOf(paremeter.get("fhyml"));
                    if (!StringUtils.isEmpty(fhyml)) {
                        paremeter.put("fhyml", fhyml);
                        paremeter.put("fhymlStr", CommonUtil.stringEncapsulation(fhyml));
                    }
                }

                pageParameter.setParams(paremeter);
            }
        } else {
            //查询参数处理 (可封装其他类型)
            QueryCondition queryCondition = queryParameterProcessing(paremeter);
            queryCondition.setUserid_skgk(userid);
            pageParameter.setParams(queryCondition);
        }

        return pageParameter;
    }

    /**
     * 处理用户习惯查询条件
     *
     * @param paremeter
     * @return
     */
    /*private static UserHabitQuery queryUserHabitInfoParameter(Map paremeter) {
        UserHabitQuery userHabitQuery = new UserHabitQuery();

        //fid
        if (paremeter.containsKey("fid")) {
            String fid = String.valueOf(paremeter.get("fid"));
            if (!StringUtils.isEmpty(fid)) {
                userHabitQuery.setFid(fid);
            }
        }

        //用户习惯id
        if (paremeter.containsKey("furlId")) {
            String furlid = String.valueOf(paremeter.get("furlId"));
            if (!StringUtils.isEmpty(furlid)) {
                userHabitQuery.setFurlId(furlid);
            }
        }

        //查询条件
        if (paremeter.containsKey("fcxtj")) {
            String fcxtj = String.valueOf(paremeter.get("fcxtj"));
            if (!StringUtils.isEmpty(fcxtj)) {
                userHabitQuery.setFcxtjStr(fcxtj);
            }
        }

        //表格信息
        if (paremeter.containsKey("ftable")) {
            String ftable = String.valueOf(paremeter.get("ftable"));
            if (!StringUtils.isEmpty(ftable)) {
                userHabitQuery.setFtableStr(ftable);
            }
        }

        //图形信息
        if (paremeter.containsKey("fecharts")) {
            String fecharts = String.valueOf(paremeter.get("fecharts"));
            if (!StringUtils.isEmpty(fecharts)) {
                userHabitQuery.setFechartsStr(fecharts);
            }
        }

        //图形信息
        if (paremeter.containsKey("fmenuName")) {
            String fmenuname = String.valueOf(paremeter.get("fmenuName"));
            if (!StringUtils.isEmpty(fmenuname)) {
                userHabitQuery.setFmenuName(fmenuname);
            }
        }

        //是否应用
        if (paremeter.containsKey("fisuse")) {
            String fisuse = String.valueOf(paremeter.get("fisuse"));
            if (!StringUtils.isEmpty(fisuse)) {
                userHabitQuery.setFisuse(fisuse);
            }
        }

        //备注
        if (paremeter.containsKey("fbz")) {
            String fbz = String.valueOf(paremeter.get("fbz"));
            if (!StringUtils.isEmpty(fbz)) {
                userHabitQuery.setFbz(fbz);
            }
        }

        //类型
        if (paremeter.containsKey("ftype")) {
            String ftype = String.valueOf(paremeter.get("ftype"));
            if (!StringUtils.isEmpty(ftype)) {
                userHabitQuery.setFtype(ftype);
            }
        }

        return userHabitQuery;
    }*/

    /*public static QueryIncomeAndExpend queryIncomeAndExpendParameter(Map paremeter) {
        QueryIncomeAndExpend queryIncomeAndExpend = new QueryIncomeAndExpend();

        //查询年月
        if (paremeter.containsKey("fssny")) {
            String fssny = paremeter.get("fssny").toString();
            if (!StringUtils.isEmpty(fssny)) {
                queryIncomeAndExpend.setFssny(fssny);
                queryIncomeAndExpend.setFyear(fssny.split("-")[0]);
                queryIncomeAndExpend.setFmonth(fssny.split("-")[1]);
            }
        }

        //所属区域
        if (paremeter.containsKey("fssqy")) {
            String fssqy = String.valueOf(paremeter.get("fssqy"));
            if (!StringUtils.isEmpty(fssqy)) {
                queryIncomeAndExpend.setFssqy(fssqy);
                queryIncomeAndExpend.setFssqyStr(CommonUtil.stringEncapsulation(fssqy));

            }
        }

        //科目名称
        if (paremeter.containsKey("fkmmc")) {
            String fkmmc = String.valueOf(paremeter.get("fkmmc"));
            if (!StringUtils.isEmpty(fkmmc)) {
                queryIncomeAndExpend.setFkmmc(fkmmc);
            }
        }

        //科目编码
        if (paremeter.containsKey("fkmbm")) {
            String fkmbm = String.valueOf(paremeter.get("fkmbm"));
            if (!StringUtils.isEmpty(fkmbm)) {
                queryIncomeAndExpend.setFkmbm(fkmbm);
            }
        }

        //纳税人名称
        if (paremeter.containsKey("fnsrmc")) {
            String fnsrmc = String.valueOf(paremeter.get("fnsrmc"));
            if (!StringUtils.isEmpty(fnsrmc)) {
                queryIncomeAndExpend.setFnsrmc(fnsrmc);
            }
        }


        return queryIncomeAndExpend;
    }*/

    /*private static QueryWarninganalysis queryWarninganalysisParameter(Map paremeter) {

        QueryWarninganalysis queryWarninganalysis = new QueryWarninganalysis();

        //开始月
        if (paremeter.containsKey("page")) {
            Integer page = Integer.valueOf((String) paremeter.get("page"));
            if (!StringUtils.isEmpty(page)) {
                queryWarninganalysis.setPage(page);
            }
        }

        //开始月
        if (paremeter.containsKey("limit")) {
            Integer limit = Integer.valueOf((String) paremeter.get("limit"));
            if (!StringUtils.isEmpty(limit)) {
                queryWarninganalysis.setLimit(limit);
            }
        }


        //开始年份
        if (paremeter.containsKey("fyear")) {
            String fyear = paremeter.get("fyear").toString();
            if (!StringUtils.isEmpty(fyear)) {
                int year = Integer.parseInt(fyear);
                queryWarninganalysis.setFyear(String.valueOf(year));
                queryWarninganalysis.setFlastyear(String.valueOf(year - 1));
            }
        }

        //结束年份
        if (paremeter.containsKey("fyear_e")) {
            String fyear_e = paremeter.get("fyear_e").toString();
            if (!StringUtils.isEmpty(fyear_e)) {
                int year = Integer.parseInt(fyear_e);
                queryWarninganalysis.setFyear_e(String.valueOf(year));
                //queryWarninganalysis.setEndyear(year - 2 + "");
            }
        }

        //开始月
        if (paremeter.containsKey("fmonth_s")) {
            String fmonth_s = String.valueOf(paremeter.get("fmonth_s"));
            if (!StringUtils.isEmpty(fmonth_s)) {
                queryWarninganalysis.setFmonth_s(fmonth_s);
            }
        }
        //结束月
        if (paremeter.containsKey("fmonth_e")) {
            String fmonth_e = String.valueOf(paremeter.get("fmonth_e"));
            if (!StringUtils.isEmpty(fmonth_e)) {
                queryWarninganalysis.setFmonth_e(fmonth_e);
            }
        }

        //纳税人名称
        if (paremeter.containsKey("tdcryj")) {
            String tdcryj = String.valueOf(paremeter.get("tdcryj"));
            if (!StringUtils.isEmpty(tdcryj)) {
                queryWarninganalysis.setTdcryj(tdcryj);
            }
        }

        //土地出让预警查询条件
        if (paremeter.containsKey("fnsrmc")) {
            String fnsrmc = String.valueOf(paremeter.get("fnsrmc"));
            if (!StringUtils.isEmpty(fnsrmc)) {
                queryWarninganalysis.setFnsrmc(fnsrmc);
            }
        }

        //状态
        if (paremeter.containsKey("ffhzt")) {
            String ffhzt = String.valueOf(paremeter.get("ffhzt"));
            if (!StringUtils.isEmpty(ffhzt)) {
                queryWarninganalysis.setFfhzt(ffhzt);
            }
        }
        //data
        if (paremeter.containsKey("fdata")) {
            String fdata = String.valueOf(paremeter.get("fdata"));
            if (!StringUtils.isEmpty(fdata)) {
                queryWarninganalysis.setFdata(fdata);
            }
        }

        //纳税人识别号
        if (paremeter.containsKey("fshxydm")) {
            String fshxydm = String.valueOf(paremeter.get("fshxydm"));
            if (!StringUtils.isEmpty(fshxydm)) {
                queryWarninganalysis.setFshxydm(fshxydm);
            }
        }

        if (paremeter.containsKey("fzspm")) {
            String fzspm = String.valueOf(paremeter.get("fzspm"));
            if (!StringUtils.isEmpty(fzspm)) {
                queryWarninganalysis.setFzspm(fzspm);
            }
        }

        //所属区域
        if (paremeter.containsKey("fssqy")) {
            String fssqy = String.valueOf(paremeter.get("fssqy"));
            if (!StringUtils.isEmpty(fssqy)) {
                queryWarninganalysis.setFssqy(fssqy);
                queryWarninganalysis.setFssqyStr(CommonUtil.stringEncapsulation(fssqy));

            }
        }

        //行业
        if (paremeter.containsKey("fhyml")) {
            String fhyml = String.valueOf(paremeter.get("fhyml"));
            if (!StringUtils.isEmpty(fhyml)) {
                queryWarninganalysis.setFhyml(fhyml);
            }
        }

        //偏离值
        if (paremeter.containsKey("plz")) {
            String fplz = String.valueOf(paremeter.get("plz"));
            if (!StringUtils.isEmpty(fplz) && !"无".equals(fplz)) {
                queryWarninganalysis.setFplz(fplz);
            }
        }

        //均价
        if (paremeter.containsKey("faverage")) {
            String faverage = String.valueOf(paremeter.get("faverage"));
            if (!StringUtils.isEmpty(faverage)) {
                queryWarninganalysis.setFaverage(faverage);
            }
        }

        //是否税务注册
        if (paremeter.containsKey("fswzc")) {
            String fswzc = String.valueOf(paremeter.get("fswzc"));
            if (!StringUtils.isEmpty(fswzc)) {
                queryWarninganalysis.setFswzc(fswzc);
            }
        }

        //适用税额
        if (paremeter.containsKey("fsysl")) {
            String fsysl = String.valueOf(paremeter.get("fsysl"));
            if (!StringUtils.isEmpty(fsysl)) {
                queryWarninganalysis.setFsysl(fsysl);
            }
        }

        //预警方式
        if (paremeter.containsKey("fyjfs")) {
            String fyjfs = String.valueOf(paremeter.get("fyjfs"));
            if (!StringUtils.isEmpty(fyjfs)) {
                queryWarninganalysis.setFyjfs(fyjfs);
            }
        }

        //税款所属期起
        if (paremeter.containsKey("fskssqq_s")) {
            String fskssqq_s = String.valueOf(paremeter.get("fskssqq_s"));
            if (!StringUtils.isEmpty(fskssqq_s)) {
                queryWarninganalysis.setFskssqq_s(fskssqq_s);
            }
        }

        //税款所属期止
        if (paremeter.containsKey("fskssqq_e")) {
            String fskssqq_e = String.valueOf(paremeter.get("fskssqq_e"));
            if (!StringUtils.isEmpty(fskssqq_e)) {
                queryWarninganalysis.setFskssqq_e(fskssqq_e);
            }
        }

        //入库日期起
        if (paremeter.containsKey("frkrq_s")) {
            String frkrq_s = String.valueOf(paremeter.get("frkrq_s"));
            if (!StringUtils.isEmpty(frkrq_s)) {
                queryWarninganalysis.setFrkrq_s(frkrq_s);
            }
        }
        if (paremeter.containsKey("fzsxm")) {
            String fzsxm = String.valueOf(paremeter.get("fzsxm"));
            if (!StringUtils.isEmpty(fzsxm)) {
                queryWarninganalysis.setFzsxm(fzsxm);
                queryWarninganalysis.setFzsxmStr(CommonUtil.stringEncapsulation(fzsxm));
            }
        }
        //入库日期止
        if (paremeter.containsKey("frkrq_e")) {
            String frkrq_e = String.valueOf(paremeter.get("frkrq_e"));
            if (!StringUtils.isEmpty(frkrq_e)) {
                queryWarninganalysis.setFrkrq_e(frkrq_e);
            }
        }

        //自定义税率
        if (paremeter.containsKey("fzdysl")) {
            String fzdysl = String.valueOf(paremeter.get("fzdysl"));
            if (!StringUtils.isEmpty(fzdysl)) {
                queryWarninganalysis.setFzdysl(fzdysl);
            }
        }

        //自定义税率
        if (paremeter.containsKey("fzdyslxgm")) {
            String fzdyslxgm = String.valueOf(paremeter.get("fzdyslxgm"));
            if (!StringUtils.isEmpty(fzdyslxgm)) {
                queryWarninganalysis.setFzdyslxgm(fzdyslxgm);
            }
        }

        //人均元
        if (paremeter.containsKey("frjy")) {
            String frjy = String.valueOf(paremeter.get("frjy"));
            if (!StringUtils.isEmpty(frjy)) {
                queryWarninganalysis.setFrjy(frjy);
            }
        }

        //刷卡占比
        if (paremeter.containsKey("fskzb")) {
            String fskzb = String.valueOf(paremeter.get("fskzb"));
            if (!StringUtils.isEmpty(fskzb)) {
                queryWarninganalysis.setFskzb(fskzb);
            }
        }

        //环保税税额
        if (paremeter.containsKey("fhbsse")) {
            String fhbsse = String.valueOf(paremeter.get("fhbsse"));
            if (!StringUtils.isEmpty(fhbsse)) {
                queryWarninganalysis.setFhbsse(fhbsse);
            }
        }

        //销售额占比
        if (paremeter.containsKey("fxsezb")) {
            String fxsezb = String.valueOf(paremeter.get("fxsezb"));
            if (!StringUtils.isEmpty(fxsezb)) {
                queryWarninganalysis.setFxsezb(fxsezb);
            }
        }

        //报名费
        if (paremeter.containsKey("fbmf")) {
            String fbmf = String.valueOf(paremeter.get("fbmf"));
            if (!StringUtils.isEmpty(fbmf)) {
                queryWarninganalysis.setFbmf(fbmf);
            }
        }

        //起征点
        if (paremeter.containsKey("fqzd")) {
            String fqzd = String.valueOf(paremeter.get("fqzd"));
            if (!StringUtils.isEmpty(fqzd)) {
                queryWarninganalysis.setFqzd(fqzd);
            }
        }

        //九二价格
        if (paremeter.containsKey("fjer")) {
            String fjer = String.valueOf(paremeter.get("fjer"));
            if (!StringUtils.isEmpty(fjer)) {
                queryWarninganalysis.setFjer(fjer);
            }
        }

        //九五价格
        if (paremeter.containsKey("fjwu")) {
            String fjwu = String.valueOf(paremeter.get("fjwu"));
            if (!StringUtils.isEmpty(fjwu)) {
                queryWarninganalysis.setFjwu(fjwu);
            }
        }

        //九八价格
        if (paremeter.containsKey("fjba")) {
            String fjba = String.valueOf(paremeter.get("fjba"));
            if (!StringUtils.isEmpty(fjba)) {
                queryWarninganalysis.setFjba(fjba);
            }
        }

        //柴油价格
        if (paremeter.containsKey("fcy")) {
            String fcy = String.valueOf(paremeter.get("fcy"));
            if (!StringUtils.isEmpty(fcy)) {
                queryWarninganalysis.setFcy(fcy);
            }
        }

        //加油站汽油价格换算
        if (paremeter.containsKey("fqydw")) {
            String fqydw = String.valueOf(paremeter.get("fqydw"));
            if (!StringUtils.isEmpty(fqydw)) {
                queryWarninganalysis.setFqydw(fqydw);
            }
        }

        //加油站柴油价格换算
        if (paremeter.containsKey("fcydw")) {
            String fcydw = String.valueOf(paremeter.get("fcydw"));
            if (!StringUtils.isEmpty(fcydw)) {
                queryWarninganalysis.setFcydw(fcydw);
            }
        }

        //房土两税房产税均价
        if (paremeter.containsKey("ffcsjj")) {
            String ffcsjj = String.valueOf(paremeter.get("ffcsjj"));
            if (!StringUtils.isEmpty(ffcsjj)) {
                queryWarninganalysis.setFfcsjj(ffcsjj);
            }
        }

        //房土两税城镇土地使用税均价
        if (paremeter.containsKey("fcztdsysjj")) {
            String fcztdsysjj = String.valueOf(paremeter.get("fcztdsysjj"));
            if (!StringUtils.isEmpty(fcztdsysjj)) {
                queryWarninganalysis.setFcztdsysjj(fcztdsysjj);
            }
        }

        //砂石每公斤炸药/公斤砂石
        if (paremeter.containsKey("fssy")) {
            String fssy = String.valueOf(paremeter.get("fssy"));
            if (!StringUtils.isEmpty(fssy)) {
                queryWarninganalysis.setFssy(fssy);
            }
        }

        //砂石增值税税率
        if (paremeter.containsKey("fzzssl")) {
            String fzzssl = String.valueOf(paremeter.get("fzzssl"));
            if (!StringUtils.isEmpty(fzzssl)) {
                queryWarninganalysis.setFzzssl(fzzssl);
            }
        }

        //砂石企业所得税税率
        if (paremeter.containsKey("fqysdssl")) {
            String fqysdssl = String.valueOf(paremeter.get("fqysdssl"));
            if (!StringUtils.isEmpty(fqysdssl)) {
                queryWarninganalysis.setFqysdssl(fqysdssl);
            }
        }

        //砂石附加税税率
        if (paremeter.containsKey("ffjssl")) {
            String ffjssl = String.valueOf(paremeter.get("ffjssl"));
            if (!StringUtils.isEmpty(ffjssl)) {
                queryWarninganalysis.setFfjssl(ffjssl);
            }
        }

        //房土两税分析口径
        if (paremeter.containsKey("fsfjn")) {
            String fsfjn = String.valueOf(paremeter.get("fsfjn"));
            if (!StringUtils.isEmpty(fsfjn)) {
                queryWarninganalysis.setFsfjn(fsfjn);
            }
        }

        //表名 第三方数据最新时间
        if (paremeter.containsKey("ftablename")) {
            String ftablename = String.valueOf(paremeter.get("ftablename"));
            if (!StringUtils.isEmpty(ftablename)) {
                queryWarninganalysis.setFtablename(ftablename);
            }
        }

        //预计可增收大于
        if (paremeter.containsKey("fyjkzs")) {
            Object fyjkzsStr = paremeter.get("fyjkzs");
            if (!StringUtils.isEmpty(fyjkzsStr)) {
                int fyjkzs = Integer.parseInt(paremeter.get("fyjkzs").toString());
                queryWarninganalysis.setFyjkzs(fyjkzs);
            }
        }
        return queryWarninganalysis;
    }*/

    /**
     * 台账
     *
     * @param paremeter
     * @return com.hnbp.jagl.bzb.tzfx.model.StandingBookParameter
     * @Title queryStandingBookParameter
     * @date 2019/7/29 18:09
     * <AUTHOR>
     */
    /*public static StandingBookParameter queryStandingBookParameter(Map paremeter) {
        StandingBookParameter standingBookParameter = new StandingBookParameter();
        //所属区域
        if (paremeter.containsKey("fssqy")) {
            String fssqy = String.valueOf(paremeter.get("fssqy"));
            if (!StringUtils.isEmpty(fssqy)) {
                standingBookParameter.setFssqy(fssqy);
            }
        }
        //纳税人名称
        if (paremeter.containsKey("fnsrmc")) {
            String fnsrmc = String.valueOf(paremeter.get("fnsrmc"));
            if (!StringUtils.isEmpty(fnsrmc)) {
                standingBookParameter.setFnsrmc(fnsrmc);
            }
        }
        //纳税人识别号
        if (paremeter.containsKey("fnsrsbh")) {
            String fnsrsbh = String.valueOf(paremeter.get("fnsrsbh"));
            if (!StringUtils.isEmpty(fnsrsbh)) {
                standingBookParameter.setFnsrsbh(fnsrsbh);
            }
        }
        if (paremeter.containsKey("fzsxm")) {
            String fzsxm = String.valueOf(paremeter.get("fzsxm"));
            if (!StringUtils.isEmpty(fzsxm)) {
                standingBookParameter.setFzsxm(fzsxm);
                standingBookParameter.setFzsxmStr(CommonUtil.stringEncapsulation(fzsxm));
            }
        }
        //行业
        if (paremeter.containsKey("fhyml")) {
            String fhyml = String.valueOf(paremeter.get("fhyml"));
            if (!StringUtils.isEmpty(fhyml)) {
                standingBookParameter.setFhyml(fhyml);
            }
        }
        //类型
        if (paremeter.containsKey("type")) {
            String type = String.valueOf(paremeter.get("type"));
            if (!StringUtils.isEmpty(type)) {
                standingBookParameter.setType(type);
            }
        }
        //开始年份
        if (paremeter.containsKey("startyear")) {
//            Calendar cal = Calendar.getInstance();
//            int year = cal.get(Calendar.YEAR);
            String startyear = paremeter.get("startyear").toString();
            if (!StringUtils.isEmpty(startyear)) {
                int year = Integer.parseInt(startyear);
                standingBookParameter.setStartyear(String.valueOf(year - 2));
                standingBookParameter.setEndyear(String.valueOf(year));
            }
        }

        //开始月
        if (paremeter.containsKey("fstartmonth")) {
            String startmonth = String.valueOf(paremeter.get("fstartmonth"));
            if (!StringUtils.isEmpty(startmonth)) {
                standingBookParameter.setStartmonth(startmonth);
            }
        }
        //结束月
        if (paremeter.containsKey("fendmonth")) {
            String endmonth = String.valueOf(paremeter.get("fendmonth"));
            if (!StringUtils.isEmpty(endmonth)) {
                standingBookParameter.setEndmonth(endmonth);
            }
        }

        //增减额
        if (paremeter.containsKey("fzje")) {
            String fzje = String.valueOf(paremeter.get("fzje"));
            if (!StringUtils.isEmpty(fzje)) {
                standingBookParameter.setFzje(fzje);
            }
        }
        //增减额
        if (paremeter.containsKey("fzjename")) {
            String fzjename = String.valueOf(paremeter.get("fzjename"));
            if (!StringUtils.isEmpty(fzjename)) {
                standingBookParameter.setFzjename(fzjename);
            }
        }
        //征收项目
//        if (paremeter.containsKey("fzsxm")) {
//            String fzsxm = String.valueOf(paremeter.get("fzsxm"));
//            if (!StringUtils.isEmpty(fzsxm)) {
//                standingBookParameter.setFzsxm(fzsxm);
//            }
//        }

        return standingBookParameter;
    }*/



    /**
     * 查询参数处理
     *
     * @param paremeter
     * @return
     */
    public static QueryCondition queryParameterProcessing(Map<String, Object> paremeter) {
        QueryCondition queryCondition = new QueryCondition();
        String userid = (String) paremeter.get("userId");
        queryCondition.setUserid_skgk(userid);
        //所属区域
        if (paremeter.containsKey("fssqy")) {
            String fssqy = String.valueOf(paremeter.get("fssqy"));
            if (!StringUtils.isEmpty(fssqy)) {
                queryCondition.setFssqy(fssqy);
                queryCondition.setFssqyStr(CommonUtil.stringEncapsulation(fssqy));
            }
        }

        if (paremeter.containsKey("fgm")) {
            String fgm = String.valueOf(paremeter.get("fgm"));
            queryCondition.setFgm(fgm);
            switch (fgm) {
                case "一百万以下":
                    queryCondition.setFgmdy("0");
                    queryCondition.setFgmxy("100");
                    break;
                case "一百万到五百万":
                    queryCondition.setFgmdy("100");
                    queryCondition.setFgmxy("500");
                    break;
                case "五百万到一千万":
                    queryCondition.setFgmdy("500");
                    queryCondition.setFgmxy("1000");
                    break;
                case "一千万到五千万":
                    queryCondition.setFgmdy("1000");
                    queryCondition.setFgmxy("5000");
                    break;
                case "五千万到一亿":
                    queryCondition.setFgmdy("5000");
                    queryCondition.setFgmxy("10000");
                    break;
                case "大于一亿":
                    queryCondition.setFgmdy("10000");
                    queryCondition.setFgmxy("10000000");
                    break;
            }
        }

        //增收项目
        if (paremeter.containsKey("page")) {
            String page = (String) paremeter.get("page");
            if (!StringUtils.isEmpty(page)) {
                queryCondition.setPage(Integer.valueOf(page));
            }
        }
        if (paremeter.containsKey("limit")) {
            String limit = (String) paremeter.get("limit");
            if (!StringUtils.isEmpty(limit)) {
                queryCondition.setLimit(Integer.valueOf(limit));
            }
        }

        //增收项目
        if (paremeter.containsKey("fzsxm")) {
            String fzsxm = String.valueOf(paremeter.get("fzsxm"));
            if (!StringUtils.isEmpty(fzsxm)) {
                queryCondition.setFzsxm(fzsxm);
                queryCondition.setFzsxmStr(CommonUtil.stringEncapsulation(fzsxm));
            }
        }

        if (paremeter.containsKey("fsz")) {
            String fsz = String.valueOf(paremeter.get("fsz"));
            if (!StringUtils.isEmpty(fsz)) {
                queryCondition.setFzsxm(fsz);
                queryCondition.setFzsxmStr(CommonUtil.stringEncapsulation(fsz));
            }
        }

        //季度
        if (paremeter.containsKey("typeTwo")) {
            String typeTwo = String.valueOf(paremeter.get("typeTwo"));
            if (!StringUtils.isEmpty(typeTwo)) {
                queryCondition.setTypeTwo(typeTwo);
            }
        }

        //季度
        if (paremeter.containsKey("fquarter")) {
            String fquarter = String.valueOf(paremeter.get("fquarter"));
            if (!StringUtils.isEmpty(fquarter)) {
                queryCondition.setFquarter(fquarter);
            }
        }

        //八项支出科目
        if (paremeter.containsKey("fzckm")) {
            String fzckm = String.valueOf(paremeter.get("fzckm"));
            if (!StringUtils.isEmpty(fzckm)) {
                queryCondition.setFzckm(fzckm);
                queryCondition.setFzckmStr(CommonUtil.stringEncapsulation(fzckm));
            }
        }

        //行业大类
        if (paremeter.containsKey("fhydl")) {
            String fhydl = String.valueOf(paremeter.get("fhydl"));
            if (!StringUtils.isEmpty(fhydl)) {
                queryCondition.setFhydl(fhydl);
                queryCondition.setFhydlStr(CommonUtil.stringEncapsulation(fhydl));
            }
        }

        //行业门类
        if (paremeter.containsKey("fhyml")) {
            String fhyml = String.valueOf(paremeter.get("fhyml"));
            if (!StringUtils.isEmpty(fhyml)) {
                queryCondition.setFhyml(fhyml);
                queryCondition.setFhymlStr(CommonUtil.stringEncapsulation(fhyml));
            }
        }
        //年
        if (paremeter.containsKey("start_year")) {
            queryCondition.setStart_year(String.valueOf(paremeter.get("start_year")));
            queryCondition.setAgo_year(String.valueOf(Integer.parseInt(paremeter.get("start_year").toString()) - 1));
        }
        //开始月
        if (paremeter.containsKey("fstartmonth")) {
            queryCondition.setFstartmonth(String.valueOf(paremeter.get("fstartmonth")));
        }

        //结束月
        if (paremeter.containsKey("fendmonth")) {
            queryCondition.setFendmonth(String.valueOf(paremeter.get("fendmonth")));
        }
        //交易类别
        if (paremeter.containsKey("fjylb")) {
            queryCondition.setFjylb(String.valueOf(paremeter.get("fjylb")));
        }
        //type 请求类型
        if (paremeter.containsKey("type")) {
            queryCondition.setType(String.valueOf(paremeter.get("type")));
        }
        //ftype 请求类型
        if (paremeter.containsKey("ftype")) {
            queryCondition.setFtype(String.valueOf(paremeter.get("ftype")));
        }
        //产业名称 请求类型
        if (paremeter.containsKey("fcymc")) {
            queryCondition.setFcymc(String.valueOf(paremeter.get("fcymc")));
        }
        //增收品目
        if (paremeter.containsKey("fzspm")) {
            queryCondition.setFzspm(String.valueOf(paremeter.get("fzspm")));
        }
        //增收品目
        if (paremeter.containsKey("userid")) {
            queryCondition.setUserid(String.valueOf(paremeter.get("userid")));
        }
        //纳税人排名
        if (paremeter.containsKey("fnsrpm")) {
            queryCondition.setFnsrpm(String.valueOf(paremeter.get("fnsrpm")));
        }
        //统计口径
        if (paremeter.containsKey("frkkj")) {
            queryCondition.setFrkkj(String.valueOf(paremeter.get("frkkj")));
        }
        //统计口径
        if (paremeter.containsKey("ftbzj")) {
            queryCondition.setFtbzj(String.valueOf(paremeter.get("ftbzj")));
        }
        if (paremeter.containsKey("fyskm")) {
            String fyskm = String.valueOf(paremeter.get("fyskm"));
            if (!StringUtils.isEmpty(fyskm)) {
                queryCondition.setFyskm(fyskm);
                queryCondition.setFyskmStr(CommonUtil.stringEncapsulation(fyskm));
            }
        }
        if (paremeter.containsKey("fzcdjlx")) {
            String fzcdjlx = String.valueOf(paremeter.get("fzcdjlx"));
            if (!StringUtils.isEmpty(fzcdjlx)) {
                queryCondition.setFzcdjlx(fzcdjlx);
                queryCondition.setFzcdjlxStr(CommonUtil.stringEncapsulation(fzcdjlx));
            }
        }
        if (paremeter.containsKey("ffhzt")) {
            queryCondition.setFfhzt(String.valueOf(paremeter.get("ffhzt")));
        }
        if (paremeter.containsKey("fdata")) {
            queryCondition.setFdata(String.valueOf(paremeter.get("fdata")));
        }
        if (paremeter.containsKey("fnsrmc")) {
            queryCondition.setFnsrmc(String.valueOf(paremeter.get("fnsrmc")));
        }
        if (paremeter.containsKey("fnsrsbh")) {
            queryCondition.setFnsrsbh(String.valueOf(paremeter.get("fnsrsbh")));
        }
        if (paremeter.containsKey("frkrq")) {
            queryCondition.setFrkrq(String.valueOf(paremeter.get("frkrq")));
        }
        if (paremeter.containsKey("fskssqq")) {
            queryCondition.setFskssqq(String.valueOf(paremeter.get("fskssqq")));
        }
        if (paremeter.containsKey("fyear")) {
            queryCondition.setFyear(String.valueOf(paremeter.get("fyear")));
        }
        if (paremeter.containsKey("fyear_e")) {
            queryCondition.setFyear_e(String.valueOf(paremeter.get("fyear_e")));
        }
        if (paremeter.containsKey("fyearzc_e")) {
            queryCondition.setFyearzc_e(String.valueOf(paremeter.get("fyearzc_e")));
        }
        if (paremeter.containsKey("fmonth")) {
            queryCondition.setFmonth(String.valueOf(paremeter.get("fmonth")));
        }
        if (paremeter.containsKey("fmonth_s")) {
            queryCondition.setFmonth_s(String.valueOf(paremeter.get("fmonth_s")));
        }
        if (paremeter.containsKey("fmonth_e")) {
            queryCondition.setFmonth_e(String.valueOf(paremeter.get("fmonth_e")));
        }
        if (paremeter.containsKey("fmonthzc_s")) {
            queryCondition.setFmonthzc_s(String.valueOf(paremeter.get("fmonthzc_s")));
        }
        if (paremeter.containsKey("fmonthzc")) {
            queryCondition.setFmonthzc(String.valueOf(paremeter.get("fmonthzc")));
        }
        if (paremeter.containsKey("fmonthzc_e")) {
            queryCondition.setFmonthzc_e(String.valueOf(paremeter.get("fmonthzc_e")));
        }
        if (paremeter.containsKey("fyearzc")) {
            queryCondition.setFyearzc(String.valueOf(paremeter.get("fyearzc")));
        }
        if (paremeter.containsKey("fcode_ss")) {
            queryCondition.setFcode_ss(String.valueOf(paremeter.get("fcode_ss")));
        }
        if (paremeter.containsKey("fldts")) {
            queryCondition.setFldts(String.valueOf(paremeter.get("fldts")));
        }
        if (paremeter.containsKey("fmdts")) {
            queryCondition.setFmdts(String.valueOf(paremeter.get("fmdts")));
        }

        if (paremeter.containsKey("fqsje")) {
            queryCondition.setFqsje(String.valueOf(paremeter.get("fqsje")));
        }

        if (paremeter.containsKey("fdq")) {
            if (paremeter.get("fdq") != null) {
                String s = paremeter.get("fdq").toString();
                if (!StringUtils.isEmpty(s)) {
                    String[] split = s.split(",");
                    List<Map<String, Object>> list = new ArrayList<>();
                    for (String value : split) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("fcs", value);
                        map.put("gdp", value + "GDP亿元");
                        map.put("gdpzs", value + "GDP亿元增速");
                        list.add(map);
                    }
                    queryCondition.setFdq(list);
                }
            }
        }
        if (paremeter.containsKey("fhy")) {
            if (paremeter.get("fhy") != null) {
                String s = paremeter.get("fhy").toString();
                if (!StringUtils.isEmpty(s)) {
                    String[] split = s.split(",");
                    List<Object> list = new ArrayList<>(Arrays.asList(split));
                    queryCondition.setFhy(list);
                }
            }
        }

        if (paremeter.containsKey("fjjzb")) {
            if (paremeter.get("fjjzb") != null) {
                String s = paremeter.get("fjjzb").toString();
                if (!StringUtils.isEmpty(s)) {
                    String[] split = s.split(",");
                    List<Object> list = new ArrayList<>(Arrays.asList(split));
                    queryCondition.setFjjzb(list);
                }
            }
        }

        if (paremeter.containsKey("ftime")) {
            if (paremeter.get("ftime") != null) {
                String ftime = paremeter.get("ftime").toString();
                queryCondition.setFtime(ftime);
            }
        }

        if (paremeter.containsKey("fqx")) {
            if (paremeter.get("fqx") != null) {
                String fqx = paremeter.get("fqx").toString();
                queryCondition.setFqx(fqx);
            }
        }


        return queryCondition;
    }


    /**
     * 查询参数处理
     *
     * @param paremeter
     * @return
     */
    /*public static QueryBusinessStatistics queryBusinessStatisticsParameterProcessing(Map paremeter) throws Exception {
        QueryBusinessStatistics queryBusinessStatistics = new QueryBusinessStatistics();
        //年
        if (paremeter.containsKey("start_year")) {
            queryBusinessStatistics.setStart_year(String.valueOf(paremeter.get("start_year")));
        }
        //开始金额
        if (paremeter.containsKey("start_je")) {
            queryBusinessStatistics.setStart_je(String.valueOf(paremeter.get("start_je")));
        }
        //结束金额
        if (paremeter.containsKey("end_je")) {
            queryBusinessStatistics.setEnd_je(String.valueOf(paremeter.get("end_je")));
        }
        //开始月
        if (paremeter.containsKey("fstartmonth")) {
            queryBusinessStatistics.setFstartmonth(String.valueOf(paremeter.get("fstartmonth")));
        }
        //结束月
        if (paremeter.containsKey("fendmonth")) {
            queryBusinessStatistics.setFendmonth(String.valueOf(paremeter.get("fendmonth")));
        }
        //纳税人名称
        if (paremeter.containsKey("fnsrmc")) {
            queryBusinessStatistics.setFnsrmc(String.valueOf(paremeter.get("fnsrmc")));
        }
        //纳税人排名
        if (paremeter.containsKey("fnsrpm")) {
            queryBusinessStatistics.setFnsrpm(String.valueOf(paremeter.get("fnsrpm")));
        }
        //纳税人识别号
        if (paremeter.containsKey("fnsrsbh")) {
            queryBusinessStatistics.setFnsrsbh(String.valueOf(paremeter.get("fnsrsbh")));
        }
        //收入类型
        if (paremeter.containsKey("fsrlx")) {
            queryBusinessStatistics.setFsrlx(String.valueOf(paremeter.get("fsrlx")));
        }
        //起始金额
        if (paremeter.containsKey("fqsje")) {
            queryBusinessStatistics.setFqsje(String.valueOf(paremeter.get("fqsje")));
        }
        //是否含留抵退税
        if (paremeter.containsKey("fldts")) {
            queryBusinessStatistics.setFldts(String.valueOf(paremeter.get("fldts")));
        }
        //所属区域
        if (paremeter.containsKey("fssqy")) {
            String fssqy = String.valueOf(paremeter.get("fssqy"));
            if (!StringUtils.isEmpty(fssqy)) {
                queryBusinessStatistics.setFssqy(fssqy);
                queryBusinessStatistics.setFssqyStr(CommonUtil.stringEncapsulation(fssqy));
            }
        }
        //所属行业
        if (paremeter.containsKey("fsshy")) {
            String fsshy = String.valueOf(paremeter.get("fsshy"));
            if (!StringUtils.isEmpty(fsshy)) {
                queryBusinessStatistics.setFsshy(fsshy);
                queryBusinessStatistics.setFsshyStr(CommonUtil.stringEncapsulation(fsshy));
            }
        }
        //fsskj;//入库口径
        if (paremeter.containsKey("frkkj")) {
            String frkkj = String.valueOf(paremeter.get("frkkj"));
            if (!StringUtils.isEmpty(frkkj)) {
                queryBusinessStatistics.setFrkkj(frkkj);
            }
        }

        //预算单位名称
        if (paremeter.containsKey("fysdwmc")) {
            String fysdwmc = String.valueOf(paremeter.get("fysdwmc"));
            if (!StringUtils.isEmpty(fysdwmc)) {
                queryBusinessStatistics.setFysdwmc(fysdwmc);
                queryBusinessStatistics.setFysdwmcStr(CommonUtil.stringEncapsulation(fysdwmc));
            }
        }

        //经济分类名称
        if (paremeter.containsKey("fjjflmc")) {
            String fjjflmc = String.valueOf(paremeter.get("fjjflmc"));
            if (!StringUtils.isEmpty(fjjflmc)) {
                queryBusinessStatistics.setFjjflmc(fjjflmc);
                queryBusinessStatistics.setFjjflmcStr(CommonUtil.stringEncapsulation(fjjflmc));
            }
        }
        //fsskj;增收品目
        if (paremeter.containsKey("fzspm")) {
            String fzspm = String.valueOf(paremeter.get("fzspm"));
            if (!StringUtils.isEmpty(fzspm)) {
                queryBusinessStatistics.setFzspm(fzspm);
            }
        }

        //增收项目
        if (paremeter.containsKey("fzsxm")) {
            String fzsxm = String.valueOf(paremeter.get("fzsxm"));
            if (!StringUtils.isEmpty(fzsxm)) {
                queryBusinessStatistics.setFzsxm(fzsxm);
                queryBusinessStatistics.setFzsxmStr(CommonUtil.stringEncapsulation(fzsxm));
            }
        }


        //行业大类
        if (paremeter.containsKey("fhydl")) {
            String fhydl = String.valueOf(paremeter.get("fhydl"));
            if (!StringUtils.isEmpty(fhydl)) {
                queryBusinessStatistics.setFhydl(fhydl);
                queryBusinessStatistics.setFhydlStr(CommonUtil.stringEncapsulation(fhydl));
            }
        }

        //行业门类
        if (paremeter.containsKey("fhyml")) {
            String fhyml = String.valueOf(paremeter.get("fhyml"));
            if (!StringUtils.isEmpty(fhyml)) {
                queryBusinessStatistics.setFhyml(fhyml);
                queryBusinessStatistics.setFhymlStr(CommonUtil.stringEncapsulation(fhyml));
            }
        }
        //注册登记类型
        if (paremeter.containsKey("fzcdjlx")) {
            String fzcdjlx = String.valueOf(paremeter.get("fzcdjlx"));
            if (!StringUtils.isEmpty(fzcdjlx)) {
                queryBusinessStatistics.setFzcdjlx(fzcdjlx);
                queryBusinessStatistics.setFzcdjlxStr(CommonUtil.stringEncapsulation(fzcdjlx));
            }
        }

        //预算科目
        if (paremeter.containsKey("fyskm")) {
            String fyskm = String.valueOf(paremeter.get("fyskm"));
            if (!StringUtils.isEmpty(fyskm)) {
                queryBusinessStatistics.setFyskm(fyskm);
                queryBusinessStatistics.setFyskmStr(CommonUtil.stringEncapsulation(fyskm));
            }
        }
        //税款所属期
        if (paremeter.containsKey("fskssqq")) {
            String fskssqq = String.valueOf(paremeter.get("fskssqq"));
            if (!StringUtils.isEmpty(fskssqq)) {
                String[] fskssqqsz = (fskssqq.split("~"));
                if (fskssqqsz.length >= 2) {
                    queryBusinessStatistics.setfStartSkssqq(fskssqqsz[0].trim());
                    queryBusinessStatistics.setfEndSkssqq(fskssqqsz[1].trim());
                    queryBusinessStatistics.setfStartSkssqqLastYear(date(fskssqqsz[0].trim()));
                    queryBusinessStatistics.setfEndSkssqqLastYear(date(fskssqqsz[1].trim()));
                }
            }
        }
        //税款所属期
        if (paremeter.containsKey("fskssq")) {
            String fskssq = String.valueOf(paremeter.get("fskssq"));
            if (!StringUtils.isEmpty(fskssq)) {
                String[] fskssqsz = (fskssq.split("~"));
                if (fskssqsz.length >= 2) {
                    queryBusinessStatistics.setfStartSkssqq(fskssqsz[0].trim());
                    queryBusinessStatistics.setfEndSkssqq(fskssqsz[1].trim());
                    queryBusinessStatistics.setfStartSkssqqLastYear(date(fskssqsz[0].trim()));
                    queryBusinessStatistics.setfEndSkssqqLastYear(date(fskssqsz[1].trim()));
                }
            }
        }
        //入库日期开始时间
        if (paremeter.containsKey("frkrq")) {
            String frkrq = String.valueOf(paremeter.get("frkrq"));
            if (!StringUtils.isEmpty(frkrq)) {
                String[] frkrqqz = (frkrq.split("~"));
                queryBusinessStatistics.setfStartRkrq(frkrqqz[0].trim());
                queryBusinessStatistics.setfEndRkrq(frkrqqz[1].trim());
                queryBusinessStatistics.setfStartRkrqLastYear(date(frkrqqz[0].trim()));
                queryBusinessStatistics.setfEndRkrqLastYear(date(frkrqqz[1].trim()));
            }
        }
        //注册日期开始时间
        if (paremeter.containsKey("fzcrq") && !"".equals(paremeter.get("fzcrq"))) {
            String fzcrq = String.valueOf(paremeter.get("fzcrq"));
            if (!StringUtils.isEmpty(fzcrq)) {
                String[] fzcrqqz = (fzcrq.split("~"));
                queryBusinessStatistics.setfStartZcrq(fzcrqqz[0].trim());
                queryBusinessStatistics.setfEndZcrq(fzcrqqz[1].trim());
                queryBusinessStatistics.setfStartZcrqLastYear(date(fzcrqqz[0].trim()));
                queryBusinessStatistics.setfEndZcrqLastYear(date(fzcrqqz[1].trim()));
            }
        }
        //增减幅类型
        if (paremeter.containsKey("fzjfType")) {
            String fzjfType = String.valueOf(paremeter.get("fzjfType"));
            if (!StringUtils.isEmpty(fzjfType)) {
                queryBusinessStatistics.setfzjfType(fzjfType);
            }
        }
        //增减幅
        if (paremeter.containsKey("fzjf")) {
            String fzjf = String.valueOf(paremeter.get("fzjf"));
            if (!StringUtils.isEmpty(fzjf)) {
                queryBusinessStatistics.setFzjf(fzjf);
            }
        }
        //年税收
        if (paremeter.containsKey("yeartotal")) {
            String yeartotal = String.valueOf(paremeter.get("yeartotal"));
            if (!StringUtils.isEmpty(yeartotal)) {
                queryBusinessStatistics.setYeartotal(yeartotal);
            }
        }
        //是否需要查询同期数据
        if (paremeter.containsKey("fqueryTqFlag")) {
            Boolean fqueryTqFlag = Boolean.parseBoolean(String.valueOf(paremeter.get("fqueryTqFlag")));
            queryBusinessStatistics.setFqueryTqFlag(fqueryTqFlag);
        }
        return queryBusinessStatistics;
    }*/

    /**
     * 传入对应实体类class   返回封装好参数的实体类对象
     *
     * @Title getEntity
     * @date 2019/7/17 17:50
     * <AUTHOR>
     */
    public static Object getEntity(Class c, Map<String, Object> map) throws Exception {
        Object obj = c.newInstance();
        Field[] declaredFields = c.getDeclaredFields();

        for (Field field : declaredFields) {
            //非静态和非final
            if ((!Modifier.isStatic(field.getModifiers())) && (!Modifier.isFinal(field.getModifiers()))) {
                field.setAccessible(true);
                field.set(obj, map.get(field.getName()));
            }

        }
        return obj;
    }


    /**
     * 请求参数处理
     *
     * @param paremeter
     * @return
     */
    /*public static QueryCondition requestParameterProcessing(Map paremeter) throws UnsupportedEncodingException {
        QueryCondition queryCondition = new QueryCondition();
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = servletRequestAttributes.getRequest();
        request.setCharacterEncoding("utf-8");
        return queryCondition;
    }*/

    /**
     * 时间减一年
     *
     * @return
     */
    public static String date(String str) throws Exception {
        SimpleDateFormat sj = new SimpleDateFormat("yyyy-MM-dd");

        Date date = sj.parse(str.length() == 7 ? str + "-01" : str);
//       Date date = new Date();//获取当前时间    
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, -1);//当前时间减去一年，即一年前的时间    
//       calendar.add(Calendar.MONTH, -1);//当前时间前去一个月，即一个月前的时间    
        //获取一年前的时间，或者一个月前的时间  
        return sj.format(calendar.getTime());
    }

    /**
     * 返回参数处理
     *
     * @param parameter
     * @return com.hnbp.jagl.bzb.srfx.model.ResponseParameter
     * @Title ResponseParameterDispose
     * @date 2019/7/12 9:10
     * <AUTHOR>
     */
    /*public static ResponseParameter responseParameterDispose(Object parameter) {
        ResponseParameter responseParameter = new ResponseParameter();
        responseParameter.setData(parameter);
        return responseParameter;
    }*/
}
