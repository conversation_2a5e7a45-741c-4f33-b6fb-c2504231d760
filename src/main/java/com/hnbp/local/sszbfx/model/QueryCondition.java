package com.hnbp.local.sszbfx.model;

import java.util.List;

/**
 * ━━━━━━神兽保佑━━━━━━
 * 　　　┏┓　　　┏┓
 * 　　┏┛┻━━━┛┻┓
 * 　　┃　　　　　　　┃
 * 　　┃　　　━　　　┃
 * 　　┃　┳┛　┗┳　┃
 * 　　┃　　　　　　　┃
 * 　　┃　　　┻　　　┃
 * 　　┃　　　　　　　┃
 * 　　┗━┓　　　┏━┛
 * 　　　　┃　　　┃
 * 　　　　┃　　　┃
 * 　　　　┃　　　┗━━━┓
 * 　　　　┃　　　　　　　┣┓
 * 　　　　┃　　　　　　　┏┛
 * 　　　　┗┓┓┏━┳┓┏┛
 * 　　　　　┃┫┫　┃┫┫
 * 　　　　　┗┻┛　┗┻┛
 * ━━━━━━永无BUG━━━━━━
 *
 * <AUTHOR>
 * @date 2019/7/2 20:03
 * @Title 查询条件实体类
 */
public class QueryCondition {
    private Integer page;
    private Integer limit;
    private String fssqy;//所属区域
    private String fzsxm;//征收项目

    private String fnsrpm;//纳税人排名
    private String start_year;//入库年份

    private String ago_year;//入库年份去年
    private String fstartmonth;//入库起始月
    private String fendmonth;//入库终止月
    private String fssqyStr;//所属区域字符串
    private String fhydl;//行业大类
    private String fhydlStr;//行业大类字符串
    private String fzsxmStr;//征收项目字符串
    private String type;//请求类型
    private String ftype;//请求类型
    private String fcymc;//产业名称
    private String fhyml;//行业门类字符串
    private String fhymlStr;//行业门类字符串
    private String fzspm;//增收品目
    private String fzckm;//支出科目
    private String fzckmStr;//支出科目字符串
    private String fjylb;//交易类别
    private String userid;//交易类别
    private String frkkj;//交易类别
    private String fyskm;
    private String fyskmStr;
    private String fzcdjlx;
    private String fzcdjlxStr;
    private String fnsrmc;
    private String fnsrsbh;
    private String frkrq;
    private String fskssqq;

    private String fssqyList;

    private String fzsxmList;

    private String fhydlList;

    private String userid_skgk;//税收类型

    private String i;
    private String l;

    private String fqx;
    private String ftime;
    private List fdq;

    private List fhy;
    private List fjjzb;

    private String fqsje;

    private String fyear;
    private String fmonth;

    private String fmonth_s;
    private String fmonth_e;
    private String fmonthzc_s;
    private String fmonthzc;
    private String fmonthzc_e;
    private String fyearzc;
    private String fcode_ss;
    private String fldts;
    private String fmdts;

    private String fgmdy;
    private String fgmxy;

    private String fgm;
    private String fquarter;

    private String tableName;
    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    private String typeTwo;
    public String getTypeTwo() {
        return typeTwo;
    }

    public void setTypeTwo(String typeTwo) {
        this.typeTwo = typeTwo;
    }

    public String getFquarter() {
        return fquarter;
    }

    public void setFquarter(String fquarter) {
        this.fquarter = fquarter;
    }

    private String fyear_e;
    private String fyearzc_e;

    public String getFmonthzc() {
        return fmonthzc;
    }

    public void setFmonthzc(String fmonthzc) {
        this.fmonthzc = fmonthzc;
    }

    public String getFyear_e() {
        return fyear_e;
    }

    public void setFyear_e(String fyear_e) {
        this.fyear_e = fyear_e;
    }

    public String getFyearzc_e() {
        return fyearzc_e;
    }

    public void setFyearzc_e(String fyearzc_e) {
        this.fyearzc_e = fyearzc_e;
    }

    public String getFgmdy() {
        return fgmdy;
    }

    public void setFgmdy(String fgmdy) {
        this.fgmdy = fgmdy;
    }

    public String getFgmxy() {
        return fgmxy;
    }

    public void setFgmxy(String fgmxy) {
        this.fgmxy = fgmxy;
    }

    public String getFgm() {
        return fgm;
    }

    public void setFgm(String fgm) {
        this.fgm = fgm;
    }

    public String getFmonth_s() {
        return fmonth_s;
    }

    public void setFmonth_s(String fmonth_s) {
        this.fmonth_s = fmonth_s;
    }

    public String getFmonth_e() {
        return fmonth_e;
    }

    public void setFmonth_e(String fmonth_e) {
        this.fmonth_e = fmonth_e;
    }

    public String getFmonthzc_s() {
        return fmonthzc_s;
    }

    public void setFmonthzc_s(String fmonthzc_s) {
        this.fmonthzc_s = fmonthzc_s;
    }

    public String getFmonthzc_e() {
        return fmonthzc_e;
    }

    public void setFmonthzc_e(String fmonthzc_e) {
        this.fmonthzc_e = fmonthzc_e;
    }

    public String getFyearzc() {
        return fyearzc;
    }

    public void setFyearzc(String fyearzc) {
        this.fyearzc = fyearzc;
    }

    public String getFcode_ss() {
        return fcode_ss;
    }

    public void setFcode_ss(String fcode_ss) {
        this.fcode_ss = fcode_ss;
    }

    public String getFldts() {
        return fldts;
    }

    public void setFldts(String fldts) {
        this.fldts = fldts;
    }

    public String getFmdts() {
        return fmdts;
    }

    public void setFmdts(String fmdts) {
        this.fmdts = fmdts;
    }

    public String getFyear() {
        return fyear;
    }


    public void setFyear(String fyear) {
        this.fyear = fyear;
    }

    public String getFmonth() {
        return fmonth;
    }

    public void setFmonth(String fmonth) {
        this.fmonth = fmonth;
    }

    public List getFhy() {
        return fhy;
    }

    public void setFhy(List fhy) {
        this.fhy = fhy;
    }

    public List getFdq() {
        return fdq;
    }

    public void setFdq(List fdq) {
        this.fdq = fdq;
    }

    public String getFhydlList() {
        return fhydlList;
    }

    public void setFhydlList(String fhydlList) {
        this.fhydlList = fhydlList;
    }

    public String getFzsxmList() {
        return fzsxmList;
    }

    public void setFzsxmList(String fzsxmList) {
        this.fzsxmList = fzsxmList;
    }

    public String getFssqyList() {
        return fssqyList;
    }

    public void setFssqyList(String fssqyList) {
        this.fssqyList = fssqyList;
    }

    public String getI() {
        return i;
    }

    public void setI(String i) {
        this.i = i;
    }

    public String getL() {
        return l;
    }

    public void setL(String l) {
        this.l = l;
    }

    public String getAgo_year() {
        return ago_year;
    }

    public void setAgo_year(String ago_year) {
        this.ago_year = ago_year;
    }

    public String getFnsrpm() {
        return fnsrpm;
    }

    public void setFnsrpm(String fnsrpm) {
        this.fnsrpm = fnsrpm;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getFjylb() {
        return fjylb;
    }

    public void setFjylb(String fjylb) {
        this.fjylb = fjylb;
    }

    public String getFzckm() {
        return fzckm;
    }

    public void setFzckm(String fzckm) {
        this.fzckm = fzckm;
    }

    public String getFzckmStr() {
        return fzckmStr;
    }

    public void setFzckmStr(String fzckmStr) {
        this.fzckmStr = fzckmStr;
    }

    public String getFzspmStr() {
        return fzckmStr;
    }

    public String getFzspm() {
        return fzspm;
    }

    public void setFzspm(String fzspm) {
        this.fzspm = fzspm;
    }

    public String getFhyml() {
        return fhyml;
    }

    public void setFhyml(String fhyml) {
        this.fhyml = fhyml;
    }

    public String getFhymlStr() {
        return fhymlStr;
    }

    public void setFhymlStr(String fhymlStr) {
        this.fhymlStr = fhymlStr;
    }

    public String getFcymc() {
        return fcymc;
    }

    public void setFcymc(String fcymc) {
        this.fcymc = fcymc;
    }

    public String getFhydl() {
        return fhydl;
    }

    public void setFhydl(String fhydl) {
        this.fhydl = fhydl;
    }

    public String getFzsxmStr() {
        return fzsxmStr;
    }

    public void setFzsxmStr(String fzsxmStr) {
        this.fzsxmStr = fzsxmStr;
    }

    public String getStart_year() {
        return start_year;
    }

    public void setStart_year(String start_year) {
        this.start_year = start_year;
    }

    public String getFstartmonth() {
        return fstartmonth;
    }

    public void setFstartmonth(String fstartmonth) {
        this.fstartmonth = fstartmonth;
    }

    public String getFendmonth() {
        return fendmonth;
    }

    public void setFendmonth(String fendmonth) {
        this.fendmonth = fendmonth;
    }

    public String getFssqyStr() {
        return fssqyStr;
    }

    public void setFssqyStr(String fssqyStr) {
        this.fssqyStr = fssqyStr;
    }

    public String getFhydlStr() {
        return fhydlStr;
    }

    public void setFhydlStr(String fhydlStr) {
        this.fhydlStr = fhydlStr;
    }

    public String getFssqy() {
        return fssqy;
    }

    public void setFssqy(String fssqy) {
        this.fssqy = fssqy;
    }

    public String getFzsxm() {
        return fzsxm;
    }

    public void setFzsxm(String fzsxm) {
        this.fzsxm = fzsxm;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUserid_skgk() {
        return userid_skgk;
    }

    public void setUserid_skgk(String userid_skgk) {
        this.userid_skgk = userid_skgk;
    }

    public String getFrkkj() {
        return frkkj;
    }

    public void setFrkkj(String frkkj) {
        this.frkkj = frkkj;
    }

    public String getFtype() {
        return ftype;
    }

    public void setFtype(String ftype) {
        this.ftype = ftype;
    }

    public String getFyskm() {
        return fyskm;
    }

    public void setFyskm(String fyskm) {
        this.fyskm = fyskm;
    }

    public String getFyskmStr() {
        return fyskmStr;
    }

    public void setFyskmStr(String fyskmStr) {
        this.fyskmStr = fyskmStr;
    }

    public String getFzcdjlx() {
        return fzcdjlx;
    }

    public void setFzcdjlx(String fzcdjlx) {
        this.fzcdjlx = fzcdjlx;
    }

    public String getFzcdjlxStr() {
        return fzcdjlxStr;
    }

    public void setFzcdjlxStr(String fzcdjlxStr) {
        this.fzcdjlxStr = fzcdjlxStr;
    }

    public String getFnsrmc() {
        return fnsrmc;
    }

    public void setFnsrmc(String fnsrmc) {
        this.fnsrmc = fnsrmc;
    }

    public String getFnsrsbh() {
        return fnsrsbh;
    }

    public void setFnsrsbh(String fnsrsbh) {
        this.fnsrsbh = fnsrsbh;
    }

    public String getFrkrq() {
        return frkrq;
    }

    public void setFrkrq(String frkrq) {
        this.frkrq = frkrq;
    }

    public String getFskssqq() {
        return fskssqq;
    }

    public void setFskssqq(String fskssqq) {
        this.fskssqq = fskssqq;
    }

    public String getFqx() {
        return fqx;
    }

    public void setFqx(String fqx) {
        this.fqx = fqx;
    }

    public String getFtime() {
        return ftime;
    }

    public void setFtime(String ftime) {
        this.ftime = ftime;
    }

    public String getFqsje() {
        return fqsje;
    }

    public void setFqsje(String fqsje) {
        this.fqsje = fqsje;
    }

    public List getFjjzb() {
        return fjjzb;
    }

    public void setFjjzb(List fjjzb) {
        this.fjjzb = fjjzb;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public void setFtbzj(String ftbzj) {

    }

    public void setFfhzt(String ffhzt) {

    }

    public void setFdata(String fdata) {

    }
}
