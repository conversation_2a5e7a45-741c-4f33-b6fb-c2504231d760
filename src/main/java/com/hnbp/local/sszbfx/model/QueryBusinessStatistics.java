package com.hnbp.local.sszbfx.model;

import com.hnbp.common.core.exception.ServiceException;
import com.hnbp.common.core.utils.StringUtils;
import lombok.Data;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;

/**
 * ━━━━━━神兽保佑━━━━━━
 * 　　　┏┓　　　┏┓
 * 　　┏┛┻━━━┛┻┓
 * 　　┃　　　　　　　┃
 * 　　┃　　　━　　　┃
 * 　　┃　┳┛　┗┳　┃
 * 　　┃　　　　　　　┃
 * 　　┃　　　┻　　　┃
 * 　　┃　　　　　　　┃
 * 　　┗━┓　　　┏━┛
 * 　　　　┃　　　┃
 * 　　　　┃　　　┃
 * 　　　　┃　　　┗━━━┓
 * 　　　　┃　　　　　　　┣┓
 * 　　　　┃　　　　　　　┏┛
 * 　　　　┗┓┓┏━┳┓┏┛
 * 　　　　　┃┫┫　┃┫┫
 * 　　　　　┗┻┛　┗┻┛
 * ━━━━━━永无BUG━━━━━━
 *
 * <AUTHOR>
 * @date 2019/7/11 17:21
 * @Title 业务统计查询实体
 */
@Data
public class QueryBusinessStatistics {
    //业务统计增加
    private String fStartSkssqq;//税款所属期开始时间
    private String fEndSkssqq;//税款所属期结束时间
    private String fStartSkssqqLastYear;//税款所属期开始时间前一年
    private String fEndSkssqqLastYear;//税款所属期结束时间前一年


    private String fStartRkrq;//入库日期开始时间
    private String fEndRkrq;//入库日期结束时间
    private String fStartRkrqLastYear;//入库日期开始时间前一年
    private String fEndRkrqLastYear;//入库日期结束时间前一年

    private String fStartZcrq;//注册日期开始时间
    private String fEndZcrq;//注册日期结束时间
    private String fStartZcrqLastYear;//注册日期开始时间前一年
    private String fEndZcrqLastYear;//注册日期结束时间前一年

    private String fzsxm;//征收项目
    private List<String> fzsxmList; // 征收项目集合
    private String fssqy;//所属区域
    private List<String> fssqyList; // 所属区域集合
    private String fhydl;//行业大类
    private List<String> fhydlList; // 行业大类集合
    private String fhyml;//行业门类
    private List<String> fhymlList; // 行业门类集合
    private String fyskm;//预算科目
    private List<String> fyskmList;
    private String fsshy;//所属行业
    private List<String> fsshyList;

    private String frkkj;//入库口径
    private String fzcdjlx;//注册登记类型
    private List<String> fzcdjlxList; // 注册登记类型集合
    //按纳税人排名统计
    private String fnsrmc;//纳税人名称
    private Integer fnsrpm;//纳税人排名 改为Integer，因为MySQL Limit子句的参数不允许运算操作
    private String fsrlx;//收入类型
    private String fqsje;//起始金额
    private String start_year;//入库年份
    private String fstartmonth;//入库起始月
    private String fendmonth;//入库终止月
    private String fnsrsbh;//纳税人识别号

    //税收总额增减排名情况表
    private String fzjfType;//增减幅类型  1.今年增减幅 2.今年+去年增减幅
    private String fzjf;//增减幅
    private String yeartotal;//年税收
    private String fzspm;//增收品目
    private String fysdwmc;//预算单位名称
    private List<String> fysdwmcList; // 预算单位名称集合
    private String fjjflmc; //经济分类名称
    private List<String> fjjflmcList; // 经济分类名称集合

    private String fldts;
    private String fmdts;

    private String start_je;

    private String end_je;

    private String userid_skgk;

    private Boolean fqueryTqFlag;

    private String fgmdy;
    private String fgmxy;

    private String fgm;

    private String fskssqq;
    private String fskssq;
    private String frkrq;
    private String fzcrq;

    private Integer page;
    private Integer limit;

    public void paramProcess(){
        this.fssqyList = StringUtils.convertStringToList(this.fssqy, String::valueOf);
        this.fzsxmList = StringUtils.convertStringToList(this.fzsxm, String::valueOf);
        this.fhydlList = StringUtils.convertStringToList(this.fhydl, String::valueOf);
        this.fhymlList = StringUtils.convertStringToList(this.fhyml, String::valueOf);
        this.fzcdjlxList = StringUtils.convertStringToList(this.fzcdjlx, String::valueOf);
        this.fysdwmcList = StringUtils.convertStringToList(this.fysdwmc, String::valueOf);
        this.fjjflmcList = StringUtils.convertStringToList(this.fjjflmc, String::valueOf);
        this.fsshyList = StringUtils.convertStringToList(this.fsshy, String::valueOf);
        this.fyskmList = StringUtils.convertStringToList(this.fyskm, String::valueOf);

        String[] fskssqqSplit = StringUtils.split(this.fskssqq, "~", 2);
        if (fskssqqSplit != null && fskssqqSplit.length == 2) {
            this.fStartSkssqq = fskssqqSplit[0].trim();
            this.fEndSkssqq = fskssqqSplit[1].trim();
            this.fStartSkssqqLastYear =  getLastYear(fskssqqSplit[0].trim());
            this.fEndSkssqqLastYear =  getLastYear(fskssqqSplit[1].trim());
        }
        String[] fskssqSplit = StringUtils.split(this.fskssq, "~", 2);
        if (fskssqSplit != null && fskssqSplit.length == 2) {
            this.fStartSkssqq = fskssqSplit[0].trim();
            this.fEndSkssqq = fskssqSplit[1].trim();
            this.fStartSkssqqLastYear =  getLastYear(fskssqSplit[0].trim());
            this.fEndSkssqqLastYear =  getLastYear(fskssqSplit[1].trim());
        }

        String[] frkrqSplit = StringUtils.split(this.frkrq, "~", 2);
        if (frkrqSplit != null && frkrqSplit.length == 2) {
            this.fStartRkrq = frkrqSplit[0].trim();
            this.fEndRkrq = frkrqSplit[1].trim();
            this.fStartRkrqLastYear =  getLastYear(frkrqSplit[0].trim());
            this.fEndRkrqLastYear =  getLastYear(frkrqSplit[1].trim());
        }

        String[] fzcrqSplit = StringUtils.split(this.fzcrq, "~", 2);
        if (fzcrqSplit != null && fzcrqSplit.length == 2) {
            this.fStartZcrq = fzcrqSplit[0].trim();
            this.fEndZcrq = fzcrqSplit[1].trim();
            this.fStartZcrqLastYear =  getLastYear(fzcrqSplit[0].trim());
            this.fEndZcrqLastYear =  getLastYear(fzcrqSplit[1].trim());
        }

    }

    // 获取去年日期
    public static String getLastYear(String date) {
        if (StringUtils.isEmpty(date)){
            return null;
        }
        if (date.length() == 7){
            date = date + "-01";
        }

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(sdf.parse(date));
            calendar.add(Calendar.YEAR, -1);
            return sdf.format(calendar.getTime());
        } catch (ParseException e) {
            throw new ServiceException(e.getMessage());
        }
    }
}
