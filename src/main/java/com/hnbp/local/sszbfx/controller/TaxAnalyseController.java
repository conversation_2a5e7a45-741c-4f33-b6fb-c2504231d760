package com.hnbp.local.sszbfx.controller;

import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.local.sszbfx.model.QueryCondition;
import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarChart;
import com.hnbp.common.core.utils.zhzs.echarts.line.LineChart;
import com.hnbp.common.core.utils.zhzs.echarts.pie.PieChart;
import com.hnbp.common.core.utils.zhzs.paging.PageParameter;
import com.hnbp.common.core.utils.zhzs.util.CommonUtil;
import com.hnbp.local.sszbfx.util.ParameterUtil;
import com.hnbp.local.sszbfx.service.TaxAnalyseService;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 主体分析
 * @date 2024-09-14
 */
@Controller
public class TaxAnalyseController {

    @Autowired
    private TaxAnalyseService taxAnalyseService;


    @RequestMapping("overallStreetConditionsChart")
    @ResponseBody
    public ResultMsg overallStreetConditionsChart(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {
        PieChart pieChart = taxAnalyseService.overallStreetConditionsChart(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(pieChart);
    }

    @RequestMapping("queryStreetDetails")
    @ResponseBody
    public ResultMsg queryStreetDetails(@RequestParam Map<String, Object> parameterMap) throws Exception {
        BarChart barChart = taxAnalyseService.queryStreetDetails(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(barChart);
    }

    @RequestMapping("districtAndCountyCompletionChart_bzb")
    @ResponseBody
    public ResultMsg districtAndCountyCompletionChart_bzb(@RequestParam Map<String, Object> parameterMap) throws Exception {
        BarChart barChart = taxAnalyseService.districtAndCountyCompletionChart(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(barChart);
    }

    @RequestMapping("TjdistrictAndCountyCompletionChart_bzb")
    @ResponseBody
    public ResultMsg TjdistrictAndCountyCompletionChart_bzb(@RequestParam Map<String, Object> parameterMap) throws Exception {
        BarChart barChart = taxAnalyseService.TjdistrictAndCountyCompletionChart_bzb(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(barChart);
    }


    @RequestMapping("districtAndCountyCompletionTable_bzb")
    @ResponseBody
    public ResultMsg districtAndCountyCompletionTable_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {
        PageInfo pageInfo = taxAnalyseService.districtAndCountyCompletionTable(ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    @RequestMapping("TjdistrictAndCountyCompletionTable_bzb")
    @ResponseBody
    public ResultMsg TjdistrictAndCountyCompletionTable_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {
        PageInfo pageInfo = taxAnalyseService.TjdistrictAndCountyCompletionTable(ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }


    @RequestMapping("detailedSnalysisRevenueTrend")
    @ResponseBody
    public ResultMsg detailedSnalysisRevenueTrend(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {
        BarChart bieData = taxAnalyseService.detailedSnalysisRevenueTrend(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(bieData);
    }


    @RequestMapping("industryAnalysisDetails_bzb")
    @ResponseBody
    public ResultMsg industryAnalysisDetails_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {
        // 查询条件-行业大类
        String fhydlStr = Optional.ofNullable(parameterMap.get("fhydl"))
                .filter(fhydl -> !StringUtils.isEmpty(fhydl))
                .map(fhydl -> String.valueOf(fhydl).split(","))
                .map(hyList -> Arrays.stream(hyList).collect(Collectors.joining(",", "'", "'")))
                .orElse(null);
        parameterMap.put("fhydlStr", fhydlStr);
        PieChart pieData = taxAnalyseService.industryAnalysisDetails(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(pieData);
    }

    @RequestMapping("industryAnalysisDetailsV2_bzb")
    @ResponseBody
    public ResultMsg industryAnalysisDetailsV2_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {
        // 查询条件-行业大类
        String fhydlStr = Optional.ofNullable(parameterMap.get("fhydl"))
                .filter(fhydl -> !StringUtils.isEmpty(fhydl))
                .map(fhydl -> String.valueOf(fhydl).split(","))
                .map(hyList -> Arrays.stream(hyList).collect(Collectors.joining(",", "'", "'")))
                .orElse(null);
        parameterMap.put("fhydlStr", fhydlStr);
        QueryCondition queryCondition = ParameterUtil.queryParameterProcessing(parameterMap);
        PieChart pieData = taxAnalyseService.industryAnalysisDetailsV2(queryCondition);
        return ResultMsg.success(pieData);
    }


    @RequestMapping("regionalTaxSituation")
    @ResponseBody
    public ResultMsg regionalTaxSituation(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {
        PageInfo pageInfo = taxAnalyseService.regionalTaxSituation((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }


    @RequestMapping("monthlyTaxSituation")
    @ResponseBody
    public ResultMsg monthlyTaxSituation(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {
        //        //参数处理
        PageInfo pageInfo = taxAnalyseService.monthlyTaxSituation((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }


    @RequestMapping("industryTaxpayersTable_bzb")
    @ResponseBody
    public ResultMsg industryTaxpayersTable_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {


        //参数处理
        // 查询条件-行业大类
        String fhydlStr = Optional.ofNullable(parameterMap.get("fhydl"))
                .filter(fhydl -> !StringUtils.isEmpty(fhydl))
                .map(fhydl -> String.valueOf(fhydl).split(","))
                .map(hyList -> Arrays.stream(hyList).collect(Collectors.joining(",", "'", "'")))
                .orElse(null);
        parameterMap.put("fhydlStr", fhydlStr);
        PageInfo pageInfo = taxAnalyseService.industryTaxpayersTable((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

/*    @RequestMapping("zdnsrsscx_bzb")
    @ResponseBody
    public ResultMsg zdnsrsscx_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {
        //参数处理
        PageInfo pageInfo = taxAnalyseService.zdnsrsscx((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }*/


    @RequestMapping("nasrExceljx_bzb")
    @ResponseBody
    public ResultMsg nasrExceljx_bzb(@RequestParam("file") MultipartFile file,
                                     HttpServletRequest request,
                                     @RequestParam Map<String, Object> parameterMap) throws Exception {

        String fileType = String.valueOf(parameterMap.get("fileType"));
        String fileName = file.getOriginalFilename();
        if (fileType == null || "".equals(fileType)) {
            int pointIndex = fileName.lastIndexOf("."); //点号的位置
            fileType = fileName.substring(pointIndex);//截取文件后缀
        }
        // 获取ServletContext对象来获取应用的真实路径
        ServletContext servletContext = request.getSession().getServletContext();
        String realPath = servletContext.getRealPath("upload/temp/");
        // 创建目标文件路径
        String uniqueFileName = UUID.randomUUID() + fileType; // 使用UUID确保文件名唯一
        File target = new File(realPath, uniqueFileName);

        // 复制文件到目标路径
        FileUtils.writeByteArrayToFile(target, file.getBytes()); // 使用更安全的方式写入文件

        List<Map<String, Object>> list = null;
        try {
            list = CommonUtil.readExcel(target);
        } catch (Exception e) {
            if (e.getMessage().contains("自定义")) {
                return ResultMsg.error("500", e.getMessage().replace("自定义", ""));
            } else {
                e.printStackTrace();
            }
        }
        if (list != null) {
            ResultMsg resultMsg = ResultMsg.success();
            boolean isExist = false;
            List<String> mcList = new ArrayList<>();
            List<String> sbhList = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                Map<String, Object> map = list.get(i);
                if (StringUtils.isEmpty(map.get("fnsrmc")) &&
                        StringUtils.isEmpty(map.get("fnsrsbh"))) {
                    isExist = true;
                    resultMsg.setMsg("第" + (i + 2) + "行未识别到纳税人名称和纳税人识别号，可能为EXCEL表头异常，或者该行为空行");
                    break;
                }
                if (!StringUtils.isEmpty(map.get("fnsrmc"))) {
                    String fnsrmc = map.get("fnsrmc").toString();
                    if (mcList.contains(fnsrmc)) {
                        isExist = true;
                        resultMsg.setMsg("第" + (i + 2) + "行识别到重复名称：" + fnsrmc);
                        break;
                    }
                    mcList.add(fnsrmc);
                }
                if (!StringUtils.isEmpty(map.get("fnsrsbh"))) {
                    String fnsrsbh = map.get("fnsrsbh").toString();
                    if (sbhList.contains(fnsrsbh)) {
                        isExist = true;
                        resultMsg.setMsg("第" + (i + 2) + "行识别到重复识别号：" + fnsrsbh);
                        break;
                    }
                    sbhList.add(fnsrsbh);
                }
            }
            if (!isExist) {
                taxAnalyseService.delNsr();
                taxAnalyseService.addNsr(list);
            }
            return resultMsg;
        }
        return ResultMsg.error();
    }


    @RequestMapping("enterpriseAbnormalAnalysis_bzb")
    @ResponseBody
    public ResultMsg enterpriseAbnormalAnalysis_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {
        parameterMap.put("entrance", "Map");//解析参数执行入口
        //参数处理
        PageInfo pageInfo = taxAnalyseService.enterpriseAbnormalAnalysis(ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }


}
