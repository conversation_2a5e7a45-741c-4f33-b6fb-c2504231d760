package com.hnbp.local.sszbfx.controller;

import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarChart;
import com.hnbp.common.core.utils.zhzs.echarts.line.LineChart;
import com.hnbp.common.core.utils.zhzs.echarts.pie.PieChart;
import com.hnbp.common.core.utils.zhzs.paging.PageParameter;
import com.hnbp.local.sszbfx.util.ParameterUtil;
import com.hnbp.local.sszbfx.model.QueryBusinessStatistics;
import com.hnbp.local.sszbfx.service.IndustryAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 分行业分析--Controller
 * @date 2024-09-14
 */
@Controller
//@RequestMapping("/page/jjzbfxxt/sszbfx/")
public class IndustryAnalysisController {

    @Autowired
    private IndustryAnalysisService IndustryAnalysisService;

    /**
     * 按税种统计整体分析(饼图)
     *
     * @return PageInfo
     * @Title taxStructureAnalysis
     * <AUTHOR>
     */
    @RequestMapping("taxStructureAnalysis_bzb")
    @ResponseBody
    public ResultMsg taxStructureAnalysis_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {

        PieChart pieData = IndustryAnalysisService.taxStructureAnalysis(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(pieData);
    }

    @RequestMapping("taxStructureAnalysisV2_bzb")
    @ResponseBody
    public ResultMsg taxStructureAnalysisV2_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {

        PieChart pieData = IndustryAnalysisService.taxStructureAnalysisV2(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(pieData);
    }

    /**
     * 按税种统计整体分析(表)
     *
     * @return PageInfo
     * @Title byTaxStatistics
     * <AUTHOR>
     */
    @RequestMapping("byTaxStatistics_bzb")
    @ResponseBody
    public ResultMsg byTaxStatistics_bzb(QueryBusinessStatistics parameterMap) throws IOException, SQLException {


        parameterMap.paramProcess();//解析参数执行入口
        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.byTaxStatistics(parameterMap);
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    /**
     * 按税种统计整体分析(历年分月柱状图)
     *
     * @return PageInfo
     * @Title changesOverTheYears
     * <AUTHOR>
     */
    @RequestMapping("changesOverTheYears_bzb")
    @ResponseBody
    public ResultMsg changesOverTheYears_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {
        BarChart bieData = IndustryAnalysisService.changesOverTheYears(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(bieData);
    }

    @RequestMapping("changesOverTheYearsV2_bzb")
    @ResponseBody
    public ResultMsg changesOverTheYearsV2_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {
        BarChart bieData = IndustryAnalysisService.changesOverTheYearsV2(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(bieData);
    }


    /**
     * 按地区统计 (表)
     *
     * @return PageInfo
     * @Title industryTaxpayersTable
     * @date 2019/7/11 10:44
     * <AUTHOR>
     */
    @RequestMapping("regionalStatistics")
    @ResponseBody
    public ResultMsg regionalStatistics(QueryBusinessStatistics parameterMap) throws IOException, SQLException {
        parameterMap.paramProcess();//解析参数执行入口
        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.regionalStatistics(parameterMap);
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }


    /**
     * 按纳税人排名统计(表)
     *
     * @return PageInfo
     * @Title rankingByTaxpayer
     * @date 2019/7/11 10:44
     * <AUTHOR>
     */
    @RequestMapping("rankingByTaxpayer_bzb")
    @ResponseBody
    public ResultMsg rankingByTaxpayer_bzb(QueryBusinessStatistics parameterMap) throws IOException, SQLException {
        parameterMap.paramProcess();//解析参数执行入口
        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.rankingByTaxpayer(parameterMap);
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    /**
     * 按纳税人排名统计
     *
     * @return PageInfo
     * @Title rankingByTaxpayer
     * @date 2019/7/11 10:44
     * <AUTHOR>
     */
    @RequestMapping("rankingByTaxpayerEchar_bzb")
    @ResponseBody
    public ResultMsg rankingByTaxpayerEchar_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {
        PieChart pieData = IndustryAnalysisService.rankingByTaxpayerEchar(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(pieData);
    }

    @RequestMapping("rankingByTaxpayerEcharline_bzb")
    @ResponseBody
    public ResultMsg rankingByTaxpayerEcharline_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {
        BarChart bieData = IndustryAnalysisService.rankingByTaxpayerEcharline(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(bieData);
    }


    @RequestMapping("findFinancialResources_bzb")
    @ResponseBody
    public ResultMsg findFinancialResources_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {
        parameterMap.put("entrance", "Map");//解析参数执行入口
        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.findFinancialResources((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    @RequestMapping("findFinancialResourcesTax_bzb")
    @ResponseBody
    public ResultMsg findFinancialResourcesTax_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {
        parameterMap.put("entrance", "Map");//解析参数执行入口
        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.findFinancialResourcesTax((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }


    /**
     * 按行业统计(表)
     *
     * @return PageInfo
     * @Title statisticsByIndustry
     * @date 2019/7/11 10:44
     * <AUTHOR>
     */
    @RequestMapping("statisticsByIndustry_bzb")
    @ResponseBody
    public ResultMsg statisticsByIndustry_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {
        // 请求参数处理
        ParameterUtil.handleRequestParam(parameterMap);
        parameterMap.put("entrance", "Map");
        PageInfo pageInfo = IndustryAnalysisService.statisticsByIndustry((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    @RequestMapping("statisticsByIndustryV2_bzb")
    @ResponseBody
    public ResultMsg statisticsByIndustryV2_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {
        // 请求参数处理
        ParameterUtil.handleRequestParam(parameterMap);
        parameterMap.put("entrance", "Map");
        PageInfo pageInfo = IndustryAnalysisService.statisticsByIndustryV2((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }


    /**
     * 按登记类型统计(表)
     *
     * @return PageInfo
     * @Title statisticsByRegistrationType
     * @date 2019/7/11 10:44
     * <AUTHOR>
     */
    @RequestMapping("statisticsByRegistrationType")
    @ResponseBody
    public ResultMsg statisticsByRegistrationType(QueryBusinessStatistics parameterMap) throws IOException, SQLException {
        parameterMap.paramProcess();//解析参数执行入口
        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.statisticsByRegistrationType(parameterMap);
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }


    /**
     * 纳税净入库通用查询(表)
     *
     * @return PageInfo
     * @Title statisticsByRegistrationType
     * @date 2019/7/11 10:44
     * <AUTHOR>
     */
/*    @RequestMapping("warehousingGeneralQuery_bzb")
    @ResponseBody
    public ResultMsg warehousingGeneralQuery_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {
        parameterMap.paramProcess();//解析参数执行入口
        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.warehousingGeneralQuery((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }*/

    /**
     * 分户分税种明细查询(表)
     *
     * @return PageInfo
     * @Title statisticsByRegistrationType
     * @date 2019/7/11 10:44
     * <AUTHOR>
     */
/*    @RequestMapping("detailsOfHouseholdTaxInquiry_bzb")
    @ResponseBody
    public ResultMsg detailsOfHouseholdTaxInquiry_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {
        parameterMap.paramProcess();//解析参数执行入口
        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.detailsOfHouseholdTaxInquiry((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }*/


    /**
     * 纳税人纳税情况查询(表)
     *
     * @return PageInfo
     * @Title statisticsByRegistrationType
     * @date 2019/7/11 10:44
     * <AUTHOR>
     */
    @RequestMapping("totalTaxRanking_bzb")
    @ResponseBody
    public ResultMsg totalTaxRanking_bzb(@RequestParam Map<String, Object> parameterMap) throws Exception {
        // 请求参数处理
        ParameterUtil.handleRequestParam(parameterMap);
        parameterMap.put("entrance", "Map");
        PageInfo pageInfo = IndustryAnalysisService.totalTaxRanking((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    /**
     * 企业利润变化情况表(表)
     *
     * @return PageInfo
     * @Title enterpriseProfitChanges
     * @date 2019/7/11 10:44
     * <AUTHOR>
     */
    @RequestMapping("enterpriseProfitChanges")
    @ResponseBody
    public ResultMsg enterpriseProfitChanges(QueryBusinessStatistics parameterMap) throws IOException, SQLException {
        parameterMap.paramProcess();//解析参数执行入口
        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.enterpriseProfitChanges(parameterMap);
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    /**
     * 增值税分行业情况(饼图)
     *
     * @return void
     * @Title VATSectorInformationEcharts
     * @date 2019/7/5 12:24
     * <AUTHOR>
     */
    @RequestMapping("VATSectorInformationEcharts_bzb")
    @ResponseBody
    public ResultMsg VATSectorInformationEcharts_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {

        PieChart pieChart = IndustryAnalysisService.VATSectorInformationEcharts(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(pieChart);
    }

    /**
     * 增值税分行业情况明细(饼图)
     *
     * @return void
     * @Title overallStreetConditionsChart
     * @date 2019/7/5 12:24
     * <AUTHOR>
     */
    @RequestMapping("detailsOfVATIndustryEcharts_bzb")
    @ResponseBody
    public ResultMsg detailsOfVATIndustryEcharts_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {
        PieChart pieChart = IndustryAnalysisService.detailsOfVATIndustryEcharts(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(pieChart);
    }

    /**
     * 增值税分行业情况明细(表格)detailsOfVATIndustryTable
     *
     * @return PageInfo
     * @Title detailsOfVATIndustryTable
     * @date 2019/7/11 10:44
     * <AUTHOR>
     */
    @RequestMapping("detailsOfVATIndustryTable_bzb")
    @ResponseBody
    public ResultMsg detailsOfVATIndustryTable_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {
        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.detailsOfVATIndustryTable((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    /**
     * 注册企业增长分析(表格)
     *
     * @return PageInfo
     * @Title registeredBusinessGrowthAnalysis
     * @date 2019/7/11 10:44
     * <AUTHOR>
     */
    @RequestMapping("registeredBusinessGrowthAnalysis_bzb")
    @ResponseBody
    public ResultMsg registeredBusinessGrowthAnalysis_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {

        ParameterUtil.handleRequestParam(parameterMap);
        parameterMap.put("entrance", "Map");//解析参数执行入口
        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.registeredBusinessGrowthAnalysis((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    @RequestMapping("enterpriseScaleAnalysis_2_bzb")
    @ResponseBody
    public ResultMsg enterpriseScaleAnalysis_2_bzb(@RequestParam Map<String, Object> parameterMap) throws Exception {

        ParameterUtil.handleRequestParam(parameterMap);
        parameterMap.put("entrance", "Map");//解析参数执行入口
        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.enterpriseScaleAnalysis_2(ParameterUtil.pagingParameter(parameterMap));

        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    @RequestMapping("enterpriseScaleAnalysisMx_bzb")
    @ResponseBody
    public ResultMsg enterpriseScaleAnalysisMx_bzb(@RequestParam Map<String, Object> parameterMap) throws Exception {

        ParameterUtil.handleRequestParam(parameterMap);
        parameterMap.put("entrance", "Map");//解析参数执行入口
        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.enterpriseScaleAnalysisMx((PageParameter) ParameterUtil.pagingParameter(parameterMap));

        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }


    /**
     * 重点税源企业分析
     *
     * @return void
     * @Title analysisOfKeyTaxSources
     * @date 2019/7/17 10:14
     * <AUTHOR>
     */
    @RequestMapping("analysisOfKeyTaxSources")
    @ResponseBody
    public ResultMsg analysisOfKeyTaxSources(QueryBusinessStatistics parameterMap) throws IOException, SQLException {
        parameterMap.paramProcess();//解析参数执行入口
        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.analysisOfKeyTaxSources(parameterMap);
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    /**
     * 个人所得税增收品目分析 (饼图)
     *
     * @return void
     * @Title analysisOfAdditionalItemsEcharts
     * @date 2019/7/5 12:24
     * <AUTHOR>
     */
    @RequestMapping("analysisOfAdditionalItemsEcharts")
    @ResponseBody
    public ResultMsg analysisOfAdditionalItemsEcharts(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {
        PieChart pieChart = IndustryAnalysisService.analysisOfAdditionalItemsEcharts(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(pieChart);
    }

    /**
     * 个人所得税增收品目分析明细 (饼图)
     *
     * @return void
     * @Title analysisOfAdditionalItemsEcharts
     * @date 2019/7/5 12:2
     * <AUTHOR>
     */
    @RequestMapping("analysisOfAdditionalItemsDetailEchart")
    @ResponseBody
    public ResultMsg analysisOfAdditionalItemsDetailEchart(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {
        PieChart pieChart = IndustryAnalysisService.analysisOfAdditionalItemsDetailEchart(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(pieChart);
    }

    /**
     * 个人所得税增收品目分析(table)
     *
     * @return PageInfo
     * @Title analysisOfAdditionalItemsTable
     * @date 2019/7/11 10:4
     * <AUTHOR>
     */
    @RequestMapping("analysisOfAdditionalItemsTable")
    @ResponseBody
    public ResultMsg analysisOfAdditionalItemsTable(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {
        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.analysisOfAdditionalItemsTable((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    /**
     * 企业所得税分行业(饼图)
     *
     * @return void
     * @Title enterpriseIncomeTaxSubsectorsEchart
     * @date 2019/7/5 12:24
     * <AUTHOR>
     */
    @RequestMapping("enterpriseIncomeTaxSubsectorsEchart_bzb")
    @ResponseBody
    public ResultMsg enterpriseIncomeTaxSubsectorsEchart_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {

        PieChart pieChart = IndustryAnalysisService.enterpriseIncomeTaxSubsectorsEchart(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(pieChart);
    }

    /**
     * 企业所得税分行业明细(饼图)
     *
     * @return void
     * @Title detailsEnterpriseIncomeTaxSubsectorsEchart
     * @date 2019/7/5 12:24
     * <AUTHOR>
     */
    @RequestMapping("detailsEnterpriseIncomeTaxSubsectorsEchart_bzb")
    @ResponseBody
    public ResultMsg detailsEnterpriseIncomeTaxSubsectorsEchart_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {

        PieChart pieChart = IndustryAnalysisService.detailsEnterpriseIncomeTaxSubsectorsEchart(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(pieChart);
    }

    /**
     * 企业所得税分行业明细(表格)
     *
     * @return PageInfo
     * @Title detailsEnterpriseIncomeTaxSubsectorsTable
     * @date 2019/7/11 10:44
     * <AUTHOR>
     */
    @RequestMapping("detailsEnterpriseIncomeTaxSubsectorsTable_bzb")
    @ResponseBody
    public ResultMsg detailsEnterpriseIncomeTaxSubsectorsTable_bzb(QueryBusinessStatistics parameterMap) throws IOException, SQLException {


        parameterMap.paramProcess();//解析参数执行入口
        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.detailsEnterpriseIncomeTaxSubsectorsTable(parameterMap);
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }


    /**
     * 企业纳税规模分析(表格)
     *
     * @return PageInfo
     * <AUTHOR>
     */
    @RequestMapping("enterpriseScaleAnalysis_bzb")
    @ResponseBody
    public ResultMsg enterpriseScaleAnalysis_bzb(@RequestParam Map<String, Object> parameterMap) throws Exception {

        ParameterUtil.handleRequestParam(parameterMap);
        parameterMap.put("entrance", "Map");//解析参数执行入口
        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.enterpriseScaleAnalysis((PageParameter) ParameterUtil.pagingParameter(parameterMap));

        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }


    @RequestMapping("subIndustryAnalysis_bzb")
    @ResponseBody
    public ResultMsg subIndustryAnalysis_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {

        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.subIndustryAnalysis((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    @RequestMapping("revenueByTaxTypeTable_bzb")
    @ResponseBody
    public ResultMsg revenueByTaxTypeTable_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {

        // 请求参数处理
        ParameterUtil.handleRequestParam(parameterMap);
        parameterMap.put("entrance", "Map");
        PageInfo pageInfo = IndustryAnalysisService.revenueByTaxTypeTable((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    @RequestMapping("revenueByTaxTypeTableV2_bzb")
    @ResponseBody
    public ResultMsg revenueByTaxTypeTableV2_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {

        // 请求参数处理
        ParameterUtil.handleRequestParam(parameterMap);
        parameterMap.put("entrance", "Map");
        PageInfo pageInfo = IndustryAnalysisService.revenueByTaxTypeTableV2((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }


    @RequestMapping("revenueByTaxTypeMxTable_bzb")
    @ResponseBody
    public ResultMsg revenueByTaxTypeMxTable_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {

        // 请求参数处理
        ParameterUtil.handleRequestParam(parameterMap);
        parameterMap.put("entrance", "Map");
        PageInfo pageInfo = IndustryAnalysisService.revenueByTaxTypeMxTable((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    @RequestMapping("revenueByTaxTypeMxTableXzsy_bzb")
    @ResponseBody
    public ResultMsg revenueByTaxTypeMxTableXzsy_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {

        // 请求参数处理
        ParameterUtil.handleRequestParam(parameterMap);
        parameterMap.put("entrance", "Map");
        PageInfo pageInfo = IndustryAnalysisService.revenueByTaxTypeMxTableXzsy((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    @RequestMapping("subCompaniesIndustryAnalysis_bzb")
    @ResponseBody
    public ResultMsg subCompaniesIndustryAnalysis_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {
        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.subCompaniesIndustryAnalysis((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }


    @RequestMapping("taxStructureAnalysis_fsz_bzb")
    @ResponseBody
    public ResultMsg taxStructureAnalysis_fsz(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {

        PieChart pieData = IndustryAnalysisService.taxStructureAnalysis_fsz(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(pieData);
    }

    @RequestMapping("AnlzByZspm_bzb")
    @ResponseBody
    public ResultMsg AnlzByZspm_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {

        ParameterUtil.handleRequestParam(parameterMap);
        parameterMap.put("entrance", "Map");

        PageInfo pageInfo = IndustryAnalysisService.AnlzByZspm(ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    @RequestMapping("AnlzByZsxm_bzb")
    @ResponseBody
    public ResultMsg AnlzByZsxm_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {

        ParameterUtil.handleRequestParam(parameterMap);
        parameterMap.put("entrance", "Map");

        PageInfo pageInfo = IndustryAnalysisService.AnlzByZsxm(ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    @RequestMapping("taxpayerTaxTrendAnlzMx_bzb")
    @ResponseBody
    public ResultMsg taxpayerTaxTrendAnlzMx_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {


        // 参数处理
        ParameterUtil.handleRequestParam(parameterMap);
        parameterMap.put("entrance", "Map");

        PageInfo pageInfo = IndustryAnalysisService.taxpayerTaxTrendAnlzMx(ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    @RequestMapping("taxStructureAnalysis_five_bzb")
    @ResponseBody
    public ResultMsg taxStructureAnalysis_five_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {

        LineChart bieData = IndustryAnalysisService.taxStructureAnalysis_five(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(bieData);
    }

    @RequestMapping("industryAnalysis_bzb")
    @ResponseBody
    public ResultMsg industryAnalysis_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {

        // 查询条件-行业大类
        String fhydlStr = Optional.ofNullable(parameterMap.get("fhydl"))
                .filter(fhydl -> !StringUtils.isEmpty(fhydl))
                .map(fhydl -> String.valueOf(fhydl).split(","))
                .map(hyList -> Arrays.stream(hyList).collect(Collectors.joining(",", "'", "'")))
                .orElse(null);
        parameterMap.put("fhydlStr", fhydlStr);
        PieChart pieData = IndustryAnalysisService.industryAnalysis(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(pieData);
    }

    @RequestMapping("industryAnalysisV2_bzb")
    @ResponseBody
    public ResultMsg industryAnalysisV2_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {
        // 查询条件-行业大类
        String fhydlStr = Optional.ofNullable(parameterMap.get("fhydl"))
                .filter(fhydl -> !StringUtils.isEmpty(fhydl))
                .map(fhydl -> String.valueOf(fhydl).split(","))
                .map(hyList -> Arrays.stream(hyList).collect(Collectors.joining(",", "'", "'")))
                .orElse(null);
        parameterMap.put("fhydlStr", fhydlStr);
        PieChart pieData = IndustryAnalysisService.industryAnalysisV2(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(pieData);
    }

    @RequestMapping("overallStatisticsByHyml_bzb")
    @ResponseBody
    public ResultMsg overallStatisticsByHyml_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {

        ParameterUtil.handleRequestParam(parameterMap);
        parameterMap.put("entrance", "Map");//解析参数执行入口
        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.overallStatisticsByHyml((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    @RequestMapping("districtAndCountyCompletionTable_v2_bzb")
    @ResponseBody
    public ResultMsg districtAndCountyCompletionTable_v2_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {

        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.districtAndCountyCompletionTable_v2((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }


    @RequestMapping("findTssyInfo_bzb")
    @ResponseBody
    public ResultMsg findTssyInfo_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {
        parameterMap.put("entrance", "Map");//解析参数执行入口
        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.findTssyInfo((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }


    @RequestMapping("findCy_hyml_hydl")
    @ResponseBody
    public ResultMsg findCy_hyml_hydl(@RequestParam Map<String, Object> parameterMap) throws Exception {

        List<HashMap<String,Object>> cy_hyml_hydl = IndustryAnalysisService.findCy_hyml_hydl(String.valueOf(parameterMap.get("fhyml")));
        for (HashMap hashMap : cy_hyml_hydl) {
            String id = String.valueOf(hashMap.get("id"));
            if (StringUtils.isEmpty(id) || !id.contains("cy")) {
                break;
            }
            hashMap.remove("parentid");
        }
        return ResultMsg.success(cy_hyml_hydl);
    }

    @RequestMapping("findZcdjlx")
    @ResponseBody
    public ResultMsg findZcdjlx(@RequestParam Map<String, Object> parameterMap) throws Exception {

        List<HashMap<String,Object>> zcdj = IndustryAnalysisService.findZcdjlx();
        return ResultMsg.success(zcdj);
    }

    @RequestMapping("findYskm")
    @ResponseBody
    public ResultMsg findYskm(@RequestParam Map<String, Object> parameterMap) throws Exception {

        List<HashMap<String,Object>> zcdj = IndustryAnalysisService.findYskm();
        return ResultMsg.success(zcdj);
    }

    @RequestMapping("findFssswjg")
    @ResponseBody
    public ResultMsg findFssswjg(@RequestParam Map<String, Object> parameterMap) throws Exception {

        List<HashMap<String,Object>> zcdj = IndustryAnalysisService.findFssswjg();
        return ResultMsg.success(zcdj);
    }

    @RequestMapping("findZsxm")
    @ResponseBody
    public ResultMsg findZsxm(@RequestParam Map<String, Object> parameterMap) throws Exception {

        List<HashMap<String,Object>> zcdj = IndustryAnalysisService.findZsxm();
        return ResultMsg.success(zcdj);
    }

    //所属地区条件
    @RequestMapping("findFssqy")
    @ResponseBody
    public ResultMsg findFssqy(@RequestParam Map<String, Object> parameterMap) throws Exception {
        return ResultMsg.success(IndustryAnalysisService.findFssqy());
    }

    //所属单位条件
    @RequestMapping("findFssdw")
    @ResponseBody
    public ResultMsg findFssdw(@RequestParam Map<String, Object> parameterMap) throws Exception {
        return ResultMsg.success(IndustryAnalysisService.findFssdw());
    }

    //纳税人状态条件
    @RequestMapping("findFnsrzt")
    @ResponseBody
    public ResultMsg findFnsrzt(@RequestParam Map<String, Object> parameterMap) throws Exception {
        return ResultMsg.success(IndustryAnalysisService.findFnsrzt());

    }


    @RequestMapping("revenueByIndustryMxTable")
    @ResponseBody
    public ResultMsg revenueByIndustryMxTable(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {

        // 请求参数处理
        ParameterUtil.handleRequestParam(parameterMap);
        parameterMap.put("entrance", "Map");
        PageInfo pageInfo = IndustryAnalysisService.revenueByIndustryMxTable((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    /**
     * 明细页面的表格
     *
     * @throws SQLException
     * @throws IOException
     */
    @RequestMapping("revenueByAreaMxTable")
    @ResponseBody
    public ResultMsg revenueByAreaMxTable(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {

        // 请求参数处理
        ParameterUtil.handleRequestParam(parameterMap);
        parameterMap.put("entrance", "Map");
        PageInfo pageInfo = IndustryAnalysisService.revenueByAreaMxTable((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    /**
     * 税收指标分析  整体分析 表格数据
     * Author ：ZhangY
     * Date:2024-03-03
     *
     * @throws Exception
     */
    @RequestMapping("Sszbfxztfx_table_bzb")
    @ResponseBody
    public ResultMsg Sszbfxztfx_table_bzb(@RequestParam Map<String, Object> parameterMap) throws Exception {

        // 请求参数处理
        ParameterUtil.handleRequestParam(parameterMap);
        parameterMap.put("entrance", "Map");
        PageInfo pageInfo = IndustryAnalysisService.Sszbfxztfx_table(ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }


    @RequestMapping("Sszbfxztfx_Chart_bzb")
    @ResponseBody
    public ResultMsg Sszbfxztfx_Chart_bzb(@RequestParam Map<String, Object> parameterMap) throws Exception {

        BarChart barChart = IndustryAnalysisService.Sszbfxztfx_Chart(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(barChart);

    }


    @RequestMapping("Sszbfxztfxfdq_Chart_bzb")
    @ResponseBody
    public ResultMsg Sszbfxztfxfdq_Chart_bzb(@RequestParam Map<String, Object> parameterMap) throws Exception {

        BarChart barChart = IndustryAnalysisService.Sszbfxztfxfdq_Chart(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(barChart);
    }


    @RequestMapping("Sszbfxztfxfsz_Chart_bzb")
    @ResponseBody
    public ResultMsg Sszbfxztfxfsz_Chart_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {

        PieChart pieData = IndustryAnalysisService.Sszbfxztfxfsz_Chart(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(pieData);
    }


    @RequestMapping("Sszbfxztfxfhy_Chart_bzb")
    @ResponseBody
    public ResultMsg Sszbfxztfxfhy_Chart_bzb(@RequestParam Map<String, Object> parameterMap) throws Exception {

        BarChart barChart = IndustryAnalysisService.Sszbfxztfxfhy_Chart(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(barChart);

    }

    @RequestMapping("Sszbfxxzsy_table_bzb")
    @ResponseBody
    public ResultMsg Sszbfxxzsy_table_bzb(@RequestParam Map<String, Object> parameterMap) throws Exception {

        // 请求参数处理
        ParameterUtil.handleRequestParam(parameterMap);
        parameterMap.put("entrance", "Map");
        PageInfo pageInfo = IndustryAnalysisService.Sszbfxxzsy_table(ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    /**
     * 异常税源整体情况分析
     *
     * @throws Exception
     */
    @RequestMapping("Sszbfxycsy_table_bzb")
    @ResponseBody
    public ResultMsg Sszbfxycsy_table_bzb(@RequestParam Map<String, Object> parameterMap) throws Exception {

        // 请求参数处理
        ParameterUtil.handleRequestParam(parameterMap);
        parameterMap.put("entrance", "Map");
        PageInfo pageInfo = IndustryAnalysisService.Sszbfxycsy_table(ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }

    @RequestMapping("Sszbfxztfxsysr_Chart_bzb")
    @ResponseBody
    public ResultMsg Sszbfxztfxsysr_Chart_bzb(@RequestParam Map<String, Object> parameterMap) throws Exception {

        BarChart barChart = IndustryAnalysisService.Sszbfxztfxsysr_Chart(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(barChart);

    }

    @RequestMapping("Sszbfxztfxsyhs_Chart_bzb")
    @ResponseBody
    public ResultMsg Sszbfxztfxsyhs_Chart_bzb(@RequestParam Map<String, Object> parameterMap) throws Exception {

        BarChart barChart = IndustryAnalysisService.Sszbfxztfxsyhs_Chart(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(barChart);

    }

    @RequestMapping("Sszbfxztfxsyfdq_Chart_bzb")
    @ResponseBody
    public ResultMsg Sszbfxztfxsyfdq_Chart_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {

        PieChart pieData = IndustryAnalysisService.Sszbfxztfxsyfdq_Chart(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(pieData);
    }

    @RequestMapping("Sszbfxztfxsysrfdq_Chart_bzb")
    @ResponseBody
    public ResultMsg Sszbfxztfxsysrfdq_Chart_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {

        PieChart pieData = IndustryAnalysisService.Sszbfxztfxsysrfdq_Chart(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(pieData);
    }

    @RequestMapping("Sszbfxztfxsyfhy_Chart_bzb")
    @ResponseBody
    public ResultMsg Sszbfxztfxsyfhy_Chart_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {

        PieChart pieData = IndustryAnalysisService.Sszbfxztfxsyfhy_Chart(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(pieData);
    }

    @RequestMapping("Sszbfxztfxsyfhysr_Chart_bzb")
    @ResponseBody
    public ResultMsg Sszbfxztfxsyfhysr_Chart_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {

        PieChart pieData = IndustryAnalysisService.Sszbfxztfxsyfhysr_Chart(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(pieData);
    }

    @RequestMapping("Sszbfxztfxsyfzclx_Chart_bzb")
    @ResponseBody
    public ResultMsg Sszbfxztfxsyfzclx_Chart_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {

        PieChart pieData = IndustryAnalysisService.Sszbfxztfxsyfzclx_Chart(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(pieData);
    }

    @RequestMapping("Sszbfxztfxsyfzclxsr_Chart_bzb")
    @ResponseBody
    public ResultMsg Sszbfxztfxsyfzclxsr_Chart_bzb(@RequestParam Map<String, Object> parameterMap) throws SQLException, IOException {

        PieChart pieData = IndustryAnalysisService.Sszbfxztfxsyfzclxsr_Chart(ParameterUtil.queryParameterProcessing(parameterMap));
        return ResultMsg.success(pieData);
    }

    @RequestMapping("subIndustryAnalysis_new_bzb")
    @ResponseBody
    public ResultMsg subIndustryAnalysis_new_bzb(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {

        //参数处理
        PageInfo pageInfo = IndustryAnalysisService.subIndustryAnalysis_new_bzb((PageParameter) ParameterUtil.pagingParameter(parameterMap));
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }



}
