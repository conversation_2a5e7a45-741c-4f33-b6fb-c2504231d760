package com.hnbp.zhzs.sszbfx.mapper;


import com.hnbp.common.core.utils.zhzs.QueryCondition;
import com.hnbp.zhzs.sszbfx.model.QueryIndicatorAnalysis;
import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 税收指标分析Mapper
 * @date 2024-11-06
 */
@Mapper
public interface TaxTargetAnalysisMapper {
    List<Map<String, String>> getHyxxAllList(Map<String, Object> parameterMap);

    List<BarSeriesInitData> getIndustryProgressTrendData(Map<String, Object> parameterMap);

    List<Map<String, String>> getIndustryProgress(QueryIndicatorAnalysis queryCondition);

    List<Map<String, String>> getIndustryProgress(Map queryCondition);

    List<Map<String, String>> getTaxReportByIndustry(Map<String, Object> parameterMap);

    List<Map<String, String>> getTaxReportByTax(Map<String, Object> parameterMap);

    List<Map<String, String>> getTaxReportByRegion(Map<String, Object> parameterMap);

    List<Map<String, String>> getSwtjbbTwo(Map<String, Object> parameterMap);

    List<Map<String, String>> getSwtjbbTwoMx(Map<String, Object> parameterMap);

    List<Map<String, String>> getAnalysisOfIndustryTaxBurdenRates(QueryCondition params);

}
