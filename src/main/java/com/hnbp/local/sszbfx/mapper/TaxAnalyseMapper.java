package com.hnbp.local.sszbfx.mapper;

import com.hnbp.local.sszbfx.model.QueryCondition;
import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData;
import com.hnbp.common.core.utils.zhzs.echarts.line.LineSeriesInitData;
import com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartTest;
import com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData;
import com.hnbp.common.core.utils.zhzs.paging.PageParameter;
import org.apache.ibatis.annotations.Mapper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: Mapper
 * @date 2024-09-14
 */
@Mapper
public interface TaxAnalyseMapper {


    List<PieChartTest> queryOverallTaxPicture();

    List<PieChartsSeriesData> overallStreetConditionsChart(QueryCondition queryCondition);

    List<BarSeriesInitData> queryStreetDetails(QueryCondition queryCondition);

    List<BarSeriesInitData> districtAndCountyCompletionChart(QueryCondition queryCondition);

    List<BarSeriesInitData> TjdistrictAndCountyCompletionChart(QueryCondition queryCondition);


    List<Map<String, Object>> districtAndCountyCompletionTable(QueryCondition queryCondition);

    List<Map<String, Object>> TjdistrictAndCountyCompletionTable(QueryCondition queryCondition);


    List<BarSeriesInitData> theTaxSubsidiary(QueryCondition queryCondition);

    List<BarSeriesInitData> areaCountyDetail(QueryCondition queryCondition);

    List<BarSeriesInitData> theIndustryOfSubsidiary(QueryCondition queryCondition);

    List<PieChartsSeriesData> industryAnalysisDetails(QueryCondition queryCondition);
    List<PieChartsSeriesData> industryAnalysisDetailsV2(QueryCondition queryCondition);

    List<Map<String,Object>> regionalTaxSituation(PageParameter pageParameter);

    List<Map<String,Object>> monthlyTaxSituation(PageParameter pageParameter);

    List<Map<String,Object>> industryTaxpayersTable(QueryCondition queryCondition);

    List<Map<String,Object>> zdnsrsscx(PageParameter pageParameter);

    void addNsr(HashMap m);

    void delNsr();

    List<Map<String,Object>> enterpriseAbnormalAnalysis(PageParameter pageParameter);
}
