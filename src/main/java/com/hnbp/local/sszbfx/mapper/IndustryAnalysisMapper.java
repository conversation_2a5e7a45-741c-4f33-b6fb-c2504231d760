package com.hnbp.local.sszbfx.mapper;

import com.hnbp.local.sszbfx.model.QueryCondition;
import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData;
import com.hnbp.common.core.utils.zhzs.echarts.line.LineSeriesInitData;
import com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData;
import com.hnbp.local.sszbfx.model.AreaCountyTax;
import com.hnbp.local.sszbfx.model.CorporateTaxAnalysis.*;
import com.hnbp.local.sszbfx.model.NewTaxSource5Year;
import com.hnbp.local.sszbfx.model.QueryBusinessStatistics;
import com.hnbp.local.sszbfx.model.TaxStatistics;
import com.hnbp.local.sszbfx.model.commonQuery.DetailsOfHouseholdTaxInquiry;
import com.hnbp.local.sszbfx.model.commonQuery.WarehousingGeneralQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 分行业Mapper
 * @date 2024-09-14
 */
@Mapper
public interface IndustryAnalysisMapper {
    List<PieChartsSeriesData> taxStructureAnalysis(QueryCondition queryCondition);
    List<PieChartsSeriesData> taxStructureAnalysisV2(QueryCondition queryCondition);

    List<PieChartsSeriesData> rankingByTaxpayerEchar(QueryCondition queryCondition);

    List<BarSeriesInitData> rankingByTaxpayerEcharline(QueryCondition queryCondition);

    List<TaxStatistics> byTaxStatistics(QueryBusinessStatistics params);

    List<BarSeriesInitData> changesOverTheYears(QueryCondition queryCondition);
    List<BarSeriesInitData> changesOverTheYearsV2(QueryCondition queryCondition);

    List<TaxStatistics> regionalStatistics(QueryBusinessStatistics params);

    List<Map<String,Object>> rankingByTaxpayer(QueryBusinessStatistics params);

    List<Map<String,Object>> findFinancialResources(Map params);

    List<Map<String,Object>> findFinancialResourcesTax(Map params);

    List<Map<String,Object>> subIndustryAnalysis(QueryCondition params);

    List<Map<String,Object>> revenueByTaxTypeTable(Map params);
    List<Map<String,Object>> revenueByTaxTypeTableV2(Map params);

    List<Map<String,Object>> revenueByTaxTypeMxTable(HashMap pagingParameter);

    List<Map<String,Object>> revenueByTaxTypeMxTable2(HashMap pagingParameter);

    List<Map<String,Object>> revenueByTaxTypeMxTableXzsy(HashMap pagingParameter);

    List<Map<String,Object>> subCompaniesIndustryAnalysis(QueryCondition params);

    List<PieChartsSeriesData> taxStructureAnalysis_fsz(QueryCondition queryCondition);

    List<Map<String,Object>> AnlzByZspm(Map params);

    List<Map<String,Object>> AnlzByZsxm(Map params);

    List<Map<String,Object>> taxpayerTaxTrendAnlzMx(Map params);

    List<LineSeriesInitData> taxStructureAnalysis_five(QueryCondition queryCondition);

    List<PieChartsSeriesData> industryAnalysis(QueryCondition queryCondition);
    List<PieChartsSeriesData> industryAnalysisV2(QueryCondition queryCondition);

    List<Map<String,Object>> overallStatisticsByHyml(Map params);

    List<AreaCountyTax> districtAndCountyCompletionTable_v2(HashMap pageParameter);

    List<Map<String,Object>> findTssyInfo(HashMap pageParameter);

    List<Map<String,Object>> statisticsByIndustry(Map params);
    List<Map<String,Object>> statisticsByIndustryV2(Map params);

    List<TaxStatistics> statisticsByRegistrationType(QueryBusinessStatistics pageParameter);

    List<WarehousingGeneralQuery> warehousingGeneralQuery(HashMap pageParameter);

    List<DetailsOfHouseholdTaxInquiry> detailsOfHouseholdTaxInquiry(HashMap pageParameter);

    List<Map<String,Object>> totalTaxRanking(HashMap pageParameter);

    List<TotalTaxRanking> enterpriseProfitChanges(QueryBusinessStatistics pageParameter);

    List<PieChartsSeriesData> VATSectorInformationEcharts(QueryCondition queryCondition);

    List<PieChartsSeriesData> detailsOfVATIndustryEcharts(QueryCondition queryCondition);

    List<DetailsOfVATIndustry> detailsOfVATIndustryTable(Map params);

    List<Map<String,Object>> registeredBusinessGrowthAnalysis(Map params);

    List<AnalysisOfKeyTaxSources> analysisOfKeyTaxSources(QueryBusinessStatistics pageParameter);

    List<PieChartsSeriesData> analysisOfAdditionalItemsEcharts(QueryCondition queryCondition);

    List<PieChartsSeriesData> analysisOfAdditionalItemsDetailEchart(QueryCondition queryCondition);

    List<AnalysisOfAdditionalItems> analysisOfAdditionalItemsTable(Map params);

    List<PieChartsSeriesData> enterpriseIncomeTaxSubsectorsEchart(QueryCondition queryCondition);

    List<PieChartsSeriesData> detailsEnterpriseIncomeTaxSubsectorsEchart(QueryCondition queryCondition);

    List<EnterpriseIncomeTaxSubsectors> detailsEnterpriseIncomeTaxSubsectorsTable(QueryBusinessStatistics params);

    List<HashMap<String,Object>> enterpriseScaleAnalysis(Map params);

    List<HashMap<String,Object>> findCy_hyml_hydl(String fhyml);

    List<HashMap<String,Object>> findZcdjlx();

    List<HashMap<String,Object>> findYskm();

    List<HashMap<String,Object>> findFssswjg();

    List<HashMap<String,Object>> findZsxm();

    List<HashMap<String,Object>> findFssqy();

    List<HashMap<String,Object>> findFssdw();

    List<HashMap<String,Object>> findFnsrzt();

    List<TaxStatistics> statisticsByEconomicType(HashMap pageParameter);

    List<Map<String,Object>> incomeByCity(HashMap pageParameter);

    List<Map<String,Object>> enterpriseScaleAnalysis2(HashMap pageParameter);

    List<Map<String,Object>> enterpriseScaleAnalysisMx(HashMap pageParameter);

    List<Map<String,Object>> revenueByIndustryMxTable(HashMap pageParameter);

    List<Map<String,Object>> revenueByAreaMxTable(HashMap pagingParameter);

    List<Map<String,Object>> Sszbfxztfx_table(HashMap pagingParameter);

    List<BarSeriesInitData> Sszbfxztfx_Chart(QueryCondition queryCondition);

    List<BarSeriesInitData> Sszbfxztfxfdq_Chart(QueryCondition queryCondition);

    List<PieChartsSeriesData> Sszbfxztfxfsz_Chart(QueryCondition queryCondition);

    List<BarSeriesInitData> Sszbfxztfxfhy_Chart(QueryCondition queryCondition);

    NewTaxSource5Year Sszbfxxzsy_table(Map<String,Object> pagingParameter);

    List<Map<String,Object>> Sszbfxycsy_table(HashMap pagingParameter);

    List<BarSeriesInitData> Sszbfxztfxsysr_Chart(QueryCondition queryCondition);

    List<BarSeriesInitData> Sszbfxztfxsyhs_Chart(QueryCondition queryCondition);

    List<PieChartsSeriesData> Sszbfxztfxsyfdq_Chart(QueryCondition queryCondition);

    List<PieChartsSeriesData> Sszbfxztfxsysrfdq_Chart(QueryCondition queryCondition);

    List<PieChartsSeriesData> Sszbfxztfxsyfhy_Chart(QueryCondition queryCondition);

    List<PieChartsSeriesData> Sszbfxztfxsyfhysr_Chart(QueryCondition queryCondition);

    List<PieChartsSeriesData> Sszbfxztfxsyfzclx_Chart(QueryCondition queryCondition);

    List<PieChartsSeriesData> Sszbfxztfxsyfzclxsr_Chart(QueryCondition queryCondition);

    List<Map<String,Object>> subIndustryAnalysis_new_bzb(QueryCondition params);

}
