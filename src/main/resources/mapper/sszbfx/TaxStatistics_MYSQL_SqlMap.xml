<?xml version="1.0" encoding="UTF-8"?><!--

    Copyright 2004-2024 the original author or authors.

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

--><!--Converted at: Sat Sep 14 10:20:56 CST 2024-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.sszbfx.mapper.IndustryAnalysisMapper">

    <!--按税种统计整体分析(饼图)-->
    <select id="taxStructureAnalysis" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select fzsxm name, round(sum(fhj) / 10000, 0) value
        from tb_dw_srfx_srfx_main
        where frkrq between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth}) ,'%Y-%m') and
        last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth}) ,'%Y-%m'))
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        <if test="fldts == 2">AND
            fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        group by fzsxm
        order by value desc
    </select>

    <select id="taxStructureAnalysisV2" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select FSZ name,round(sum(case when substring(frkrq,1,4) = #{start_year} then fje else 0 end) / 10000) value from TB_DW_ZHZS_HYSZ_GEN
        where STR_TO_DATE(frkrq, '%Y-%m') between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth}) ,'%Y-%m')
        and last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth}) ,'%Y-%m'))
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fsz in (${fzsxmStr})
        </if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test ="fhydl != null and fhydl != ''">
            and fhydl in
            <foreach collection="fhydl.split(',')" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        and fsz in ('教育费附加','地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        group by fsz
        order by value desc
    </select>

    <!--按税种统计整体分析(表) byTaxStatistics-->
    <select id="byTaxStatistics" parameterType="com.hnbp.local.sszbfx.model.QueryBusinessStatistics"
            resultType="com.hnbp.local.sszbfx.model.TaxStatistics">
        select
        case when grouping(征收项目) = 1 then '合计' else 征收项目 end fzsxm,
        round(sum(本年累计) / 10000, 2) yeartotal,
        round(sum(上年同期累计) / 10000, 2) lastYeartotal,
        round((sum(本年累计) - sum(上年同期累计)) / 10000, 2) fzje,
        round(sum(今年中央级) / 10000, 2) as fjnqzzyj,
        round(sum(去年中央级) / 10000, 2) as fqnqzzyj,
        round(sum(今年省市级) / 10000, 2) as fjnqzssj,
        round(sum(去年省市级) / 10000, 2) as fqnqzssj,
        round(sum(今年地市级) / 10000, 2) as fjnqzdsj,
        round(sum(去年地市级) / 10000, 2) as fqnqzdsj,
        round(sum(今年区县级) / 10000, 2) as fjnqzqxj,
        round(sum(去年区县级) / 10000, 2) as fqnqzqxj,
        case
        when sum(上年同期累计) = 0 then
        0
        else
        round((sum(本年累计) - sum(上年同期累计)) / sum(上年同期累计), 4) * 100
        end yearOverYear
        from (
        select fzsxm 征收项目,
        sum(case when YEAR(frkrq) = substr(#{fStartRkrq}, 1 , 4) then fhj else 0 end) 本年累计,
        sum(case when YEAR(frkrq) = substr(#{fStartRkrq}, 1 , 4) then fzyj else 0 end) as 今年中央级,
        sum(case when YEAR(frkrq) = substr(#{fStartRkrq}, 1 , 4) then fssj else 0 end) as 今年省市级,
        sum(case when YEAR(frkrq) = substr(#{fStartRkrq}, 1 , 4) then fdsj else 0 end) as 今年地市级,
        sum(case when YEAR(frkrq) = substr(#{fStartRkrq}, 1 , 4) then fqxj else 0 end) as 今年区县级,

        sum(case when YEAR(frkrq) = substr(#{fStartRkrqLastYear}, 1 , 4) then fhj else 0 end) as 上年同期累计 ,
        sum(case when YEAR(frkrq) = substr(#{fStartRkrqLastYear}, 1 , 4) then fzyj else 0 end) as 去年中央级,
        sum(case when YEAR(frkrq) = substr(#{fStartRkrqLastYear}, 1 , 4) then fssj else 0 end) as 去年省市级,
        sum(case when YEAR(frkrq) = substr(#{fStartRkrqLastYear}, 1 , 4) then fdsj else 0 end) as 去年地市级,
        sum(case when YEAR(frkrq) = substr(#{fStartRkrqLastYear}, 1 , 4) then fqxj else 0 end) as 去年区县级
        from tb_dw_srfx_srfx_main
        where 1 = 1
        and (
        frkrq between
        str_to_date(#{fStartRkrq}, '%Y-%m-%d') and
        str_to_date(#{fEndRkrq}, '%Y-%m-%d')
        or
        frkrq between
        str_to_date(#{fStartRkrqLastYear}, '%Y-%m-%d') and
        str_to_date(#{fEndRkrqLastYear}, '%Y-%m-%d')
        )
        <if test="fssqyList != null and fssqyList.size>0">
            and fskgk in
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null and fzsxmList.size>0">
            and fzsxm in
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhydlList != null and fhydlList.size>0">
            and fhydl in
            <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>

        <if test="fldts == 2">AND
            fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        group by fzsxm
        ) a
        where 征收项目 is not null
        group by 征收项目 with rollup
        order by grouping(征收项目) desc, yeartotal desc
    </select>

    <select id="revenueByTaxTypeMxTableXzsy" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
             case when grouping(concat(fshxydm,fnsrmc))=1 then NULL ELSE MAX(fshxydm) END fshxydm
            , case when grouping(concat(fshxydm,fnsrmc))=1 then '合计' ELSE MAX(fnsrmc) END  fnsrmc
            , case when grouping(concat(fshxydm,fnsrmc))=1 then NULL ELSE MAX(fpm) END fpm
            , sum(fshj) fshj, sum(fshj_qn) fshj_qn, sum(fzje) fzje
            , case when grouping(concat(fshxydm,fnsrmc))=1 then
                round(case when sum(fshj_qn) = 0 then 0 else (sum(fshj)-sum(fshj_qn))/sum(fshj_qn)*100 end, 2)
                ELSE MAX(ftb)
            END ftb
        from (
            select t1.fshxydm fshxydm,t1.fnsrmc
            , dense_rank() over(order by fshj is null, fshj desc) as fpm
            , round(coalesce(fshj, 0)/10000,2) fshj
            , round(coalesce(fshj_qn, 0)/10000, 2) fshj_qn
            , round((coalesce(fshj, 0)-coalesce(fshj_qn, 0))/10000, 2) fzje
            , round(case when fshj_qn = 0 then 0 else (fshj-fshj_qn)/fshj_qn*100 end, 2) ftb
            from (
            select fshxydm, fnsrmc
            from tb_dm_zhzs_xzsymd_gen
            where fxzrq is not null
            and fswdjrq between str_to_date(substr(#{fzcnyqz},1,7),'%Y-%m')
            and last_day(to_date(substr(#{fzcnyqz},-7,7),'%Y-%m'))
            <if test="fcode_ss == 1">
                and fdjzclx not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
                ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
                ,'民办非企业单位（个体）','农村集体经济组织')
            </if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
            <if test="fssqyList != null">
                AND fjdxz IN
                <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="fzcdjlxStr != null and fzcdjlxStr != ''">AND
                fdjzclx in (${fzcdjlxStr})
            </if>
            <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
            <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
                and region_id in
                <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>
            group by fshxydm, fnsrmc
            ) t1
            left join
            (
            select fnsrmc
            , fnsrsbh
            , sum(case when year (frkrq) = substr(#{frkrq},1,4) then case when #{ftype} = 'qkj' then fshj else fdfsr
            end else 0 end ) fshj
            , sum(case when year (frkrq) = substr(#{frkrq},1,4)-1 then case when #{ftype} = 'qkj' then fshj else fdfsr
            end else 0 end ) fshj_qn
            from tb_dw_srfx_srfx_main
            where 1 = 1
            <if test="fldts == 2">
                AND fyskm NOT LIKE '%留抵退%'
            </if>
            <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
            <if test="fssqyList != null">AND
                fskgk IN
                <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="fzcdjlxStr != null and fzcdjlxStr !=''">AND
                fdjzclx in (${fzcdjlxStr})
            </if>
            <if test="fhydlList != null">AND
                fhydl IN
                <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
            and (
        frkrq between date_sub(str_to_date(concat(substr(#{frkrq},1,7), '-01'), '%Y-%m-%d'), INTERVAL 1 year)
        and date_sub(last_day(str_to_date(concat(substr(#{frkrq},-7,7), '-01'), '%Y-%m-%d')),INTERVAL 1 year)
        or
        frkrq between str_to_date(concat(substr(#{frkrq},1,7), '-01'), '%Y-%m-%d')
        and last_day(str_to_date(substr(#{frkrq},-7,7), '%Y-%m'))
            )
            and fnsrsbh in (
                select fshxydm
                from tb_dm_zhzs_xzsymd_gen
                where fxzrq is not null
                and fswdjrq between str_to_date(substr(#{fzcnyqz},1,7),'%Y-%m')
                and last_day(str_to_date(substr(#{fzcnyqz},-7,7),'%Y-%m'))
                group by fshxydm
            )
            and fzsxm in ('增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
            group by fnsrmc, fnsrsbh
            ) t2
            on t1.fshxydm = t2.fnsrsbh and t1.fnsrmc = t2.fnsrmc
            where 1=1
            <if test="fsrlx == &quot;大于一亿&quot;">and
                fshj &gt;= 10000*10000
            </if>
            <if test="fsrlx == &quot;五千万至一亿&quot;">and
                fshj &gt;= 5000*10000 AND fshj &lt; 10000*10000
            </if>
            <if test="fsrlx == &quot;一千万至五千万&quot;">and
                fshj &gt;= 1000*10000 AND fshj &lt; 5000*10000
            </if>
            <if test="fsrlx == &quot;五百万至一千万&quot;">and
                fshj &gt;= 500*10000 AND fshj &lt; 1000*10000
            </if>
            <if test="fsrlx == &quot;五十万至五百万&quot;">and
                fshj &gt;= 50*10000 AND fshj &lt; 500*10000
            </if>
            <if test="fsrlx == &quot;小于五十万&quot;">and
                fshj &lt; 50*10000
            </if>
        ) tmp1
        <where>
            <if test="fpmLimit != null and fpmLimit != ''">AND
                fpm &lt;= #{fpmLimit} *1
            </if>
        </where>
        group by (concat(fshxydm,fnsrmc)) with rollup
        order by grouping(concat(fshxydm,fnsrmc)) desc, fpm asc
    </select>


    <!--按税种统计整体分析(历年分月柱状图)-->
    <select id="changesOverTheYears" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData">
        select round(sum(fhj) / 10000, 0) yAxis,
        to_char(frkrq,'yyyy') legend,
        to_char(frkrq,'mm') xAxis
        from tb_dw_srfx_srfx_main
        where
        frkrq between str_to_date(CONCAT(#{start_year} - 3,'-','01','-','01'),'%Y-%m-%d') and
        str_to_date(CONCAT(#{start_year},'-','12','-','31'),'%Y-%m-%d')
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhymlStr != null and fhymlStr != ''">and
            fhydl in (${fhymlStr})
        </if>
        <if test="fldts == 2">AND
            fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>

        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        group by to_char(frkrq,'yyyy'), to_char(frkrq,'mm')
        order by legend desc, xAxis asc
    </select>

    <select id="changesOverTheYearsV2" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData">
        select round(sum(fje)/10000,0) yAxis,
        SUBSTR(frkrq, 1, 4) legend,
        SUBSTR(frkrq, 6, 7) xAxis from TB_DW_ZHZS_HYSZ_GEN
        where
        STR_TO_DATE(frkrq, '%Y-%m') between str_to_date(CONCAT(#{start_year} - 3,'-','01','-','01'),'%Y-%m-%d') and
        str_to_date(CONCAT(#{start_year},'-','12','-','31'),'%Y-%m-%d')
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fsz in (${fzsxmStr})
        </if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test ="fhydl != null and fhydl != ''">
            and fhydl in
            <foreach collection="fhydl.split(',')" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        and fsz in ('教育费附加','地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        group by SUBSTRING(frkrq, 1, 4), SUBSTRING(frkrq, 6, 7)
        order by legend desc, xAxis asc
    </select>


    <!--按地区统计  regionalStatistics-->
    <select id="regionalStatistics" parameterType="com.hnbp.local.sszbfx.model.QueryBusinessStatistics"
            resultType="com.hnbp.local.sszbfx.model.TaxStatistics">
        select
        case when grouping (收款国库)=1 then '合计' else 收款国库 end fskgk,
        grouping(收款国库) fpx,
        round((sum(今年累计)) / 10000, 2) yeartotal,
        round((sum(今年累计) - sum(去年累计)) / 10000, 2) fzje,
        round(sum(去年累计) / 10000, 2) lastYeartotal,
        case
        when sum(去年累计) = 0 then
        0
        else
        round((sum(今年累计) - sum(去年累计)) / sum(去年累计), 4) * 100
        end yearOverYear,
        round(sum(今年中央级) / 10000, 2) as fjnqzzyj,
        round(sum(去年中央级) / 10000, 2) as fqnqzzyj,
        round(sum(今年省市级) / 10000, 2) as fjnqzssj,
        round(sum(去年省市级) / 10000, 2) as fqnqzssj,
        round(sum(今年地市级) / 10000, 2) as fjnqzdsj,
        round(sum(去年地市级) / 10000, 2) as fqnqzdsj,
        round(sum(今年区县级) / 10000, 2) as fjnqzqxj,
        round(sum(去年区县级) / 10000, 2) as fqnqzqxj
        from (select fskgk 收款国库,
        round(sum(fhj), 2) 今年累计,
        0 as 去年累计,
        sum(fzyj) 今年中央级,
        0 as 去年中央级,
        sum(fssj) 今年省市级,
        0 as 去年省市级,
        sum(fdsj) 今年地市级,
        0 as 去年地市级,
        sum(fqxj) 今年区县级,
        0 as 去年区县级
        from tb_dw_srfx_srfx_main
        where 1 = 1
        <if test="fssqyList != null and fssqyList.size>0">
            and fskgk in
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null and fzsxmList.size>0">
            and fzsxm in
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhydlList != null and fhydlList.size>0">
            and fhydl in
            <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>

        <if test="fStartSkssqq != null and fStartSkssqq != ''">and
            fskssqq between
            str_to_date(#{fStartSkssqq}, '%Y-%m-%d') and
            str_to_date(#{fEndSkssqq}, '%Y-%m-%d')
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">and
            frkrq between
            str_to_date(#{fStartRkrq}, '%Y-%m-%d') and
            str_to_date(#{fEndRkrq}, '%Y-%m-%d')
        </if>
        group by fskgk
        union all
        select fskgk,
        0 as 今年累计,
        round(sum(fhj), 2) 去年累计,
        0 as 今年中央级,
        sum(fzyj) as 去年中央级,
        0 as 今年省市级,
        sum(fssj) as 去年省市级,
        0 as 今年地市级,
        sum(fdsj) as 去年地市级,
        0 as 今年区县级,
        sum(fqxj) as 去年区县级
        from tb_dw_srfx_srfx_main
        where 1 = 1
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        <if test="fStartSkssqqLastYear != null and fStartSkssqqLastYear != ''">and
            fskssqq between
            str_to_date(#{fStartSkssqqLastYear}, '%Y-%m-%d') and
            str_to_date(#{fEndSkssqqLastYear}, '%Y-%m-%d')
        </if>
        <if test="fStartRkrqLastYear != null and fStartRkrqLastYear != ''">and
            frkrq between
            str_to_date(#{fStartRkrqLastYear}, '%Y-%m-%d') and
            str_to_date(#{fEndRkrqLastYear}, '%Y-%m-%d')
        </if>
        group by fskgk) as a
        where 收款国库 is not null
        group by 收款国库 with rollup
        order by fpx desc,yeartotal desc
    </select>


    <!--按纳税人排名统计  RankingByTaxpayer-->
    <select id="rankingByTaxpayer" parameterType="com.hnbp.local.sszbfx.model.QueryBusinessStatistics"
            resultType="java.util.Map">
        WITH
        topN AS (
            SELECT FNSRMC
                , SUM(CASE WHEN #{frkkj} = '全口径税收' THEN FHJ WHEN #{frkkj} = '地方税收' THEN FDFSR END) 今年税收合计
                , SUM(CASE WHEN MONTH(FRKRQ) = #{fendmonth} THEN CASE WHEN #{frkkj} = '全口径税收' THEN FHJ WHEN #{frkkj} = '地方税收' THEN FDFSR END ELSE 0 END) 今年当月税收合计
            FROM tb_dw_srfx_srfx_main
            WHERE FRKRQ between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth},'-','01'),'%Y-%m-%d') and last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth}),'%Y-%m'))
            <!-- and FNSRMC not like '%电子税票%' and FNSRMC not like '%退库%' and FNSRMC not like '%更正%' and FNSRMC not like '%留底退%' -->
            <if test="fssqyList != null and fssqyList.size>0">
                and fskgk in
                <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="fzsxmList != null and fzsxmList.size>0">
                and fzsxm in
                <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="fhydlList != null and fhydlList.size>0">
                and fhydl in
                <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>

            <if test="fnsrmc != null and fnsrmc != ''">
                and fnsrmc like CONCAT('%' ,#{fnsrmc},'%')
            </if>
            <if test="fldts ==2">
                and fyskm not like '%留抵退%'
            </if>
            <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
            <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
            <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
                and region_id in
                <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>
            GROUP BY FNSRMC
            <if test="fqsje != null and fqsje != ''">
                HAVING SUM(CASE WHEN #{frkkj} = '全口径税收' THEN FHJ WHEN #{frkkj} = '地方税收' THEN FDFSR END) >= ${fqsje}*10000
            </if>
            ORDER BY 今年税收合计 DESC
            <if test="fnsrpm != null and fnsrpm != ''">
                LIMIT #{fnsrpm}
            </if>
        )
        , ssQn AS (
            SELECT topN.FNSRMC
                , SUM(CASE WHEN #{frkkj} = '全口径税收' THEN FHJ WHEN #{frkkj} = '地方税收' THEN FDFSR END) 去年税收合计
                , SUM(CASE WHEN MONTH(FRKRQ) = #{fendmonth} THEN CASE WHEN #{frkkj} = '全口径税收' THEN FHJ WHEN #{frkkj} = '地方税收' THEN FDFSR END ELSE 0 END) 去年当月税收合计
            FROM topN
            LEFT JOIN tb_dw_srfx_srfx_main srfx ON topN.FNSRMC = srfx.FNSRMC
            WHERE FRKRQ between str_to_date(CONCAT(#{start_year}-1,'-',#{fstartmonth},'-','01'),'%Y-%m-%d') and last_day(str_to_date(CONCAT(#{start_year}-1,'-',#{fendmonth}),'%Y-%m'))
            <if test="fssqyList != null and fssqyList.size>0">
                and srfx.fskgk in
                <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="fzsxmList != null and fzsxmList.size>0">
                and srfx.fzsxm in
                <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="fhydlList != null and fhydlList.size>0">
                and srfx.fhydl in
                <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="fnsrmc != null and fnsrmc != ''">
                and srfx.fnsrmc like CONCAT('%' ,#{fnsrmc},'%')
            </if>
            <if test="fldts ==2">
                and srfx.fyskm not like '%留抵退%'
            </if>
            <if test="fmdts == 2">AND srfx.fyskm != '免抵调增增值税'</if>
            <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
            <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
                and region_id in
                <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>
            GROUP BY topN.FNSRMC
        )
        SELECT
            CASE WHEN GROUPING(FNSRMC)=1 THEN '合计' ELSE FNSRMC END fnsrmc
            , CASE WHEN GROUPING(FNSRMC)=1 THEN NULL ELSE MAX(fdesc) END fdesc
            , GROUPING(FNSRMC) fgrouping
            , ROUND(SUM(今年税收合计)/10000, 2) fjnsjrk
            , ROUND(SUM(今年当月税收合计)/10000, 2) fjndylx
            , ROUND(SUM(去年税收合计)/10000, 2) fqnsjrk
            , ROUND(SUM(去年当月税收合计)/10000, 2) fqndylx
            , ROUND((SUM(今年税收合计)-SUM(去年税收合计))/10000, 2) fzje
            , ROUND((SUM(今年当月税收合计)-SUM(去年当月税收合计))/10000, 2) mounthfzje
            , CASE WHEN SUM(去年当月税收合计)=0 THEN 100 ELSE ROUND((SUM(今年当月税收合计)-SUM(去年当月税收合计))/SUM(去年当月税收合计)*100, 2) END mounthyearoveryear
            , CASE WHEN SUM(去年税收合计)=0 THEN 100 ELSE ROUND((SUM(今年税收合计)-SUM(去年税收合计))/SUM(去年税收合计)*100, 2) END yearoveryear
        FROM (
            SELECT topN.FNSRMC, 今年税收合计, 今年当月税收合计, 去年税收合计, 去年当月税收合计
                , DENSE_RANK() OVER(ORDER BY 今年税收合计 DESC) fdesc
            FROM topN LEFT JOIN ssQn ON topN.FNSRMC = ssQn.FNSRMC
        ) tmp1
        GROUP BY FNSRMC WITH ROLLUP
        ORDER BY FGROUPING DESC, fjnsjrk DESC
    </select>


    <select id="findFinancialResources" parameterType="java.util.Map" resultType="java.util.Map">
        select ifnull(sum(fhj),0) fhj, ifnull(sum(fdwnsr),0) fdwnsr, ifnull(sum(fgth),0) fgth, ifnull(sum(fwbnsr),0) fwbnsr
        , ifnull(sum(fqt),0) fqt
        from (select fkzztdjlx
                   , count(fnsrmc)                                           fhj
                   , IF(fkzztdjlx = '单位纳税人税务登记', count(1), 0)       fdwnsr
                   , IF(fkzztdjlx = '个体经营纳税人税务登记', count(1), 0)   fgth
                   , IF(fkzztdjlx = '外埠纳税人经营地报验登记', count(1), 0) fwbnsr
                   , CASE
                         when fkzztdjlx not in ('单位纳税人税务登记', '个体经营纳税人税务登记', '外埠纳税人经营地报验登记')
                             THEN count(1)
                         ELSE 0 END                                          fqt
              from (select fkzztdjlx, fnsrmc, fdjrq, row_number() over(partition by fnsrmc order by fdjrq desc) rn
                    from tb_dw_srfx_swdjxx_main
                    where fnsrzt = '正常'
                    <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
                    <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
                        and region_id in
                        <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                            #{item}
                        </foreach>
                    </if>
                    union all
                    select '外埠纳税人经营地报验登记', fnsrmc, fdjrq, '1'
                    from tb_dw_srfx_wbnsr_gen
                    where 1=1
                    <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
                    <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
                        and region_id in
                        <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                            #{item}
                        </foreach>
                    </if>
                    ) tmp1
              where rn = 1
                and fdjrq &lt; #{fjzny}
              group by fkzztdjlx) tmp2
    </select>

    <select id="findFinancialResourcesTax" parameterType="java.util.Map" resultType="java.util.Map">

        select ifnull(flx,''), ifnull(round(sum(fsshj), 2), 0) fsshj
        from (select fnsrmc,
                     CASE fkzztdjlx
                         WHEN '个体经营纳税人税务登记' THEN '个体户'
                         WHEN '外埠纳税人经营地报验登记' THEN '外埠纳税人'
                         WHEN '单位纳税人税务登记' THEN '单位纳税人'
                         ELSE '其他纳税人' END flx
              from (select fkzztdjlx, fnsrmc, fdjrq, row_number() over(partition by fnsrmc order by fdjrq desc) rn
                    from tb_dw_srfx_swdjxx_main
                    where fnsrzt = '正常'
                    <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
                    <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
                        and region_id in
                        <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                            #{item}
                        </foreach>
                    </if>
                    union all
                    select '外埠纳税人经营地报验登记', fnsrmc, fdjrq, '1'
                    from tb_dw_srfx_wbnsr_gen
                    where 1=1
                    <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
                    <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
                        and region_id in
                        <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                            #{item}
                        </foreach>
                    </if>) tmp1
              where rn = 1
                and fdjrq &lt; #{fjzny}) a
                 left join
             (select fnsrmc 纳税人名称, sum(fhj) / 10000 fsshj
              from tb_dw_srfx_srfx_main
              where year (frkrq)=${fssnf}
                <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
                <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
                    and region_id in
                    <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                        #{item}
                    </foreach>
                </if>
              group by fnsrmc) b
             on a.fnsrmc = b.纳税人名称
        group by flx
    </select>

    <select id="findTssyInfo" parameterType="java.util.Map" resultType="java.util.Map">

        select case when grouping(fqymc) = 1 then '合计' else fqymc end as fqymc
        ,round(ifnull(sum(fshj),'0'),2) fshj,round(ifnull(sum(fdfsr),'0'),2) fdfsr
        ,round(ifnull(sum(flzs),'0'),2) flzs
        ,round(ifnull(sum(fsds),'0'),2) fsds
        ,round(ifnull(sum(fccxwzys),'0'),2) fccxwzys
        ,round(ifnull(sum(fshj_qn),'0'),2) fshj_qn,round(ifnull(sum(fdfsr_qn),'0'),2) fdfsr_qn
        ,round(ifnull(sum(fshj),'0')-ifnull(sum(fshj_qn),'0'),2) fshj_zje
        ,round(ifnull(sum(fdfsr),'0')-ifnull(sum(fdfsr_qn),'0'),2) fdfsr_zje
        ,case when ifnull(sum(fshj_qn),'0') = '0' then 0 when ifnull(sum(fshj_qn),'') = ''
        then 0 else round((sum(fshj)-sum(fshj_qn))/sum(fshj_qn)*100,2) end fshj_tb
        ,case when ifnull(sum(fdfsr_qn),'0') = '0' then 0 when ifnull(sum(fdfsr_qn),'') = ''
        then 0 else round((sum(fdfsr)-sum(fdfsr_qn))/sum(fdfsr_qn)*100,2) end fdfsr_tb
        from
        (
        select distinct fqymc from (
        select t1.fqymc,fhy,fskgk,t3.REGION_ID from (select distinct fnsrmc fqymc from tb_dw_zhzs_tssymdb_gen
        where flx = #{ftablename}
        <if test="fnsrmc != null and fnsrmc != ''">and
            fnsrmc = #{fnsrmc}
        </if>) t1
        left join tb_dw_srfx_swdjxx_main t2 on t2.fnsrmc = t1.fqymc
        left join (select distinct fnsrmc,fskgk,REGION_ID from tb_dw_srfx_srfx_main) t3 on t3.fnsrmc = t1.fqymc
        ) tmp
        where 1=1
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhy in (${fhydlStr})
        </if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        ) t1
        left join
        (
        select fnsrmc,fnsrsbh,sum(fshj)/10000 fshj,sum(fdfsr)/10000 fdfsr
        ,sum(fzzs+fxfs+fyys)/10000 flzs
        ,sum(fgrsds+fqysds)/10000 fsds
        ,sum(FGDZYS+fyhs+fqs+fccs+ffcs+fcztdsys)/10000 fccxwzys
        from tb_dm_zhzs_ssmd_main
        where frkrq between STR_TO_DATE(concat(#{start_year},'-',#{fstartmonth},'-01'),'%Y-%m-%d')
        and last_day(STR_TO_DATE(concat(#{start_year},'-',#{fendmonth}),'%Y-%m-%d'))
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        group by fnsrmc
        ) t2
        on t1.fqymc = t2.fnsrmc
        left join
        (
        select fnsrmc,fnsrsbh,sum(fshj)/10000 fshj_qn,sum(fdfsr)/10000 fdfsr_qn from tb_dm_zhzs_ssmd_main
        where frkrq between DATE_SUB(STR_TO_DATE(concat(#{start_year}-1,'-',#{fstartmonth},'-01'),'%Y-%m-%d'),INTERVAL 12
        MONTH)
        and
        DATE_SUB(STR_TO_DATE(last_day(STR_TO_DATE(concat(#{start_year}-1,'-',#{fendmonth}),'%Y-%m-%d')),'%Y-%m-%d'),INTERVAL
        12 MONTH)
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        group by fnsrmc
        ) t3
        on t1.fqymc = t3.fnsrmc
        group by fqymc with rollup
        order by case when fqymc = '合计' then 0 else 1 end,fshj_zje desc
    </select>


    <select id="subIndustryAnalysis" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="java.util.HashMap">
        select case when fname is null then '合计' else fname end fname
        ,round(nvl(sum(fsshjjn),0),2) fsshjjn
        ,round(nvl(sum(fdfsr),0),2) fdfsr
        ,round(nvl(sum(fsshjqn),0),2) fsshjqn,
        round(nvl(sum(fsshjjn),0)-nvl(sum(fsshjqn),0),2) fzj
        ,round(case nvl(sum(fsshjqn),0) when 0 then 100 else
        (nvl(sum(fsshjjn),0)-nvl(sum(fsshjqn),0))/nvl(sum(fsshjqn),0) * 100 end,2) fbl
        from
        (
        select fname
        , sum(case when fyear = #{start_year} then fhj else 0 end)/10000 fsshjjn
        , sum(case when fyear = #{ago_year} then fhj else 0 end)/10000 fsshjqn
        , sum(case when fyear = #{start_year} then fdfsr else 0 end)/10000 fdfsr
        from (
        select
        <choose>
            <when test='type == "行业"'>fhyml</when>
            <when test='type == "地区"'>fskgk</when>
            <when test='type == "税种"'>fzsxm</when>
        </choose>
        fname
        , YEAR(frkrq) AS fyear
        , sum(fhj) fhj
        , sum(fdfsr) fdfsr
        from tb_dw_srfx_srfx_main
        where
        (
        frkrq between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth},'-','01'),'%Y-%m-%d')
        and last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth},'-','01'),'%Y-%m-%d'))
        or
        frkrq between str_to_date(CONCAT(#{ago_year},'-',#{fstartmonth},'-','01'),'%Y-%m-%d')
        and last_day(str_to_date(CONCAT(#{ago_year},'-',#{fendmonth},'-','01'),'%Y-%m-%d'))
        )
        <if test="fssqyStr != null and fssqyStr != ''">
            and fskgk in (${fssqyStr})
        </if>
        <if test="fzsxmStr != null and fzsxmStr!= ''">
            and fzsxm in (${fzsxmStr})
        </if>
        <if test="fhydlStr != null and fhydlStr!= ''">
            and fhydl in (${fhydlStr})
        </if>
        <if test="fldts== 2">
            AND fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
            and fzsxm in ('教育费附加',
            '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
            group by
            <choose>
                <when test='type == "行业"'>fhyml</when>
                <when test='type == "地区"'>fskgk</when>
                <when test='type == "税种"'>fzsxm</when>
            </choose>
            , YEAR(frkrq)
        ) tmp1 GROUP BY fname
        ) ss
        where
        case
        when #{ftype} = '增长'
        and ifnull(fsshjjn, 0) &gt;= ifnull(fsshjqn, 0)
        then 'ok'
        when #{ftype} = '下降'
        and ifnull(fsshjqn, 0) &gt;= ifnull(fsshjjn, 0)
        then 'ok'
        else 'err'
        end = 'ok'
        group by fname with rollup
        order by grouping(fname) desc
        , case
        when #{ftype} = '增长'
        then ifnull(fsshjjn, 0) - ifnull(fsshjqn, 0)
        when #{ftype} = '下降'
        then ifnull(fsshjqn, 0) - ifnull(fsshjjn, 0)
        end
        desc
    </select>

    <select id="revenueByTaxTypeTable" parameterType="hashmap" resultType="hashmap">
        select * from (select case when grouping(fzsxm) = 1 then '合计' else fzsxm end fzsxm,
        round(SUM(fhj), 2) fhj,
        round(SUM(fzyj), 2) fzyj,
        round(SUM(fssj), 2) fssj,
        round(SUM(fdsj), 2) fdsj,
        round(SUM(fqxj), 2) fqxj,
        round(SUM(fhj_qn), 2) fhj_qn,
        round(SUM(fzyj_qn), 2) fzyj_qn,
        round(SUM(fssj_qn), 2) fssj_qn,
        round(SUM(fdsj_qn), 2) fdsj_qn,
        round(SUM(fqxj_qn), 2) fqxj_qn,
        round(ifnull(SUM(fhj), 0) - ifnull(SUM(fhj_qn), 0), 2) fzje_hj,
        case when ifnull(SUM(fhj_qn), 0) = 0 then null
        else round((ifnull(SUM(fhj), 0) - ifnull(SUM(fhj_qn), 0)) / ifnull(SUM(fhj_qn), 0) * 100, 2)
        end fzjb_hj,
        round(ifnull(SUM(fqxj), 0) - ifnull(SUM(fqxj_qn), 0), 2) fzje_qxj,
        case when ifnull(SUM(fqxj_qn), 0) = 0 then null
        else round((ifnull(SUM(fqxj), 0) - ifnull(SUM(fqxj_qn), 0)) / ifnull(SUM(fqxj_qn), 0) * 100, 2)
        end fzjb_qxj
        from (
        SELECT
        fzsxm,
        SUM(case when year(
        <if test="fStartSkssqq != null and fStartSkssqq != ''">fskssqq</if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">frkrq</if>) = #{start_year} then srfx.fhj else 0
        end)/10000 fhj,
        SUM(case when year(
        <if test="fStartSkssqq != null and fStartSkssqq != ''">fskssqq</if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">frkrq</if>) = #{start_year} then srfx.fzyj else 0
        end)/10000 fzyj,
        SUM(case when year(
        <if test="fStartSkssqq != null and fStartSkssqq != ''">fskssqq</if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">frkrq</if>) = #{start_year} then srfx.fssj else 0
        end)/10000 fssj,
        SUM(case when year(
        <if test="fStartSkssqq != null and fStartSkssqq != ''">fskssqq</if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">frkrq</if>) = #{start_year} then srfx.fdsj else 0
        end)/10000 fdsj,
        SUM(case when year(
        <if test="fStartSkssqq != null and fStartSkssqq != ''">fskssqq</if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">frkrq</if>) = #{start_year} then srfx.fqxj else 0
        end)/10000 fqxj,
        SUM(case when year(
        <if test="fStartSkssqq != null and fStartSkssqq != ''">fskssqq</if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">frkrq</if>) = #{start_year} - 1 then srfx.fhj else 0
        end)/10000 fhj_qn,
        SUM(case when year(
        <if test="fStartSkssqq != null and fStartSkssqq != ''">fskssqq</if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">frkrq</if>) = #{start_year} - 1 then srfx.fzyj else 0
        end)/10000 fzyj_qn,
        SUM(case when year(
        <if test="fStartSkssqq != null and fStartSkssqq != ''">fskssqq</if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">frkrq</if>) = #{start_year} - 1 then srfx.fssj else 0
        end)/10000 fssj_qn,
        SUM(case when year(
        <if test="fStartSkssqq != null and fStartSkssqq != ''">fskssqq</if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">frkrq</if>) = #{start_year} - 1 then srfx.fdsj else 0
        end)/10000 fdsj_qn,
        SUM(case when year(
        <if test="fStartSkssqq != null and fStartSkssqq != ''">fskssqq</if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">frkrq</if>) = #{start_year} - 1 then srfx.fqxj else 0
        end)/10000 fqxj_qn
        FROM tb_dw_srfx_srfx_main srfx
        WHERE 1=1
        <if test="fStartSkssqq != null and fStartSkssqq != ''">and
            (
            srfx.fskssqq BETWEEN #{fStartSkssqq} and #{fEndSkssqq}
            OR srfx.fskssqq BETWEEN #{fStartSkssqqLastYear} AND #{fEndSkssqqLastYear}
            )
        </if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">and
            (srfx.frkrq BETWEEN CONCAT(#{start_year}, '-', #{fstartmonth}, '-01') AND
            last_day(str_to_date(CONCAT(#{start_year}, '-', #{fendmonth}), '%Y-%m'))
            OR srfx.frkrq BETWEEN CONCAT(#{start_year} - 1, '-', #{fstartmonth}, '-01') AND
            last_day(str_to_date(CONCAT(#{start_year} - 1, '-', #{fendmonth}), '%Y-%m'))
            )
        </if>
        <if test="fssqyList != null">AND
            fskgk IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null">AND
            fzsxm IN
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhydlList != null">AND
            fhydl IN
            <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzcdjlxStr != null and fzcdjlxStr != ''">AND
            fdjzclx in (${fzcdjlxStr})
        </if>
        <if test="fldts == 2">AND
            fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        GROUP BY fzsxm
        ) a
        GROUP BY fzsxm WITH ROLLUP
        ) a
        order by case when fzsxm = '合计' then 1 else 0 end desc ,fhj desc
    </select>

    <select id="revenueByTaxTypeTableV2" parameterType="hashmap" resultType="hashmap">

        select * from (select case when fzsxm is null then '合计' else fzsxm end fzsxm,
        round(SUM(fhj), 2) fhj,
        round(SUM(fzyj), 2) fzyj,
        round(SUM(fssj), 2) fssj,
        round(SUM(fdsj), 2) fdsj,
        round(SUM(fqxj), 2) fqxj,
        round(SUM(fhj_qn), 2) fhj_qn,
        round(SUM(fzyj_qn), 2) fzyj_qn,
        round(SUM(fssj_qn), 2) fssj_qn,
        round(SUM(fdsj_qn), 2) fdsj_qn,
        round(SUM(fqxj_qn), 2) fqxj_qn,
        round(ifnull(SUM(fhj), 0) - ifnull(SUM(fhj_qn), 0), 2) fzje_hj,
        case when ifnull(SUM(fhj_qn), 0) = 0 then null
        else round((ifnull(SUM(fhj), 0) - ifnull(SUM(fhj_qn), 0)) / ifnull(SUM(fhj_qn), 0) * 100, 2)
        end fzjb_hj,
        round(ifnull(SUM(fqxj), 0) - ifnull(SUM(fqxj_qn), 0), 2) fzje_qxj,
        case when ifnull(SUM(fqxj_qn), 0) = 0 then null
        else round((ifnull(SUM(fqxj), 0) - ifnull(SUM(fqxj_qn), 0)) / ifnull(SUM(fqxj_qn), 0) * 100, 2)
        end fzjb_qxj
        from (


        select fsz fzsxm ,
        SUM(case when SUBSTR(frkrq, 1, 4) = #{start_year} then fje else 0
        end)/10000 fhj,
        SUM(case when SUBSTR(frkrq, 1, 4) = #{start_year} then fje else 0
        end)/10000 * ( fzyj / 100 ) fzyj,
        SUM(case when SUBSTR(frkrq, 1, 4) = #{start_year} then fje else 0
        end)/10000 * ( fssj / 100 )   fssj,
        SUM(case when SUBSTR(frkrq, 1, 4) = #{start_year} then fje else 0
        end)/10000 * ( fdsj / 100 )  fdsj,
        SUM(case when SUBSTR(frkrq, 1, 4) = #{start_year} then fje else 0
        end)/10000 * ( fqxj / 100 )  fqxj,
        SUM(case when SUBSTR(frkrq, 1, 4) = #{start_year} - 1 then fje else 0
        end)/10000 fhj_qn,
        SUM(case when SUBSTR(frkrq, 1, 4) = #{start_year} - 1 then fje else 0
        end)/10000 fzyj_qn,
        SUM(case when SUBSTR(frkrq, 1, 4) = #{start_year} - 1 then fje else 0
        end)/10000 fssj_qn,
        SUM(case when SUBSTR(frkrq, 1, 4) = #{start_year} - 1 then fje else 0
        end)/10000 fdsj_qn,
        SUM(case when SUBSTR(frkrq, 1, 4) = #{start_year} - 1 then fje else 0
        end)/10000 fqxj_qn

        from TB_DW_ZHZS_HYSZ_GEN         WHERE 1=1
        and (
        str_TO_DATE(frkrq, '%Y-%m') BETWEEN CONCAT(#{start_year}, '-', #{fstartmonth}, '-01') AND
        last_day(str_to_date(CONCAT(#{start_year}, '-', #{fendmonth}), '%Y-%m'))
        OR str_TO_DATE(frkrq, '%Y-%m') BETWEEN CONCAT(#{start_year} - 1, '-', #{fstartmonth}, '-01') AND
        last_day(str_to_date(CONCAT(#{start_year} - 1, '-', #{fendmonth}), '%Y-%m'))
        )
        <if test="fzsxmStr != null and fzsxmStr != ''">AND
            fsz IN (${fzsxmStr})
        </if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test ="fhydl != null and fhydl != ''">
            and fhydl in
            <foreach collection="fhydl.split(',')" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        and fsz in
        ('教育费附加','地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        group by  fsz,fqxj,fdsj,fssj,fzyj
        order by case when fsz = '合计' then 1 else 0 end desc ,sum(fje) desc

        ) a
        GROUP BY fzsxm with rollup
        ) aa
        order by case when fzsxm = '合计' then 1 else 0 end desc ,fhj desc
    </select>

    <select id="revenueByTaxTypeMxTable" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT FNSRSBH, FNSRMC 纳税人名称, fhyml, fpx,
        ROUND(FHJ_JN / 10000, 2) fhj_jn,
        ROUND(FHJ_QN / 10000, 2) fhj_qn,
        ROUND((FHJ_JN - FHJ_QN) / 10000, 2) fzje_hj,
        ROUND(CASE WHEN FHJ_QN = 0 THEN 0
        ELSE (FHJ_JN - FHJ_QN) / ABS(FHJ_QN) * 100 END, 2) fzjb_hj
        FROM (
        SELECT
            GROUPING(CONCAT(FNSRSBH, FNSRMC, fhyml)) fgropuping,
            MAX(FNSRSBH) FNSRSBH,
            CASE WHEN GROUPING(CONCAT(FNSRSBH, FNSRMC, fhyml)) = 1 THEN NULL ELSE FPX END AS FPX,
            CASE WHEN GROUPING(CONCAT(FNSRSBH, FNSRMC, fhyml)) = 1 THEN '合计' ELSE FNSRMC END AS FNSRMC,
            CASE WHEN GROUPING(CONCAT(FNSRSBH, FNSRMC, fhyml)) = 1 THEN '' ELSE fhyml END AS fhyml,
            SUM(FHJ_JN) FHJ_JN,
            SUM(FHJ_QN) FHJ_QN
        FROM (
        SELECT tmp1.FNSRSBH, tmp1.FNSRMC,
        FIRST_VALUE(tmp1.fhyml) OVER(PARTITION BY tmp1.FNSRSBH, tmp1.FNSRMC ORDER BY FHJ_JN DESC ) fhyml,
        DENSE_RANK() OVER(ORDER BY FHJ_JN DESC) AS FPX,
        FHJ_JN,
        FHJ_QN
        FROM
        (SELECT max(FNSRSBH) FNSRSBH, FNSRMC, fhyml,
        SUM(CASE WHEN #{ftype} = 'dflc' THEN FQXJ ELSE FHJ END) FHJ_JN
        FROM TB_DW_SRFX_SRFX_MAIN tdssm
        WHERE FZSXM IN ('教育费附加','地方教育附加','增值税','企业所得税','契税',
        '土地增值税','耕地占用税','资源税','房产税','城市维护建设税',
        '车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税',
        '印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="fStartRkrq != null and fStartRkrq != ''">
            <if test="fEndRkrq != null and fEndRkrq != ''">
                AND FRKRQ BETWEEN TO_DATE(#{fStartRkrq},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndRkrq},'YYYY-MM'))
            </if>
        </if>
        <if test="fStartDjrq != null and fStartDjrq != ''">
            <if test="fEndDjrq != null and fEndDjrq != ''">
                AND EXISTS (SELECT 1 FROM TB_DW_SRFX_SWDJXX_MAIN tmp WHERE tdssm.FNSRMC = tmp.FNSRMC)
                AND FKYSLRQ BETWEEN TO_DATE(#{fStartDjrq},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndDjrq},'YYYY-MM'))
            </if>
        </if>
        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            <if test="fEndSkssqq != null and fEndSkssqq != ''">
                AND FSKSSQQQ BETWEEN TO_DATE(#{fStartSkssqq},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndSkssqq},'YYYY-MM'))
            </if>
        </if>
        <if test="fssqyList != null">
        AND FSKGK IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">AND
            FZSXM IN (${fzsxmStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">AND
            fhyml in (${fhydlStr}) <!-- FHYDL in (${fhydlStr}) -->
        </if>
        <if test="fzcdjlxStr != null and fzcdjlxStr != ''">AND
            FDJZCLX IN (${fzcdjlxStr})
        </if>
        <if test="fldts == 2">AND
            FYSKM NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <if test="fyskmStr != null and fyskmStr != ''">and
            FYSKM in (${fyskmStr})
        </if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        GROUP BY FNSRMC, fhyml
        )tmp1
        left join(
        SELECT FNSRMC, fhyml,
        SUM(CASE WHEN #{ftype} = 'dflc' THEN FQXJ ELSE FHJ END) FHJ_QN
        FROM TB_DW_SRFX_SRFX_MAIN tdssm
        WHERE FZSXM IN ('教育费附加','地方教育附加','增值税','企业所得税','契税',
        '土地增值税','耕地占用税','资源税','房产税','城市维护建设税',
        '车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税',
        '印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="fStartRkrqLastYear != null and fStartRkrqLastYear != ''">
            <if test="fEndRkrqLastYear != null and fEndRkrqLastYear != ''">
                AND FRKRQ BETWEEN TO_DATE(#{fStartRkrqLastYear},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndRkrqLastYear},'YYYY-MM'))
            </if>
        </if>
        <if test="fStartDjrq != null and fStartDjrq != ''">
            <if test="fEndDjrq != null and fEndDjrq != ''">
                AND EXISTS (SELECT 1 FROM TB_DW_SRFX_SWDJXX_MAIN tmp WHERE tdssm.FNSRMC = tmp.FNSRMC
                AND FKYSLRQ BETWEEN TO_DATE(#{fStartDjrq},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndDjrq},'YYYY-MM'))
                )
            </if>
        </if>
        <if test="fStartSkssqqLastYear != null and fStartSkssqqLastYear != ''">
            <if test="fEndSkssqqLastYear != null and fEndSkssqqLastYear != ''">
                AND FSKSSQQ BETWEEN TO_DATE(#{fStartSkssqqLastYear},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndSkssqqLastYear},'YYYY-MM'))
            </if>
        </if>
        <if test="fssqyList != null">AND
            FSKGK IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">AND
            FZSXM IN (${fzsxmStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">AND
            fhyml in (${fhydlStr}) <!-- FHYDL in (${fhydlStr}) -->
        </if>
        <if test="fzcdjlxStr != null and fzcdjlxStr != ''">AND
            FDJZCLX IN (${fzcdjlxStr})
        </if>
        <if test="fldts == 2">AND
            FYSKM NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <if test="fyskmStr != null and fyskmStr != ''">AND
            FYSKM in (${fyskmStr})
        </if>
        GROUP BY FNSRMC, fhyml) tmp
        on tmp1.fnsrmc=tmp.fnsrmc
        WHERE 1=1
        <if test="fsrlx == &quot;大于一亿&quot;">AND
            FHJ_JN &gt;= 100000000
        </if>
        <if test="fsrlx == &quot;五千万至一亿&quot;">AND
            FHJ_JN &gt;= 50000000 AND FHJ_JN &lt;= 100000000
        </if>
        <if test="fsrlx == &quot;一千万至五千万&quot;">AND
            FHJ_JN &gt;= 10000000 AND FHJ_JN &lt;= 50000000
        </if>
        <if test="fsrlx == &quot;五百万至一千万&quot;">AND
            FHJ_JN &gt;= 5000000 AND FHJ_JN &lt;= 10000000
        </if>
        <if test="fsrlx == &quot;五十万至五百万&quot;">AND
            FHJ_JN &gt;= 500000 AND FHJ_JN &lt;= 5000000
        </if>
        <if test="fsrlx == &quot;小于五十万&quot;">AND
            FHJ_JN &lt;= 500000
        </if>
        ) tmp3
        <where>
            <if test="fpmLimit != null and fpmLimit != ''">
               and FPX &lt;= #{fpmLimit}
            </if>
        </where>
        GROUP BY CONCAT(FNSRSBH, FNSRMC, fhyml) WITH ROLLUP
        ORDER BY fgropuping DESC, FPX ASC
        ) tmp4
    </select>
    <select id="revenueByTaxTypeMxTable2" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select * from (
        SELECT FNSRSBH, FNSRMC 纳税人名称, FHYDL,
        ROW_NUMBER() OVER (ORDER BY FHJ_JN DESC,FNSRSBH,FNSRMC,FHYDL) - 1 AS fpx,
        ROUND(FHJ_JN / 10000, 2) fhj_jn,
        ROUND(FHJ_QN / 10000, 2) fhj_qn,
        ROUND((FHJ_JN - FHJ_QN) / 10000, 2) fzje_hj,
        ROUND(CASE WHEN FHJ_QN = 0 THEN 0
        ELSE (FHJ_JN - FHJ_QN) / ABS(FHJ_QN) * 100 END, 2) fzjb_hj
        FROM (SELECT FNSRSBH,
        CASE WHEN GROUPING(CONCAT(FNSRSBH, FNSRMC, FHYDL)) = 1 THEN '合计'
        ELSE FNSRMC END AS
        FNSRMC,
        CASE WHEN GROUPING(CONCAT(FNSRSBH, FNSRMC, FHYDL)) = 1 THEN ''
        ELSE FHYDL END AS FHYDL,
        SUM(FHJ_JN) FHJ_JN,
        SUM(FHJ_QN) FHJ_QN
        FROM (SELECT FNSRSBH, FNSRMC,
        FIRST_VALUE(FHYDL) OVER(PARTITION BY FNSRSBH, FNSRMC ORDER BY FHJ_JN DESC ) FHYDL,
        FHJ_JN,
        FHJ_QN
        FROM
        (SELECT max(FNSRSBH) FNSRSBH, FNSRMC, FHYDL,
        SUM(CASE WHEN #{ftype} = 'dflc' THEN FQXJ ELSE FHJ END) FHJ_JN,0 FHJ_QN
        FROM TB_DW_SRFX_SRFX_MAIN tdssm
        WHERE FZSXM IN ('教育费附加','地方教育附加','增值税','企业所得税','契税',
        '土地增值税','耕地占用税','资源税','房产税','城市维护建设税',
        '车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税',
        '印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="fStartRkrq != null and fStartRkrq != ''">
            <if test="fEndRkrq != null and fEndRkrq != ''">
                AND FRKRQ BETWEEN TO_DATE(#{fStartRkrq},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndRkrq},'YYYY-MM'))
            </if>
        </if>
        <if test="fStartDjrq != null and fStartDjrq != ''">
            <if test="fEndDjrq != null and fEndDjrq != ''">
                AND EXISTS (SELECT 1 FROM TB_DW_SRFX_SWDJXX_MAIN tmp WHERE tdssm.FNSRMC = tmp.FNSRMC)
                AND FKYSLRQ BETWEEN TO_DATE(#{fStartDjrq},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndDjrq},'YYYY-MM'))
            </if>
        </if>
        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            <if test="fEndSkssqq != null and fEndSkssqq != ''">
                AND FSKSSQQQ BETWEEN TO_DATE(#{fStartSkssqq},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndSkssqq},'YYYY-MM'))
            </if>
        </if>
        <if test="fssqyList != null">
            AND
            FSKGK IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">AND
            FZSXM IN (${fzsxmStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">AND
            fhydl in (${fhydlStr})
        </if>
        <if test="fzcdjlxStr != null and fzcdjlxStr != ''">AND
            FDJZCLX IN (${fzcdjlxStr})
        </if>
        <if test="fldts == 2">AND
            FYSKM NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <if test="fyskmStr != null and fyskmStr != ''">and
            FYSKM in (${fyskmStr})
        </if>

        GROUP BY FNSRMC, FHYDL
        union all
        SELECT max(FNSRSBH) FNSRSBH,FNSRMC, FHYDL,
        0 FHJ_JN,SUM(CASE WHEN #{ftype} = 'dflc' THEN FQXJ ELSE FHJ END) FHJ_QN
        FROM TB_DW_SRFX_SRFX_MAIN tdssm
        WHERE FZSXM IN ('教育费附加','地方教育附加','增值税','企业所得税','契税',
        '土地增值税','耕地占用税','资源税','房产税','城市维护建设税',
        '车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税',
        '印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="fStartRkrqLastYear != null and fStartRkrqLastYear != ''">
            <if test="fEndRkrqLastYear != null and fEndRkrqLastYear != ''">
                AND FRKRQ BETWEEN TO_DATE(#{fStartRkrqLastYear},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndRkrqLastYear},'YYYY-MM'))
            </if>
        </if>
        <if test="fStartDjrq != null and fStartDjrq != ''">
            <if test="fEndDjrq != null and fEndDjrq != ''">
                AND EXISTS (SELECT 1 FROM TB_DW_SRFX_SWDJXX_MAIN tmp WHERE tdssm.FNSRMC = tmp.FNSRMC
                AND FKYSLRQ BETWEEN TO_DATE(#{fStartDjrq},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndDjrq},'YYYY-MM'))
                )
            </if>
        </if>

        <if test="fStartSkssqqLastYear != null and fStartSkssqqLastYear != ''">
            <if test="fEndSkssqqLastYear != null and fEndSkssqqLastYear != ''">
                AND FSKSSQQQ BETWEEN TO_DATE(#{fStartSkssqqLastYear},'YYYY-MM') AND LAST_DAY(TO_DATE(#{fEndSkssqqLastYear},'YYYY-MM'))
            </if>
        </if>
        <if test="fssqyList != null">AND
            FSKGK IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">AND
            FZSXM IN (${fzsxmStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">AND
            fhydl in (${fhydlStr})
        </if>
        <if test="fzcdjlxStr != null and fzcdjlxStr != ''">AND
            FDJZCLX IN (${fzcdjlxStr})
        </if>
        <if test="fldts == 2">AND
            FYSKM NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <if test="fyskmStr != null and fyskmStr != ''">AND
            FYSKM in (${fyskmStr})
        </if>
        GROUP BY FNSRMC, FHYDL) tmp
        ) t1
        WHERE 1=1
        <if test="fsrlx == &quot;大于一亿&quot;">AND
            FHJ_JN &gt;= 100000000
        </if>
        <if test="fsrlx == &quot;五千万至一亿&quot;">AND
            FHJ_JN &gt;= 50000000 AND FHJ_JN &lt;= 100000000
        </if>
        <if test="fsrlx == &quot;一千万至五千万&quot;">AND
            FHJ_JN &gt;= 10000000 AND FHJ_JN &lt;= 50000000
        </if>
        <if test="fsrlx == &quot;五百万至一千万&quot;">AND
            FHJ_JN &gt;= 5000000 AND FHJ_JN &lt;= 10000000
        </if>
        <if test="fsrlx == &quot;五十万至五百万&quot;">AND
            FHJ_JN &gt;= 500000 AND FHJ_JN &lt;= 5000000
        </if>
        <if test="fsrlx == &quot;小于五十万&quot;">AND
            FHJ_JN &lt;= 500000
        </if>
        GROUP BY CONCAT(FNSRSBH, FNSRMC, FHYDL) WITH ROLLUP) t2
        ORDER BY fpx
        )t where 1=1
        <if test="fpmLimit != null and fpmLimit != ''">
            and fpx &lt;= #{fpmLimit}
        </if>
    </select>

    <select id="taxStructureAnalysis_fsz" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select fzsxm name, round(sum(fhj) / 10000, 2) value
        from tb_dw_srfx_srfx_main a
        where 1 = 1
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fskssqq != null and fskssqq != ''">AND
            a.fskssqq BETWEEN str_to_date(concat(substr(#{fskssqq}, 1, 7),'-01'), '%Y-%m-%d')
            AND last_day(str_to_date(substr(#{fskssqq}, -7, 7), '%Y-%m'))
        </if>
        <if test="frkrq != null and frkrq != ''">AND
            a.frkrq BETWEEN str_to_date(concat(substr(#{frkrq}, 1, 7),'-01'), '%Y-%m-%d')
            AND last_day(str_to_date(substr(#{frkrq}, -7, 7), '%Y-%m'))
        </if>
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="fnsrmc != null and fnsrmc != ''">and
            fnsrmc = #{fnsrmc}
        </if>
        <if test="fnsrsbh != null and fnsrsbh != ''">and
            fnsrsbh like concat('%', #{fnsrsbh}, '%')
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        <if test="fyskmStr != null and fyskmStr != ''">and
            fyskm in (${fyskmStr})
        </if>
        <if test="fzcdjlxStr != null and fzcdjlxStr != ''">and
            fdjzclx in (${fzcdjlxStr})
        </if>
        <if test="fldts == 2">AND
            fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        group by fzsxm
        order by value desc
    </select>

    <select id="AnlzByZspm" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        ss.*,
        round(fhj_jn - fhj_qn, 2) fhj_zje,
        CASE WHEN fhj_qn = 0 and fhj_jn = 0 THEN 0
        WHEN fhj_qn = 0 THEN 100
        WHEN fhj_qn is null THEN 100
        WHEN fhj_qn = '' THEN 100
        ELSE ROUND((fhj_jn - fhj_qn)/fhj_qn * 100, 2)
        END fhj_zjb,
        round(fqxj_jn - fqxj_qn, 2) fqxj_zje,
        CASE WHEN fqxj_qn = 0 and fqxj_jn = 0 THEN 0
        WHEN fqxj_qn=0 THEN 100
        WHEN fqxj_qn is null THEN 100
        WHEN fqxj_qn= '' THEN 100
        ELSE ROUND((fqxj_jn - fqxj_qn)/fqxj_qn * 100, 2)
        END fqxj_zjb
        FROM (
        SELECT
        fzspm,
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4) =
            YEAR(fskssqq)
            then fhj else 0 end
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4) =
            YEAR(frkrq)
            then fhj else 0 end
        </if>

        )/10000, 2) fhj_jn,
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4) =
            YEAR(fskssqq)
            then fzyj else 0 end
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4) =
            YEAR(frkrq)
            then fzyj else 0 end
        </if>

        )/10000, 2) fzyj_jn,
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4) =
            YEAR(fskssqq)
            then fssj else 0 end
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4) =
            YEAR(frkrq)
            then fssj else 0 end
        </if>

        )/10000, 2) fsdj_jn,
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4) =
            YEAR(fskssqq)
            then fdsj else 0 end
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4) =
            YEAR(frkrq)
            then fdsj else 0 end
        </if>

        )/10000, 2) fdsj_jn,
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4) =
            YEAR(fskssqq)
            then fqxj else 0 end
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4) =
            YEAR(frkrq)
            then fqxj else 0 end
        </if>

        )/10000, 2) fqxj_jn,
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4)- 1 =
            YEAR(fskssqq)
            then fhj else 0 end
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4)- 1 =
            YEAR(frkrq)
            then fhj else 0 end
        </if>

        )/10000, 2) fhj_qn,
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4)- 1 =
            YEAR(fskssqq)
            then fzyj else 0 end
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4)- 1 =
            YEAR(frkrq)
            then fzyj else 0 end
        </if>


        )/10000, 2) fzyj_qn,
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4)- 1 =
            YEAR(fskssqq)
            then fssj else 0 end
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4)- 1 =
            YEAR(frkrq)
            then fssj else 0 end
        </if>

        )/10000, 2) fsdj_qn,
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4)- 1 =
            YEAR(fskssqq)
            then fdsj else 0 end
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4)- 1 =
            YEAR(frkrq)
            then fdsj else 0 end
        </if>

        )/10000, 2) fdsj_qn,
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4)- 1 =
            YEAR(fskssqq)
            then fqxj else 0 end
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4)- 1 =
            YEAR(frkrq)
            then fqxj else 0 end
        </if>

        )/10000, 2) fqxj_qn
        FROM tb_dw_srfx_srfx_main
        WHERE 1=1
        <if test="fnsrmc != null and fnsrmc != ''">and
            fnsrmc = #{fnsrmc}
        </if>
        <if test="fssqyList != null">AND
            fskgk IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null">AND
            fzsxm IN
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhydlList != null">AND
            fhydl IN
            <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fnsrsbh != null and fnsrsbh != ''">and
            fnsrsbh = #{fnsrsbh}
        </if>
        <if test="fStartSkssqq != null and fStartSkssqq != ''">and
            (
            fskssqq between str_to_date(concat(#{fStartSkssqq},'-01'),'%Y-%m-%d') and
            last_day(str_to_date(#{fEndSkssqq},'%Y-%m'))
            OR fskssqq between str_to_date(concat(#{fStartSkssqqLastYear},'-01'),'%Y-%m-%d') and
            last_day(str_to_date(#{fEndSkssqqLastYear},'%Y-%m'))
            )
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">and
            (
            frkrq between str_to_date(concat(#{fStartRkrq},'-01'),'%Y-%m-%d') and
            last_day(str_to_date(#{fEndRkrq},'%Y-%m'))
            OR frkrq between str_to_date(concat(#{fStartRkrqLastYear},'-01'),'%Y-%m-%d') and
            last_day(str_to_date(#{fEndRkrqLastYear},'%Y-%m'))
            )
        </if>
        <if test="fyskmStr != null and fyskmStr != ''">and
            fyskm in (${fyskmStr})
        </if>
        <if test="fzcdjlxStr != null and fzcdjlxStr != ''">and
            fdjzclx in (${fzcdjlxStr})
        </if>
        <if test="fldts == 2">AND
            fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        GROUP BY fzspm
        ) ss
        order by fhj_jn desc
    </select>

    <select id="taxpayerTaxTrendAnlzMx" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        fnsrsbh fnsrsbh,
        fnsrmc fnsrmc,
        fzsxm fzsxm,
        fzspm fzspm,
        DATE_FORMAT(frkrq, '%Y-%m-%d') frkrq,
        DATE_FORMAT(fskssqq, '%Y-%m-%d') fskssqq,
        DATE_FORMAT(fskssqz, '%Y-%m-%d') fskssqz,
        fdjzclx fzcdjlx,
        fhyml fhyml,
        fhydl fhydl,
        fhyzl fhyzl,
        fhy fhy,
        fjsyj fjsyj,
        case when instr(CONCAT(fsl,''),'.')=1 then CONCAT('0',fsl,'') else CONCAT(fsl,'') end fsl,
        fssswjg fskssswjg,
        fskgk fskgk,
        fyskm fyskm,
        fjdxz fjdxz,
        fsksx fsksx,
        ROUND(fhj/10000, 2) fhj,
        ROUND(fzyj/10000, 2) fzyj,
        ROUND(fssj/10000, 2) fsdj,
        ROUND(fdsj/10000, 2) fdsj,
        ROUND(fqxj/10000, 2) fqxj
        FROM tb_dw_srfx_srfx_main
        WHERE 1=1
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fnsrmc != null and fnsrmc != ''">and
            fnsrmc = #{fnsrmc}
        </if>
        <if test="fssqyList != null">AND
            fskgk IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null">AND
            fzsxm IN
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhydlList != null">AND
            fhydl IN
            <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fnsrsbh != null and fnsrsbh != ''">and
            fnsrsbh = #{fnsrsbh}
        </if>
        <if test="fStartSkssqq != null and fStartSkssqq != ''">and
            (
            fskssqq between str_to_date(concat(#{fStartSkssqq},'-01'),'%Y-%m-%d') and
            last_day(str_to_date(#{fEndSkssqq},'%Y-%m'))
            OR fskssqq between str_to_date(concat(#{fStartSkssqqLastYear},'-01'),'%Y-%m-%d') and
            last_day(str_to_date(#{fEndSkssqqLastYear},'%Y-%m'))
            )
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">and
            (
            frkrq between str_to_date(concat(#{fStartRkrq},'-01'),'%Y-%m-%d') and
            last_day(str_to_date(#{fEndRkrq},'%Y-%m'))
            OR frkrq between str_to_date(concat(#{fStartRkrqLastYear},'-01'),'%Y-%m-%d') and
            last_day(str_to_date(#{fEndRkrqLastYear},'%Y-%m'))
            )
        </if>
        <if test="fyskmStr != null and fyskmStr != ''">and
            fyskm in (${fyskmStr})
        </if>
        <if test="fzcdjlxStr != null and fzcdjlxStr != ''">and
            fdjzclx in (${fzcdjlxStr})
        </if>
        <if test="fzspm != null and fzspm != ''">AND
            fzspm = #{fzspm}
        </if>
        <if test="fldts == 2">AND
            fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        order by frkrq desc
    </select>

    <select id="AnlzByZsxm" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        ss.*,
        round(fhj_jn - fhj_qn, 2) fhj_zje,
        CASE WHEN fhj_qn = 0 and fhj_jn = 0 THEN 0
        WHEN fhj_qn = 0 THEN 100
        WHEN fhj_qn is null THEN 100
        WHEN fhj_qn = '' THEN 100
        ELSE ROUND((fhj_jn - fhj_qn)/fhj_qn * 100, 2)
        END fhj_zjb,
        round(fqxj_jn - fqxj_qn, 2) fqxj_zje,
        CASE WHEN fqxj_qn = 0 and fqxj_jn = 0 THEN 0
        WHEN fqxj_qn=0 THEN 100
        WHEN fqxj_qn is null THEN 100
        WHEN fqxj_qn= '' THEN 100
        ELSE ROUND((fqxj_jn - fqxj_qn)/fqxj_qn * 100, 2)
        END fqxj_zjb
        FROM (
        SELECT
        fzsxm,
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4) =
            YEAR(fskssqq)
            then fhj else 0 end
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4) =
            YEAR(frkrq)
            then fhj else 0 end
        </if>

        )/10000, 2) fhj_jn,
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4) =
            YEAR(fskssqq)
            then fzyj else 0 end
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4) =
            YEAR(frkrq)
            then fzyj else 0 end
        </if>

        )/10000, 2) fzyj_jn,
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4) =
            YEAR(fskssqq)
            then fssj else 0 end
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4) =
            YEAR(frkrq)
            then fssj else 0 end
        </if>

        )/10000, 2) fsdj_jn,
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4) =
            YEAR(fskssqq)
            then fdsj else 0 end
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4) =
            YEAR(frkrq)
            then fdsj else 0 end
        </if>

        )/10000, 2) fdsj_jn,
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4) =
            YEAR(fskssqq)
            then fqxj else 0 end
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4) =
            YEAR(frkrq)
            then fqxj else 0 end
        </if>

        )/10000, 2) fqxj_jn,
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4)- 1 =
            YEAR(fskssqq)
            then fhj else 0 end
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4)- 1 =
            YEAR(frkrq)
            then fhj else 0 end
        </if>

        )/10000, 2) fhj_qn,
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4)- 1 =
            YEAR(fskssqq)
            then fzyj else 0 end
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4)- 1 =
            YEAR(frkrq)
            then fzyj else 0 end
        </if>


        )/10000, 2) fzyj_qn,
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4)- 1 =
            YEAR(fskssqq)
            then fssj else 0 end
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4)- 1 =
            YEAR(frkrq)
            then fssj else 0 end
        </if>

        )/10000, 2) fsdj_qn,
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4)- 1 =
            YEAR(fskssqq)
            then fdsj else 0 end
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4)- 1 =
            YEAR(frkrq)
            then fdsj else 0 end
        </if>

        )/10000, 2) fdsj_qn,
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4)- 1 =
            YEAR(fskssqq)
            then fqxj else 0 end
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4)- 1 =
            YEAR(frkrq)
            then fqxj else 0 end
        </if>

        )/10000, 2) fqxj_qn
        FROM tb_dw_srfx_srfx_main
        WHERE 1=1
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fnsrmc != null and fnsrmc != ''">and
            fnsrmc = #{fnsrmc}
        </if>
        <if test="fssqyList != null">AND
            fskgk IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null">AND
            fzsxm IN
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhydlList != null">AND
            fhydl IN
            <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fnsrsbh != null and fnsrsbh != ''">and
            fnsrsbh = #{fnsrsbh}
        </if>
        <if test="fStartSkssqq != null and fStartSkssqq != ''">and
            (
            fskssqq between to_date(concat(#{fStartSkssqq},'-01'),'yyyy-mm-dd')
            and last_day(to_date(#{fEndSkssqq},'yyyy-mm'))
            OR fskssqq between to_date(concat(#{fStartSkssqqLastYear},'-01'),'yyyy-mm-dd')
            and last_day(to_date(#{fEndSkssqqLastYear},'yyyy-mm'))
            )
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">and
            (
            frkrq between to_date(concat(#{fStartRkrq},'-01'),'yyyy-mm-dd') and last_day(to_date(#{fEndRkrq},'yyyy-mm'))
            OR frkrq between to_date(concat(#{fStartRkrqLastYear},'-01'),'yyyy-mm-dd') and
            last_day(to_date(#{fEndRkrqLastYear},'yyyy-mm'))
            )
        </if>
        <if test="fyskmStr != null and fyskmStr != ''">and
            fyskm in (${fyskmStr})
        </if>
        <if test="fzcdjlxStr != null and fzcdjlxStr != ''">and
            fdjzclx in (${fzcdjlxStr})
        </if>
        <if test="fldts == 2">AND
            fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        GROUP BY fzsxm
        ) ss
        order by fhj_jn desc
    </select>

    <select id="taxStructureAnalysis_five" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.line.LineSeriesInitData">
        select t1.fyear xAxis,t1.legend,case when t2.fshj is null then 0 else t2.fshj end yAxis from
        (
        select #{start_year} fyear ,'税收' legend from dual
        union all
        select #{start_year}-1,'税收' from dual
        union all
        select #{start_year}-2,'税收' from dual
        union all
        select #{start_year}-3,'税收' from dual
        union all
        select #{start_year}-4,'税收' from dual
        union all
        select #{start_year} fyear ,'税收占比' legend from dual
        union all
        select #{start_year}-1,'税收占比' from dual
        union all
        select #{start_year}-2,'税收占比' from dual
        union all
        select #{start_year}-3,'税收占比' from dual
        union all
        select #{start_year}-4,'税收占比' from dual
        )t1
        left join (
        select sum(fhj)/10000 fshj,substr(
        <if test="frkrq != null and frkrq != ''">frkrq</if>
        <if test="fskssqq != null and fskssqq != ''">fskssqq</if>
        ,1,4) fyear ,'税收' legend from tb_dw_srfx_srfx_main
        where 1=1
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="frkrq != null and frkrq != ''">
            and fnf BETWEEN substr(#{frkrq}, 1, 4)-4 AND substr(#{frkrq}, 1, 4)
            and fyf BETWEEN substr(#{frkrq}, 6, 2) AND substr(#{frkrq}, 16, 2)
        </if>
        <if test="fskssqq != null and fskssqq != ''">
            and (
            fskssqq BETWEEN (concat(substr(#{fskssqq}, 1, 4), substr(#{fskssqq}, 5, 3),'-01'),'%Y-%m-%d')
            AND last_day(str_to_date(concat(substr(#{fskssqq}, 11, 4),substr(#{fskssqq}, 15, 3)),'%Y-%m'))
            or
            fskssqq BETWEEN str_to_date(concat(substr(#{fskssqq}, 1, 4) - 1, substr(#{fskssqq}, 5, 3),'-01'),'%Y-%m-%d')
            AND last_day(str_to_date(concat(substr(#{fskssqq}, 11, 4) - 1, substr(#{fskssqq}, 15, 3)),'%Y-%m'))
            or
            fskssqq BETWEEN str_to_date(concat(substr(#{fskssqq}, 1, 4) - 2, substr(#{fskssqq}, 5, 3),'-01'),'%Y-%m-%d')
            AND last_day(str_to_date(concat(substr(#{fskssqq}, 11, 4) -2, substr(#{fskssqq}, 15, 3)),'%Y-%m'))
            or
            fskssqq BETWEEN str_to_date(concat(substr(#{fskssqq}, 1, 4) - 3, substr(#{fskssqq}, 5, 3),'-01'),'%Y-%m-%d')
            AND last_day(str_to_date(concat(substr(#{fskssqq}, 11, 4) -3, substr(#{fskssqq}, 15, 3)),'%Y-%m'))
            or
            fskssqq BETWEEN str_to_date(concat(substr(#{fskssqq}, 1, 4) - 4, substr(#{fskssqq}, 5, 3),'-01'),'%Y-%m-%d')
            AND last_day(str_to_date(concat(substr(#{fskssqq}, 11, 4) -4, substr(#{fskssqq}, 15, 3)),'%Y-%m'))
            )
        </if>
        <if test="fnsrmc != null and fnsrmc != ''">and
            fnsrmc = #{fnsrmc}
        </if>
        <if test="fnsrsbh != null and fnsrsbh != ''">and
            fnsrsbh like concat('%', #{fnsrsbh}, '%')
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        <if test="fyskmStr != null and fyskmStr != ''">and
            fyskm in (${fyskmStr})
        </if>
        <if test="fzcdjlxStr != null and fzcdjlxStr != ''">and
            fdjzclx in (${fzcdjlxStr})
        </if>
        <if test="fldts == 2">AND
            fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        group by substr(
        <if test="frkrq != null and frkrq != ''">frkrq</if>
        <if test="fskssqq != null and fskssqq != ''">fskssqq</if>
        ,1,4)
        union all
        select case when fshj_all = 0 then 0 else round(fshj*100/fshj_all,2) end fzb , t1.fyear , '税收占比' legend from
        (select sum(fhj)/10000 fshj_all,substr(
        <if test="frkrq != null and frkrq != ''">frkrq</if>
        <if test="fskssqq != null and fskssqq != ''">fskssqq</if>
        ,1,4) fyear from tb_dw_srfx_srfx_main
        where 1=1
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="frkrq != null and frkrq != ''">
            and fnf BETWEEN substr(#{frkrq}, 1, 4)-4 AND substr(#{frkrq}, 1, 4)
            and fyf BETWEEN substr(#{frkrq}, 6, 2) AND substr(#{frkrq}, 16, 2)
        </if>
        <if test="fskssqq != null and fskssqq != ''">
            and (
            fskssqq BETWEEN str_to_date(concat(substr(#{fskssqq}, 1, 4), substr(#{fskssqq}, 5, 3),'-01'),'%Y-%m-%d')
            AND last_day(str_to_date(concat(substr(#{fskssqq}, 11, 4),substr(#{fskssqq}, 15, 3)),'%Y-%m'))
            or
            fskssqq BETWEEN str_to_date(concat(substr(#{fskssqq}, 1, 4) - 1, substr(#{fskssqq}, 5, 3),'-01'),'%Y-%m-%d')
            AND last_day(str_to_date(concat(substr(#{fskssqq}, 11, 4) - 1, substr(#{fskssqq}, 15, 3)),'%Y-%m'))
            or
            fskssqq BETWEEN str_to_date(concat(substr(#{fskssqq}, 1, 4) - 2, substr(#{fskssqq}, 5, 3),'-01'),'%Y-%m-%d')
            AND last_day(str_to_date(concat(substr(#{fskssqq}, 11, 4) -2, substr(#{fskssqq}, 15, 3)),'%Y-%m'))
            or
            fskssqq BETWEEN str_to_date(concat(substr(#{fskssqq}, 1, 4) - 3, substr(#{fskssqq}, 5, 3),'-01'),'%Y-%m-%d')
            AND last_day(str_to_date(concat(substr(#{fskssqq}, 11, 4) -3, substr(#{fskssqq}, 15, 3)),'%Y-%m'))
            or
            fskssqq BETWEEN str_to_date(concat(substr(#{fskssqq}, 1, 4) - 4, substr(#{fskssqq}, 5, 3),'-01'),'%Y-%m-%d')
            AND last_day(str_to_date(concat(substr(#{fskssqq}, 11, 4) -4, substr(#{fskssqq}, 15, 3)),'%Y-%m'))
            )
        </if>
        <if test="fnsrmc != null and fnsrmc != ''">and
            fnsrmc = #{fnsrmc}
        </if>
        <if test="fnsrsbh != null and fnsrsbh != ''">and
            fnsrsbh like concat('%', #{fnsrsbh}, '%')
        </if>
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        <if test="fyskmStr != null and fyskmStr != ''">and
            fyskm in (${fyskmStr})
        </if>
        <if test="fzcdjlxStr != null and fzcdjlxStr != ''">and
            fdjzclx in (${fzcdjlxStr})
        </if>
        <if test="fldts == 2">AND
            fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        group by substr(
        <if test="frkrq != null and frkrq != ''">frkrq</if>
        <if test="fskssqq != null and fskssqq != ''">fskssqq</if>
        ,1,4) ) t1
        left join
        (
        select sum(fhj)/10000 fshj
        ,substr(
        <if test="frkrq != null and frkrq != ''">frkrq</if>
        <if test="fskssqq != null and fskssqq != ''">fskssqq</if>
        ,1,4) fyear from tb_dw_srfx_srfx_main
        where 1=1
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="frkrq != null and frkrq != ''">
            and fnf BETWEEN substr(#{frkrq}, 1, 4)-4 AND substr(#{frkrq}, 1, 4)
            and fyf BETWEEN substr(#{frkrq}, 6, 2) AND substr(#{frkrq}, 16, 2)
        </if>
        <if test="fskssqq != null and fskssqq != ''">
            and (
            fskssqq BETWEEN str_to_date(concat(substr(#{fskssqq}, 1, 4), substr(#{fskssqq}, 5, 3),'-01'),'%Y-%m-%d')
            AND last_day(str_to_date(concat(substr(#{fskssqq}, 11, 4),substr(#{fskssqq}, 15, 3)),'%Y-%m'))
            or
            fskssqq BETWEEN str_to_date(concat(substr(#{fskssqq}, 1, 4) - 1, substr(#{fskssqq}, 5, 3),'-01'),'%Y-%m-%d')
            AND last_day(str_to_date(concat(substr(#{fskssqq}, 11, 4) - 1, substr(#{fskssqq}, 15, 3)),'%Y-%m'))
            or
            fskssqq BETWEEN str_to_date(concat(substr(#{fskssqq}, 1, 4) - 2, substr(#{fskssqq}, 5, 3),'-01'),'%Y-%m-%d')
            AND last_day(str_to_date(concat(substr(#{fskssqq}, 11, 4) -2, substr(#{fskssqq}, 15, 3)),'%Y-%m'))
            or
            fskssqq BETWEEN str_to_date(concat(substr(#{fskssqq}, 1, 4) - 3, substr(#{fskssqq}, 5, 3),'-01'),'%Y-%m-%d')
            AND last_day(str_to_date(concat(substr(#{fskssqq}, 11, 4) -3, substr(#{fskssqq}, 15, 3)),'%Y-%m'))
            or
            fskssqq BETWEEN str_to_date(concat(substr(#{fskssqq}, 1, 4) - 4, substr(#{fskssqq}, 5, 3),'-01'),'%Y-%m-%d')
            AND last_day(str_to_date(concat(substr(#{fskssqq}, 11, 4) -4, substr(#{fskssqq}, 15, 3)),'%Y-%m'))
            )
        </if>

        <if test="fnsrmc != null and fnsrmc != ''">and
            fnsrmc = #{fnsrmc}
        </if>
        <if test="fnsrsbh != null and fnsrsbh != ''">and
            fnsrsbh like concat('%', #{fnsrsbh}, '%')
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        <if test="fyskmStr != null and fyskmStr != ''">and
            fyskm in (${fyskmStr})
        </if>
        <if test="fzcdjlxStr != null and fzcdjlxStr != ''">and
            fdjzclx in (${fzcdjlxStr})
        </if>
        <if test="fldts == 2">AND
            fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        group by substr(
        <if test="frkrq != null and frkrq != ''">frkrq</if>
        <if test="fskssqq != null and fskssqq != ''">fskssqq</if>
        ,1,4)
        ) t2 on t1.fyear=t2.fyear
        ) t2 on t1.fyear = t2.fyear and t1.legend=t2.legend
        order by t1.fyear
    </select>

    <select id="rankingByTaxpayerEchar" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select name,round(value,2) value from (
        SELECT
        '一百万以下' name
        ,sum(case when fhj &lt; 100 then fhj else 0 end) as value
        FROM
        (select sum(fhj)/10000 fhj,fnsrmc from tb_dw_srfx_srfx_main
        where
        frkrq between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth},'-','01'),'%Y-%m-%d')
        and last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth}),'%Y-%m'))
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhymlStr != null and fhymlStr != ''">and
            fhyml in (${fhymlStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        <if test="fnsrmc != null and fnsrmc != ''">and
            fnsrmc like CONCAT('%' ,#{fnsrmc},'%')
        </if>
        <if test="fldts == 2">and
            fyskm not like '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        group by fnsrmc) as a
        where 1=1
        <if test="fqsje != null and fqsje != ''">and
            fhj &gt;= #{fqsje}
        </if>

        union all

        SELECT
        '一百万到五百万' name
        ,sum(case when fhj &gt;= 100 and fhj &lt; 500 then fhj else 0 end) as value
        FROM
        (select sum(fhj)/10000 fhj,fnsrmc from tb_dw_srfx_srfx_main
        where
        frkrq between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth},'-','01'),'%Y-%m-%d')
        and last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth}),'%Y-%m'))
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhymlStr != null and fhymlStr != ''">and
            fhyml in (${fhymlStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        <if test="fnsrmc != null and fnsrmc != ''">and
            fnsrmc like CONCAT('%' ,#{fnsrmc},'%')
        </if>
        <if test="fldts == 2">and
            fyskm not like '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        group by fnsrmc) as a
        where 1=1
        <if test="fqsje != null and fqsje != ''">and
            fhj &gt;= #{fqsje}
        </if>
        union all
        SELECT
        '五百万到一千万' name
        ,sum(case when fhj &gt;= 500 and fhj &lt; 1000 then fhj else 0 end) as value
        FROM
        (select sum(fhj)/10000 fhj,fnsrmc from tb_dw_srfx_srfx_main
        where
        frkrq between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth},'-','01'),'%Y-%m-%d')
        and last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth}),'%Y-%m'))
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhymlStr != null and fhymlStr != ''">and
            fhyml in (${fhymlStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        <if test="fnsrmc != null and fnsrmc != ''">and
            fnsrmc like CONCAT('%' ,#{fnsrmc},'%')
        </if>
        <if test="fldts == 2">and
            fyskm not like '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        group by fnsrmc) as a
        where 1=1
        <if test="fqsje != null and fqsje != ''">and
            fhj &gt;= #{fqsje}
        </if>
        union all
        SELECT
        '一千万到五千万' name
        ,sum(case when fhj &gt;= 1000 and fhj &lt; 5000 then fhj else 0 end) as value
        FROM
        (select sum(fhj)/10000 fhj,fnsrmc from tb_dw_srfx_srfx_main
        where
        frkrq between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth},'-','01'),'%Y-%m-%d')
        and last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth}),'%Y-%m'))
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhymlStr != null and fhymlStr != ''">and
            fhyml in (${fhymlStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        <if test="fnsrmc != null and fnsrmc != ''">and
            fnsrmc like CONCAT('%' ,#{fnsrmc},'%')
        </if>
        <if test="fldts == 2">and
            fyskm not like '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        group by fnsrmc) as a
        where 1=1
        <if test="fqsje != null and fqsje != ''">and
            fhj &gt;= #{fqsje}
        </if>
        union all
        SELECT
        '五千万到一亿' name
        ,sum(case when fhj &gt;= 5000 and fhj &lt; 10000 then fhj else 0 end) as value
        FROM
        (select sum(fhj)/10000 fhj,fnsrmc from tb_dw_srfx_srfx_main
        where
        frkrq between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth},'-','01'),'%Y-%m-%d')
        and last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth}),'%Y-%m'))
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhymlStr != null and fhymlStr != ''">and
            fhyml in (${fhymlStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        <if test="fnsrmc != null and fnsrmc != ''">and
            fnsrmc like CONCAT('%' ,#{fnsrmc},'%')
        </if>
        <if test="fldts == 2">and
            fyskm not like '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        group by fnsrmc) as a
        where 1=1
        <if test="fqsje != null and fqsje != ''">and
            fhj &gt;= #{fqsje}
        </if>
        union all
        SELECT
        '大于一亿' name
        ,sum(case when fhj &gt;= 10000 then fhj else 0 end) as value
        FROM
        (select sum(fhj)/10000 fhj,fnsrmc from tb_dw_srfx_srfx_main
        where
        frkrq between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth},'-','01'),'%Y-%m-%d')
        and last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth}),'%Y-%m'))
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhymlStr != null and fhymlStr != ''">and
            fhyml in (${fhymlStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        <if test="fnsrmc != null and fnsrmc != ''">and
            fnsrmc like CONCAT('%' ,#{fnsrmc},'%')
        </if>
        <if test="fldts == 2">and
            fyskm not like '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        group by fnsrmc) as a
        where 1=1
        <if test="fqsje != null and fqsje != ''">and
            fhj &gt;= #{fqsje}
        </if>
        ) f
    </select>

    <select id="rankingByTaxpayerEcharline" parameterType="java.util.Map"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData">
        select fhj yAxis ,ff legend,fnf xAxis from (
        select sum(fhj) fhj, fnf , #{fgm} ff from (
        select sum(fhj)/10000 fhj,fnsrmc,fnf from tb_dw_srfx_srfx_main
        where fnf between #{start_year}-4 and #{start_year} and fyf between #{fstartmonth} and #{fendmonth}
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhymlStr != null and fhymlStr != ''">and
            fhyml in (${fhymlStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>

        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        <if test="fnsrmc != null and fnsrmc != ''">and
            fnsrmc like CONCAT('%' ,#{fnsrmc},'%')
        </if>
        <if test="fldts == 2">and
            fyskm not like '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        group by fnsrmc,fnf
        ) as a where 1=1
        <if test="fqsje != null and fqsje != ''">and
            fhj &gt; 100
        </if>
        and fhj between ${fgmdy} and ${fgmxy}
        group by fnf

        union all
        select case when fhj_all = 0 then 0 else round(fhj*100/fhj_all,2) end fzb,t1.fnf,concat(#{fgm},'占比') from (
        select sum(fhj) fhj, fnf from (
        select sum(fhj)/10000 fhj,fnsrmc,fnf from tb_dw_srfx_srfx_main
        where fnf between #{start_year}-4 and #{start_year} and fyf between #{fstartmonth} and #{fendmonth}
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhymlStr != null and fhymlStr != ''">and
            fhyml in (${fhymlStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        <if test="fnsrmc != null and fnsrmc != ''">and
            fnsrmc like CONCAT('%' ,#{fnsrmc},'%')
        </if>
        <if test="fldts == 2">and
            fyskm not like '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        group by fnsrmc,fnf
        ) as b where 1=1
        <if test="fqsje != null and fqsje != ''">and
            fhj &gt; 100
        </if>
        and fhj between ${fgmdy} and ${fgmxy}
        group by fnf) t1
        left join
        (
        select sum(fhj) fhj_all, fnf from (
        select sum(fhj)/10000 fhj,fnsrmc,fnf from tb_dw_srfx_srfx_main
        where fnf between #{start_year}-4 and #{start_year} and fyf between #{fstartmonth} and #{fendmonth}
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhymlStr != null and fhymlStr != ''">and
            fhyml in (${fhymlStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        <if test="fnsrmc != null and fnsrmc != ''">and
            fnsrmc like CONCAT('%' ,#{fnsrmc},'%')
        </if>
        <if test="fldts == 2">and
            fyskm not like '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        group by fnsrmc,fnf
        ) as c where 1=1
        <if test="fqsje != null and fqsje != ''">and
            fhj &gt; 100
        </if>
        group by fnf
        ) t2 on t1.fnf=t2.fnf
        ) as d
        order by fnf
    </select>

    <select id="industryAnalysis" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">

        select
        case when fhyml is null then '其他' else fhyml end name,
        round(sum(fhj) / 10000, 2) as value
        from tb_dw_srfx_srfx_main a
        where 1=1
        <if test="start_year != null and start_year != ''">AND
            frkrq between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth}),'%Y-%m') and
            last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth}),'%Y-%m'))
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        <if test="fldts == 2">AND
            fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        group by fhyml
        order by value desc
    </select>

    <select id="industryAnalysisV2" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select
        case when fhyml is null then '其他' else fhyml end "name",
        round(sum(fje) / 10000, 2) as "value"
        from TB_DW_ZHZS_HYSZ_GEN a
        where 1=1
        <if test="start_year != null and start_year != ''">
            AND str_to_date(frkrq, '%Y-%m') between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth}),'%Y-%m') and
            last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth}),'%Y-%m'))
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fsz in (${fzsxmStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        and fsz in ('教育费附加','地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        group by fhyml
        order by value desc
    </select>

    <select id="overallStatisticsByHyml" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT
        case when grouping(行业门类) = 1 then '合计' else 行业门类 end fhyml,
        round(sum(fhj_jn), 2) fhj_jn,
        round(sum(fhj_qn), 2) fhj_qn,
        round(sum(fhj_jn) - sum(fhj_qn), 2) fzje,
        CASE sum(fhj_qn)
        WHEN 0 THEN 0
        ELSE ROUND((sum(fhj_jn) - sum(fhj_qn)) / sum(fhj_qn) * 100, 2)
        END fzjb
        FROM (
        SELECT
        fhyml 行业门类,
        ROUND(SUM(
        case when YEAR(frkrq) = #{start_year} then fhj else 0 end
        )/10000, 2) fhj_jn,
        ROUND(SUM(
        case when YEAR(frkrq) = #{start_year} - 1 then fhj else 0 end
        )/10000, 2) fhj_qn
        FROM tb_dw_srfx_srfx_main srfx
        WHERE (
        frkrq BETWEEN str_to_date(CONCAT(#{start_year}, '-', #{fstartmonth}, '-01'), '%Y-%m-%d') AND
        last_day(str_to_date(CONCAT(#{start_year}, '-', #{fendmonth}, '-01'), '%Y-%m-%d'))
        or
        frkrq BETWEEN str_to_date(CONCAT(#{start_year} - 1, '-', #{fstartmonth}, '-01'), '%Y-%m-%d') AND
        last_day(str_to_date(CONCAT(#{start_year} - 1, '-', #{fendmonth}, '-01'), '%Y-%m-%d'))
        )
        <if test="fssqyList != null">AND
            fskgk IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null">AND
            fzsxm IN
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fldts == 2">AND
            fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        GROUP BY fhyml
        ) tab1
        GROUP BY 行业门类 with rollup
        ORDER BY grouping(行业门类) desc, fzje DESC
    </select>

    <!--按行业统计    statisticsByIndustry-->
    <select id="statisticsByIndustry" parameterType="java.util.Map" resultType="java.util.Map">
        select case when grouping(fhyml) = 1 then '合计' else fhyml end fhyml,
        round(SUM(fhj), 2) fhj,
        round(SUM(fzyj), 2) fzyj,
        round(SUM(fssj), 2) fssj,
        round(SUM(fdsj), 2) fdsj,
        round(SUM(fqxj), 2) fqxj,
        round(SUM(fhj_qn), 2) fhj_qn,
        round(SUM(fzyj_qn), 2) fzyj_qn,
        round(SUM(fssj_qn), 2) fssj_qn,
        round(SUM(fdsj_qn), 2) fdsj_qn,
        round(SUM(fqxj_qn), 2) fqxj_qn,
        round(ifnull(SUM(fhj), 0) - ifnull(SUM(fhj_qn), 0), 2) fzje_hj,
        case when ifnull(SUM(fhj_qn), 0) = 0 then null
        else round((ifnull(SUM(fhj), 0) - ifnull(SUM(fhj_qn), 0)) / ifnull(SUM(fhj_qn), 0) * 100, 2)
        end fzjb_hj,
        round(ifnull(SUM(fqxj), 0) - ifnull(SUM(fqxj_qn), 0), 2) fzje_qxj,
        case when ifnull(SUM(fqxj_qn), 0) = 0 then null
        else round((ifnull(SUM(fqxj), 0) - ifnull(SUM(fqxj_qn), 0)) / ifnull(SUM(fqxj_qn), 0) * 100, 2)
        end fzjb_qxj
        from (
        SELECT
        fhyml fhyml,
        SUM(case when year(
        <if test="fStartSkssqq != null and fStartSkssqq != ''">fskssqq</if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">frkrq</if>) = #{start_year} then srfx.fhj else 0
        end)/10000 fhj,
        SUM(case when year(
        <if test="fStartSkssqq != null and fStartSkssqq != ''">fskssqq</if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">frkrq</if>) = #{start_year} then srfx.fzyj else 0
        end)/10000 fzyj,
        SUM(case when year(
        <if test="fStartSkssqq != null and fStartSkssqq != ''">fskssqq</if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">frkrq</if>) = #{start_year} then srfx.fssj else 0
        end)/10000 fssj,
        SUM(case when year(
        <if test="fStartSkssqq != null and fStartSkssqq != ''">fskssqq</if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">frkrq</if>) = #{start_year} then srfx.fdsj else 0
        end)/10000 fdsj,
        SUM(case when year(
        <if test="fStartSkssqq != null and fStartSkssqq != ''">fskssqq</if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">frkrq</if>) = #{start_year} then srfx.fqxj else 0
        end)/10000 fqxj,
        SUM(case when year(
        <if test="fStartSkssqq != null and fStartSkssqq != ''">fskssqq</if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">frkrq</if>) = #{start_year} - 1 then srfx.fhj else 0
        end)/10000 fhj_qn,
        SUM(case when year(
        <if test="fStartSkssqq != null and fStartSkssqq != ''">fskssqq</if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">frkrq</if>) = #{start_year} - 1 then srfx.fzyj else 0
        end)/10000 fzyj_qn,
        SUM(case when year(
        <if test="fStartSkssqq != null and fStartSkssqq != ''">fskssqq</if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">frkrq</if>) = #{start_year} - 1 then srfx.fssj else 0
        end)/10000 fssj_qn,
        SUM(case when year(
        <if test="fStartSkssqq != null and fStartSkssqq != ''">fskssqq</if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">frkrq</if>) = #{start_year} - 1 then srfx.fdsj else 0
        end)/10000 fdsj_qn,
        SUM(case when year(
        <if test="fStartSkssqq != null and fStartSkssqq != ''">fskssqq</if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">frkrq</if>) = #{start_year} - 1 then srfx.fqxj else 0
        end)/10000 fqxj_qn
        FROM tb_dw_srfx_srfx_main srfx
        WHERE 1=1
        <if test="fStartSkssqq != null and fStartSkssqq != ''">and
            (
            srfx.fskssqq BETWEEN #{fStartSkssqq} and #{fEndSkssqq}
            OR srfx.fskssqq BETWEEN #{fStartSkssqqLastYear} AND #{fEndSkssqqLastYear}
            )
        </if>
        <if test="fStartSkssqq == null or fStartSkssqq == ''">and
            (srfx.frkrq BETWEEN CONCAT(#{start_year}, '-', #{fstartmonth}, '-01') AND
            last_day(str_to_date(CONCAT(#{start_year}, '-', #{fendmonth}), '%Y-%m'))
            OR srfx.frkrq BETWEEN CONCAT(#{start_year} - 1, '-', #{fstartmonth}, '-01') AND
            last_day(str_to_date(CONCAT(#{start_year} - 1, '-', #{fendmonth}), '%Y-%m'))
            )
        </if>
        <if test="fssqyList != null">AND
            fskgk IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null">AND
            fzsxm IN
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhydlList != null">AND
            fhydl IN
            <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzcdjlxStr != null and fzcdjlxStr != ''">AND
            fdjzclx in (${fzcdjlxStr})
        </if>
        <if test="fldts == 2">AND
            fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        GROUP BY fhyml
        ) a
        GROUP BY fhyml WITH ROLLUP
        order by grouping(fhyml) desc, fhj * 1 desc
    </select>

    <select id="statisticsByIndustryV2" parameterType="java.util.Map" resultType="java.util.Map">
        select case when fhyml is null then '合计' else fhyml end fhyml,
        round(SUM(fhj), 2) fhj,
        round(SUM(fhj_qn), 2) fhj_qn,
        round(ifnull(SUM(fhj), 0) - ifnull(SUM(fhj_qn), 0), 2) fzje_hj,
        case when ifnull(SUM(fhj_qn), 0) = 0 then null
        else round((ifnull(SUM(fhj), 0) - ifnull(SUM(fhj_qn), 0)) / ifnull(SUM(fhj_qn), 0) * 100, 2)
        end fzjb_hj
        from (
        SELECT
        fhyml fhyml,
        SUM(case when SUBSTR(frkrq, 1, 4) = #{start_year} then srfx.fje else 0
        end)/10000 fhj,
        SUM(case when SUBSTR(frkrq, 1, 4) = #{start_year} - 1 then srfx.fje else 0
        end)/10000 fhj_qn
        FROM TB_DW_ZHZS_HYSZ_GEN srfx
        WHERE 1=1
        and(
            str_to_date(frkrq, '%Y-%m') BETWEEN str_to_date(CONCAT(#{start_year}, '-', #{fstartmonth}, '-01'), '%Y-%m-%d') AND
            last_day(str_to_date(CONCAT(#{start_year}, '-', #{fendmonth}, '-01'), '%Y-%m-%d'))
            or
            str_to_date(frkrq, '%Y-%m') BETWEEN str_to_date(CONCAT(#{start_year} - 1, '-', #{fstartmonth}, '-01'), '%Y-%m-%d') AND
            last_day(str_to_date(CONCAT(#{start_year} - 1, '-', #{fendmonth}, '-01'), '%Y-%m-%d'))
        )
        <if test="fzsxmStr != null and fzsxmStr != ''">
            AND fsz IN (${fzsxmStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">
            AND fhydl IN (${fhydlStr})
        </if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        and fsz in ('教育费附加','地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        GROUP BY fhyml
        ) a
        GROUP BY fhyml WITH ROLLUP
        order by grouping(fhyml) desc, fhj * 1 desc
    </select>

    <!--按登记类型统计    statisticsByRegistrationType-->
    <select id="statisticsByRegistrationType" parameterType="com.hnbp.local.sszbfx.model.QueryBusinessStatistics"
            resultType="com.hnbp.local.sszbfx.model.TaxStatistics">
        select
        case when grouping(登记注册类型)=1 then '合计' else 登记注册类型 end fzcdjlx,
        grouping(登记注册类型)fpx,
        round(sum(本年累计) / 10000, 2) yeartotal,
        round(sum(上年同期累计) / 10000, 2) lastYeartotal,
        round((sum(本年累计) - sum(上年同期累计)) / 10000, 2) fzje,
        round(sum(今年中央级) / 10000, 2) as fjnqzzyj,
        round(sum(去年中央级) / 10000, 2) as fqnqzzyj,
        round(sum(今年省市级) / 10000, 2) as fjnqzssj,
        round(sum(去年省市级) / 10000, 2) as fqnqzssj,
        round(sum(今年地市级) / 10000, 2) as fjnqzdsj,
        round(sum(去年地市级) / 10000, 2) as fqnqzdsj,
        round(sum(今年区县级) / 10000, 2) as fjnqzqxj,
        round(sum(去年区县级) / 10000, 2) as fqnqzqxj,
        case
        when sum(上年同期累计) = 0 then
        0
        else
        round((sum(本年累计) - sum(上年同期累计)) / sum(上年同期累计), 4) * 100
        end yearOverYear
        from (
        select fdjzclx 登记注册类型,
        round(sum(fhj), 2) 本年累计,
        0 as 上年同期累计,
        sum(fzyj) 今年中央级,
        0 as 去年中央级,
        sum(fssj) 今年省市级,
        0 as 去年省市级,
        sum(fdsj) 今年地市级,
        0 as 去年地市级,
        sum(fqxj) 今年区县级,
        0 as 去年区县级
        from tb_dw_srfx_srfx_main
        where 1 = 1
        <if test="fssqyList != null and fssqyList.size>0">
            and fskgk in
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null and fzsxmList.size>0">
            and fzsxm in
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhydlList != null and fhydlList.size>0">
            and fhydl in
            <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fStartSkssqq != null and fStartSkssqq != ''">and
            fskssqq between
            str_to_date(#{fStartSkssqq}, '%Y-%m-%d') and
            str_to_date(#{fEndSkssqq}, '%Y-%m-%d')
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">and
            frkrq between
            str_to_date(#{fStartRkrq}, '%Y-%m-%d') and
            str_to_date(#{fEndRkrq}, '%Y-%m-%d')
        </if>
        group by fdjzclx
        union all
        select fdjzclx fzcdjlx,
        0 as 本年累计,
        round(sum(fhj), 2) 去年税收合计,
        0 as 上年同期累计,
        sum(fzyj) as 去年中央级,
        0 as 今年省市级,
        sum(fssj) as 去年省市级,
        0 as 今年地市级,
        sum(fdsj) as 去年地市级,
        0 as 今年区县级,
        sum(fqxj) as 去年区县级
        from tb_dw_srfx_srfx_main
        where 1 = 1
        <if test="fssqyList != null and fssqyList.size>0">
            and fskgk in
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null and fzsxmList.size>0">
            and fzsxm in
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhydlList != null and fhydlList.size>0">
            and fhydl in
            <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fStartSkssqqLastYear != null and fStartSkssqqLastYear != ''">and
            fskssqq between
            str_to_date(#{fStartSkssqqLastYear}, '%Y-%m-%d') and
            str_to_date(#{fEndSkssqqLastYear}, '%Y-%m-%d')
        </if>
        <if test="fStartRkrqLastYear != null and fStartRkrqLastYear != ''">and
            frkrq between
            str_to_date(#{fStartRkrqLastYear}, '%Y-%m-%d') and
            str_to_date(#{fEndRkrqLastYear}, '%Y-%m-%d')
        </if>
        group by fdjzclx) as a
        where 登记注册类型 is not null
        group by 登记注册类型 with rollup
        order by fpx desc,yeartotal desc
    </select>


    <!--纳税净入库通用查询 WarehousingGeneralQuery-->
    <select id="warehousingGeneralQuery" parameterType="com.hnbp.local.sszbfx.model.QueryBusinessStatistics"
            resultType="com.hnbp.local.sszbfx.model.commonQuery.WarehousingGeneralQuery">
        select
        case when grouping( concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''), ifnull(fzsxm, ''), ifnull(fzspm, ''),
        ifnull(fskssqq, ''), ifnull(fskssqz, ''), ifnull(fzcdjlx, ''), ifnull(fhyml, ''), ifnull(fsl, ''), ifnull(frkrq,
        '')
        , ifnull(fskssswjg, ''), ifnull(fyskm, ''), ifnull(fjdxz, ''), ifnull(fsksx, ''), ifnull(fsjly, ''))) = 1 then
        concat('合计（共', count(1), '条）') else max( fnsrsbh ) end fnsrsbh,
        case when grouping( concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''), ifnull(fzsxm, ''), ifnull(fzspm, ''),
        ifnull(fskssqq, ''), ifnull(fskssqz, ''), ifnull(fzcdjlx, ''), ifnull(fhyml, ''), ifnull(fsl, ''), ifnull(frkrq,
        '')
        , ifnull(fskssswjg, ''), ifnull(fyskm, ''), ifnull(fjdxz, ''), ifnull(fsksx, ''), ifnull(fsjly, ''))) = 1 then
        '' else max( fnsrmc ) end fnsrmc,
        case when grouping( concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''), ifnull(fzsxm, ''), ifnull(fzspm, ''),
        ifnull(fskssqq, ''), ifnull(fskssqz, ''), ifnull(fzcdjlx, ''), ifnull(fhyml, ''), ifnull(fsl, ''), ifnull(frkrq,
        '')
        , ifnull(fskssswjg, ''), ifnull(fyskm, ''), ifnull(fjdxz, ''), ifnull(fsksx, ''), ifnull(fsjly, ''))) = 1 then
        '' else max( fzsxm ) end fzsxm,
        case when grouping( concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''), ifnull(fzsxm, ''), ifnull(fzspm, ''),
        ifnull(fskssqq, ''), ifnull(fskssqz, ''), ifnull(fzcdjlx, ''), ifnull(fhyml, ''), ifnull(fsl, ''), ifnull(frkrq,
        '')
        , ifnull(fskssswjg, ''), ifnull(fyskm, ''), ifnull(fjdxz, ''), ifnull(fsksx, ''), ifnull(fsjly, ''))) = 1 then
        '' else max( fzspm ) end fzspm,
        case when grouping( concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''), ifnull(fzsxm, ''), ifnull(fzspm, ''),
        ifnull(fskssqq, ''), ifnull(fskssqz, ''), ifnull(fzcdjlx, ''), ifnull(fhyml, ''), ifnull(fsl, ''), ifnull(frkrq,
        '')
        , ifnull(fskssswjg, ''), ifnull(fyskm, ''), ifnull(fjdxz, ''), ifnull(fsksx, ''), ifnull(fsjly, ''))) = 1 then
        '' else max( fskssqq ) end fskssqq,
        case when grouping( concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''), ifnull(fzsxm, ''), ifnull(fzspm, ''),
        ifnull(fskssqq, ''), ifnull(fskssqz, ''), ifnull(fzcdjlx, ''), ifnull(fhyml, ''), ifnull(fsl, ''), ifnull(frkrq,
        '')
        , ifnull(fskssswjg, ''), ifnull(fyskm, ''), ifnull(fjdxz, ''), ifnull(fsksx, ''), ifnull(fsjly, ''))) = 1 then
        '' else max( fskssqz ) end fskssqz,
        case when grouping( concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''), ifnull(fzsxm, ''), ifnull(fzspm, ''),
        ifnull(fskssqq, ''), ifnull(fskssqz, ''), ifnull(fzcdjlx, ''), ifnull(fhyml, ''), ifnull(fsl, ''), ifnull(frkrq,
        '')
        , ifnull(fskssswjg, ''), ifnull(fyskm, ''), ifnull(fjdxz, ''), ifnull(fsksx, ''), ifnull(fsjly, ''))) = 1 then
        '' else max( fzcdjlx ) end fzcdjlx,
        case when grouping( concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''), ifnull(fzsxm, ''), ifnull(fzspm, ''),
        ifnull(fskssqq, ''), ifnull(fskssqz, ''), ifnull(fzcdjlx, ''), ifnull(fhyml, ''), ifnull(fsl, ''), ifnull(frkrq,
        '')
        , ifnull(fskssswjg, ''), ifnull(fyskm, ''), ifnull(fjdxz, ''), ifnull(fsksx, ''), ifnull(fsjly, ''))) = 1 then
        '' else max( fhyml ) end fhyml,
        case when grouping( concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''), ifnull(fzsxm, ''), ifnull(fzspm, ''),
        ifnull(fskssqq, ''), ifnull(fskssqz, ''), ifnull(fzcdjlx, ''), ifnull(fhyml, ''), ifnull(fsl, ''), ifnull(frkrq,
        '')
        , ifnull(fskssswjg, ''), ifnull(fyskm, ''), ifnull(fjdxz, ''), ifnull(fsksx, ''), ifnull(fsjly, ''))) = 1 then
        '' else max( fhydl ) end fhydl,
        case when grouping( concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''), ifnull(fzsxm, ''), ifnull(fzspm, ''),
        ifnull(fskssqq, ''), ifnull(fskssqz, ''), ifnull(fzcdjlx, ''), ifnull(fhyml, ''), ifnull(fsl, ''), ifnull(frkrq,
        '')
        , ifnull(fskssswjg, ''), ifnull(fyskm, ''), ifnull(fjdxz, ''), ifnull(fsksx, ''), ifnull(fsjly, ''))) = 1 then
        '' else max( fhyzl ) end fhyzl,
        case when grouping( concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''), ifnull(fzsxm, ''), ifnull(fzspm, ''),
        ifnull(fskssqq, ''), ifnull(fskssqz, ''), ifnull(fzcdjlx, ''), ifnull(fhyml, ''), ifnull(fsl, ''), ifnull(frkrq,
        '')
        , ifnull(fskssswjg, ''), ifnull(fyskm, ''), ifnull(fjdxz, ''), ifnull(fsksx, ''), ifnull(fsjly, ''))) = 1 then
        '' else max( fhy ) end fhy,
        case when grouping( concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''), ifnull(fzsxm, ''), ifnull(fzspm, ''),
        ifnull(fskssqq, ''), ifnull(fskssqz, ''), ifnull(fzcdjlx, ''), ifnull(fhyml, ''), ifnull(fsl, ''), ifnull(frkrq,
        '')
        , ifnull(fskssswjg, ''), ifnull(fyskm, ''), ifnull(fjdxz, ''), ifnull(fsksx, ''), ifnull(fsjly, ''))) = 1 then
        '' else max( fsl ) end fsl,
        case when grouping( concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''), ifnull(fzsxm, ''), ifnull(fzspm, ''),
        ifnull(fskssqq, ''), ifnull(fskssqz, ''), ifnull(fzcdjlx, ''), ifnull(fhyml, ''), ifnull(fsl, ''), ifnull(frkrq,
        '')
        , ifnull(fskssswjg, ''), ifnull(fyskm, ''), ifnull(fjdxz, ''), ifnull(fsksx, ''), ifnull(fsjly, ''))) = 1 then
        '' else max( frkrq ) end frkrq,
        case when grouping( concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''), ifnull(fzsxm, ''), ifnull(fzspm, ''),
        ifnull(fskssqq, ''), ifnull(fskssqz, ''), ifnull(fzcdjlx, ''), ifnull(fhyml, ''), ifnull(fsl, ''), ifnull(frkrq,
        '')
        , ifnull(fskssswjg, ''), ifnull(fyskm, ''), ifnull(fjdxz, ''), ifnull(fsksx, ''), ifnull(fsjly, ''))) = 1 then
        '' else max( fskssswjg ) end fskssswjg,
        case when grouping( concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''), ifnull(fzsxm, ''), ifnull(fzspm, ''),
        ifnull(fskssqq, ''), ifnull(fskssqz, ''), ifnull(fzcdjlx, ''), ifnull(fhyml, ''), ifnull(fsl, ''), ifnull(frkrq,
        '')
        , ifnull(fskssswjg, ''), ifnull(fyskm, ''), ifnull(fjdxz, ''), ifnull(fsksx, ''), ifnull(fsjly, ''))) = 1 then
        '' else max( fyskm ) end fyskm,
        case when grouping( concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''), ifnull(fzsxm, ''), ifnull(fzspm, ''),
        ifnull(fskssqq, ''), ifnull(fskssqz, ''), ifnull(fzcdjlx, ''), ifnull(fhyml, ''), ifnull(fsl, ''), ifnull(frkrq,
        '')
        , ifnull(fskssswjg, ''), ifnull(fyskm, ''), ifnull(fjdxz, ''), ifnull(fsksx, ''), ifnull(fsjly, ''))) = 1 then
        '' else max( fjdxz ) end fjdxz,
        case when grouping( concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''), ifnull(fzsxm, ''), ifnull(fzspm, ''),
        ifnull(fskssqq, ''), ifnull(fskssqz, ''), ifnull(fzcdjlx, ''), ifnull(fhyml, ''), ifnull(fsl, ''), ifnull(frkrq,
        '')
        , ifnull(fskssswjg, ''), ifnull(fyskm, ''), ifnull(fjdxz, ''), ifnull(fsksx, ''), ifnull(fsjly, ''))) = 1 then
        '' else max( fsksx ) end fsksx,
        case when grouping( concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''), ifnull(fzsxm, ''), ifnull(fzspm, ''),
        ifnull(fskssqq, ''), ifnull(fskssqz, ''), ifnull(fzcdjlx, ''), ifnull(fhyml, ''), ifnull(fsl, ''), ifnull(frkrq,
        '')
        , ifnull(fskssswjg, ''), ifnull(fyskm, ''), ifnull(fjdxz, ''), ifnull(fsksx, ''), ifnull(fsjly, ''))) = 1 then
        '' else max( fsjly ) end fsjly,
        sum(fjsyj) fjsyj,
        sum(fsjje) fsjje,
        round(sum(nvl(中央级, 0)), 2) fzyj,
        round(sum(nvl(省市级, 0)), 2) fsdj,
        round(sum(nvl(地市级, 0)), 2) fdsj,
        round(sum(nvl(区县级, 0)), 2) fqxj,
        case when sum(fsjje) = 0 then 0 else round(sum(中央级) / sum(fsjje), 4) * 100 end as fzyjbl,
        case when sum(fsjje) = 0 then 0 else round(sum(省市级) / sum(fsjje), 4) * 100 end as fsdjbl,
        case when sum(fsjje) = 0 then 0 else round(sum(地市级) / sum(fsjje), 4) * 100 end as fdsjbl,
        case when sum(fsjje) = 0 then 0 else round(sum(区县级) / sum(fsjje), 4) * 100 end as fqxjbl
        from (
        select
        fnsrsbh fnsrsbh,
        fnsrmc fnsrmc,
        fzsxm fzsxm,
        fzspm fzspm,
        fskssqq fskssqq,
        fskssqz fskssqz,
        fdjzclx fzcdjlx,
        fhyml fhyml,
        fhydl fhydl,
        fhyzl fhyzl,
        fhy fhy,
        fjsyj fjsyj,
        case when instr(CONCAT(fsl,''),'.')=1 then CONCAT('0',fsl,'') else CONCAT(fsl,'') end fsl,
        fhj fsjje,
        frkrq frkrq,
        fssswjg fskssswjg,
        fskgk fskgk,
        fyskm fyskm,
        fjdxz fjdxz,
        fsksx fsksx,
        fsjly fsjly,
        fzyj 中央级,
        fssj 省市级,
        fdsj 地市级,
        fqxj 区县级
        from tb_dw_srfx_srfx_main
        where
        1=1
        <if test="fssqyList != null and fssqyList.size>0">
            and fskgk in
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null and fzsxmList.size>0">
            and fzsxm in
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhydlList != null and fhydlList.size>0">
            and fhydl in
            <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>

        <if test="fnsrmc != null and fnsrmc != ''">and
            fnsrmc like CONCAT('%' , #{fnsrmc} , '%')
        </if>
        <if test="fnsrsbh != null and fnsrsbh != ''">and
            fnsrsbh = #{fnsrsbh}
        </if>

        <if test="fStartSkssqq != null and fStartSkssqq != ''">and
            fskssqq between date_format(concat(#{fStartSkssqq},'-01'),'%Y-%m-%d') and
            last_day(date_format(concat(#{fEndSkssqq},'-01'),'%Y-%m-%d'))
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">and
            frkrq between date_format(concat(#{fStartRkrq},'-01'),'%Y-%m-%d') and
            last_day(date_format(concat(#{fEndRkrq},'-01'),'%Y-%m-%d'))
        </if>
        <if test="fyskmList != null and fyskmList.size>0">
            and fyskm in
            <foreach collection="fyskmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzcdjlxList != null and fzcdjlxList.size>0">
            and fdjzclx in
            <foreach collection="fzcdjlxList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        ) as z
        group by concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''), ifnull(fzsxm, ''), ifnull(fzspm, '')
        , ifnull(fskssqq, ''), ifnull(fskssqz, ''), ifnull(fzcdjlx, ''), ifnull(fhyml, '')
        , ifnull(fsl, ''), ifnull(frkrq, ''), ifnull(fskssswjg, ''), ifnull(fyskm, '')
        , ifnull(fjdxz, ''), ifnull(fsksx, ''), ifnull(fsjly, '')) with rollup
        order by grouping( concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''), ifnull(fzsxm, ''), ifnull(fzspm, ''),
        ifnull(fskssqq, ''), ifnull(fskssqz, ''), ifnull(fzcdjlx, ''), ifnull(fhyml, ''), ifnull(fsl, ''), ifnull(frkrq,
        '')
        , ifnull(fskssswjg, ''), ifnull(fyskm, ''), ifnull(fjdxz, ''), ifnull(fsksx, ''), ifnull(fsjly, ''))) desc
        , fjsyj desc
    </select>
    <!--分户分税种明细查询  DetailsOfHouseholdTaxInquiry-->
    <select id="detailsOfHouseholdTaxInquiry" parameterType="com.hnbp.local.sszbfx.model.QueryBusinessStatistics"
            resultType="com.hnbp.local.sszbfx.model.commonQuery.DetailsOfHouseholdTaxInquiry">
        select
        grouping(concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''))) fpx,
        case when grouping(concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''))) = 1 then '合计' else fnsrsbh end fnsrsbh,
        case when grouping(concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, ''))) = 1 then '' else fnsrmc end fnsrmc,
        round(sum(fhj), 2) fhj,
        round(sum(fshj), 2) fshj,
        round(sum(ffhj), 2) ffhj,
        round(sum(fzzs), 2) fzzs,
        round(sum(fgrsds), 2) fgrsds,
        round(sum(fgdzys), 2) fgdzys,
        round(sum(fyhs), 2) fyhs,
        round(sum(ffcs), 2) ffcs,
        round(sum(ftdzzs), 2) ftdzzs,
        round(sum(fqysds), 2) fqysds,
        round(sum(fyys), 2) fyys,
        round(sum(fzys), 2) fzys,
        round(sum(fcswhjss), 2) fcswhjss,
        round(sum(fqs), 2) fqs,
        round(sum(fcztdsys), 2) fcztdsys,
        round(sum(fhjbhs), 2) fhjbhs,
        round(sum(fxfs), 2) fxfs,
        round(sum(fccs), 2) fccs,
        round(sum(fclgzs), 2) fclgzs,
        round(sum(fdfjyfj), 2) fdfjyfj,
        round(sum(fqtsr), 2) fqtsr,
        round(sum(fsljszxsr), 2) fsljszxsr,
        round(sum(fcjrjybzj), 2) fcjrjybzj,
        round(sum(fwhsyjsf), 2) fwhsyjsf,
        round(sum(fjyffj), 2) fjyffj,
        round(sum(fyanys), 2) fyanys,
        round(sum(fzfbmfmsr), 2) fzfbmfmsr,
        round(sum(fqtzfxjjsr), 2) fqtzfxjjsr,
        round(sum(fzyj), 2) fzyj,
        round(sum(fsdj), 2) fsdj,
        round(sum(fdsj), 2) fdsj,
        round(sum(fqxj), 2) fqxj
        from (select fnsrsbh fnsrsbh,
        fnsrmc fnsrmc,
        round(sum(fhj) / 10000, 2) fhj,
        round(sum(fshj) / 10000, 2) fshj,
        round(sum(ffhj) / 10000, 2) ffhj,
        round(sum(case when fzsxm ='增值税' then fhj/10000 else 0 end),2) fzzs,
        round(sum(case when fzsxm ='个人所得税' then fhj/10000 else 0 end),2) fgrsds,
        round(sum(case when fzsxm ='耕地占用税' then fhj/10000 else 0 end),2) fgdzys,
        round(sum(case when fzsxm ='印花税' then fhj/10000 else 0 end),2) fyhs,
        round(sum(case when fzsxm ='房产税' then fhj/10000 else 0 end),2) ffcs,
        round(sum(case when fzsxm ='土地增值税' then fhj/10000 else 0 end),2) ftdzzs,
        round(sum(case when fzsxm ='企业所得税' then fhj/10000 else 0 end),2) fqysds,
        round(sum(case when fzsxm ='营业税' then fhj/10000 else 0 end),2) fyys,
        round(sum(case when fzsxm ='资源税' then fhj/10000 else 0 end),2) fzys,
        round(sum(case when fzsxm ='城市维护建设税' then fhj/10000 else 0 end),2) fcswhjss,
        round(sum(case when fzsxm ='契税' then fhj/10000 else 0 end),2) fqs,
        round(sum(case when fzsxm ='城镇土地使用税' then fhj/10000 else 0 end),2) fcztdsys,
        round(sum(case when fzsxm ='环境保护税' then fhj/10000 else 0 end),2) fhjbhs,
        round(sum(case when fzsxm ='消费税' then fhj/10000 else 0 end),2) fxfs,
        round(sum(case when fzsxm ='车船税' then fhj/10000 else 0 end),2) fccs,
        round(sum(case when fzsxm ='车辆购置税' then fhj/10000 else 0 end),2) fclgzs,
        round(sum(case when fzsxm ='烟叶税' then fhj/10000 else 0 end),2) fyanys,
        round(sum(case when fzsxm ='地方教育附加' then fhj/10000 else 0 end),2) fdfjyfj,
        round(sum(case when fzsxm ='其他收入' then fhj/10000 else 0 end),2) fqtsr,
        round(sum(case when fzsxm ='水利建设专项收入' then fhj/10000 else 0 end),2) fsljszxsr,
        round(sum(case when fzsxm ='残疾人就业保障金' then fhj/10000 else 0 end),2) fcjrjybzj,
        round(sum(case when fzsxm ='文化事业建设费' then fhj/10000 else 0 end),2) fwhsyjsf,
        round(sum(case when fzsxm ='教育费附加' then fhj/10000 else 0 end),2) fjyffj,
        round(sum(case when fzsxm ='税务部门罚没收入' then fhj/10000 else 0 end),2) fzfbmfmsr,
        round(sum(case when fzsxm ='其他政府性基金收入' then fhj/10000 else 0 end),2) fqtzfxjjsr,
        round(sum(fzyj) / 10000, 2) as fzyj,
        round(sum(fssj) / 10000, 2) as fsdj,
        round(sum(fdsj) / 10000, 2) as fdsj,
        round(sum(fqxj) / 10000, 2) as fqxj
        from tb_dw_srfx_srfx_main
        where 1 = 1
        and fzsxm in
        (
        '增值税','个人所得税','耕地占用税','印花税','房产税','土地增值税','企业所得税'
        ,'营业税','资源税','城市维护建设税','契税','城镇土地使用税','环境保护税','消费税'
        ,'车船税','车辆购置税','烟叶税','地方教育附加','其他收入','水利建设专项收入'
        ,'残疾人就业保障金','文化事业建设费','教育费附加','税务部门罚没收入','其他政府性基金收入'
        )
        <if test="fnsrmc != null and fnsrmc != ''">and
            fnsrmc like CONCAT('%' , #{fnsrmc} , '%')
        </if>
        <if test="fnsrsbh != null and fnsrsbh != ''">and
            fnsrsbh = #{fnsrsbh}
        </if>
        <if test="fStartSkssqq != null and fStartSkssqq != ''">and
            fskssqq between
            str_to_date(#{fStartSkssqq}, '%Y-%m-%d ') and
            str_to_date(#{fEndSkssqq}, '%Y-%m-%d ')
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">AND
            frkrq between str_to_date(#{fStartRkrq}, '%Y-%m-%d ') and
            str_to_date(#{fEndRkrq}, '%Y-%m-%d ')
        </if>
        <if test="fssqyList != null and fssqyList.size>0">
            and fskgk in
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null and fzsxmList.size>0">
            and fzsxm in
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhydlList != null and fhydlList.size>0">
            and fhydl in
            <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzcdjlxList != null and fzcdjlxList.size>0">
            and fdjzclx in
            <foreach collection="fzcdjlxList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhymlList != null and fhymlList.size>0">
            and fhyml in
            <foreach collection="fhymlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fyskmList != null and fyskmList.size>0">
            and fyskm in
            <foreach collection="fyskmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        group by fnsrsbh, fnsrmc,fzsxm
        having 1=1
        <if test="start_je != null and start_je != ''">and
            round(sum(合计) / 10000, 2) &gt;= #{start_je}
        </if>
        <if test="end_je != null and end_je != ''">and
            round(sum(合计) / 10000, 2) &lt; #{end_je}
        </if>
        ) as z
        group by concat(ifnull(fnsrsbh, ''), ifnull(fnsrmc, '')) with rollup
        order by fpx desc,fhj desc
    </select>

    <!--纳税人纳税情况查询 getnsrnsqkcx  taxpayerTaxInformationInquiry-->
    <select id="taxpayerTaxInformationInquiry" parameterType="com.hnbp.local.sszbfx.model.QueryBusinessStatistics"
            resultType="com.hnbp.local.sszbfx.model.commonQuery.DetailsOfHouseholdTaxInquiry">
        select
        case
        when grouping(year(frkrq)) = 1 or grouping(concat(year(frkrq), MONTH(frkrq), fnsrmc)) = 1 then ''
        else max(fnsrsbh)
        end fnsrsbh,
        case
        when grouping(year(frkrq)) = 1 then '99999'
        when grouping(concat(year(frkrq), MONTH(frkrq), fnsrmc)) = 1 then CONCAT(year(frkrq),'13')
        else year(frkrq)
        end yeardesc,
        case
        when grouping(year(frkrq)) = 1 then '合计'
        when grouping(concat(year(frkrq), MONTH(frkrq), fnsrmc)) = 1 then ''
        else max(fnsrmc)
        end fnsrmc,
        case
        when grouping(concat(year(frkrq), MONTH(frkrq), fnsrmc)) + grouping(year(frkrq))= 1 then CONCAT(year(frkrq)
        ,'年小计')
        else year(frkrq)
        end year,
        case
        when grouping(concat(year(frkrq), MONTH(frkrq), fnsrmc)) = 0 then concat(year(frkrq), '-',MONTH(frkrq))
        else ''
        end yearmonth,
        case
        when grouping(concat(year(frkrq), MONTH(frkrq), fnsrmc)) = 0 then max(fhyml)
        else ''
        end fhy,
        case
        when grouping(concat(year(frkrq), MONTH(frkrq), fnsrmc)) = 0 then max(fskgk)
        else ''
        end fskgk,
        round(sum(fhj) / 10000, 2) fhj,
        round(sum(fshj) / 10000, 2) fshj,
        round(sum(ffhj) / 10000, 2) ffhj,
        round(sum(case when fzsxm ='增值税' then fhj/10000 else 0 end),2) fzzs,
        round(sum(case when fzsxm ='个人所得税' then fhj/10000 else 0 end),2) fgrsds,
        round(sum(case when fzsxm ='耕地占用税' then fhj/10000 else 0 end),2) fgdzys,
        round(sum(case when fzsxm ='印花税' then fhj/10000 else 0 end),2) fyhs,
        round(sum(case when fzsxm ='房产税' then fhj/10000 else 0 end),2) ffcs,
        round(sum(case when fzsxm ='土地增值税' then fhj/10000 else 0 end),2) ftdzzs,
        round(sum(case when fzsxm ='企业所得税' then fhj/10000 else 0 end),2) fqysds,
        round(sum(case when fzsxm ='营业税' then fhj/10000 else 0 end),2) fyys,
        round(sum(case when fzsxm ='资源税' then fhj/10000 else 0 end),2) fzys,
        round(sum(case when fzsxm ='城市维护建设税' then fhj/10000 else 0 end),2) fcswhjss,
        round(sum(case when fzsxm ='契税' then fhj/10000 else 0 end),2) fqs,
        round(sum(case when fzsxm ='城镇土地使用税' then fhj/10000 else 0 end),2) fcztdsys,
        round(sum(case when fzsxm ='环境保护税' then fhj/10000 else 0 end),2) fhjbhs,
        round(sum(case when fzsxm ='消费税' then fhj/10000 else 0 end),2) fxfs,
        round(sum(case when fzsxm ='车船税' then fhj/10000 else 0 end),2) fccs,
        round(sum(case when fzsxm ='车辆购置税' then fhj/10000 else 0 end),2) fclgzs,
        round(sum(case when fzsxm ='烟叶税' then fhj/10000 else 0 end),2) fyanys,
        round(sum(case when fzsxm ='地方教育附加' then fhj/10000 else 0 end),2) fdfjyfj,
        round(sum(case when fzsxm ='其他收入' then fhj/10000 else 0 end),2) fqtsr,
        round(sum(case when fzsxm ='水利建设专项收入' then fhj/10000 else 0 end),2) fsljszxsr,
        round(sum(case when fzsxm ='残疾人就业保障金' then fhj/10000 else 0 end),2) fcjrjybzj,
        round(sum(case when fzsxm ='文化事业建设费' then fhj/10000 else 0 end),2) fwhsyjsf,
        round(sum(case when fzsxm ='教育费附加' then fhj/10000 else 0 end),2) fjyffj,
        round(sum(case when fzsxm ='税务部门罚没收入' then fhj/10000 else 0 end),2) fzfbmfmsr,
        round(sum(case when fzsxm ='其他政府性基金收入' then fhj/10000 else 0 end),2) fqtzfxjjsr,

        round(sum(fzyj) / 10000, 2) as fzyj,
        round(sum(fssj) / 10000, 2) as fsdj,
        round(sum(fdsj) / 10000, 2) as fdsj,
        round(sum(fqxj) / 10000, 2) as fqxj
        from tb_dw_srfx_srfx_main
        where 1 = 1
        and fzsxm in
        (
        '增值税','个人所得税','耕地占用税','印花税','房产税','土地增值税','企业所得税'
        ,'营业税','资源税','城市维护建设税','契税','城镇土地使用税','环境保护税','消费税'
        ,'车船税','车辆购置税','烟叶税','地方教育附加','其他收入','水利建设专项收入'
        ,'残疾人就业保障金','文化事业建设费','教育费附加','税务部门罚没收入','其他政府性基金收入'
        )
        <if test="fnsrmc != null and fnsrmc != ''">and
            纳税人名称 like CONCAT('%' , #{fnsrmc} , '%')
        </if>
        <if test="fnsrsbh != null and fnsrsbh != ''">and
            纳税人识别号 = #{fnsrsbh}
        </if>
        <if test="fStartSkssqq != null and fStartSkssqq != ''">and
            税款所属期起 between
            str_to_date(#{fStartSkssqq}, '%Y-%m-%d ') and
            str_to_date(#{fEndSkssqq}, '%Y-%m-%d ')
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">and
            入库日期 between
            str_to_date(#{fStartRkrq}, '%Y-%m-%d ') and
            str_to_date(#{fEndRkrq}, '%Y-%m-%d ')
        </if>
        <if test="fssqyList != null and fssqyList.size>0">
            and 收款国库 in
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null and fzsxmList.size>0">
            and 征收项目 in
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhydlList != null and fhydlList.size>0">
            and 行业大类 in
            <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzcdjlxList != null and fzcdjlxList.size>0">
            and 登记注册类型 in
            <foreach collection="fzcdjlxList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>

        <if test="fyskmList != null and fyskmList.size>0">
            and 预算科目 in
            <foreach collection="fyskmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        group by year(入库日期), concat(year(入库日期), MONTH(入库日期), 纳税人名称),fzsxm with rollup
        order by yeardesc desc
    </select>


    <!--税收总额排名 TotalTaxRanking-->
    <select id="totalTaxRanking" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select fnsrmc,round(yearTotal/10000, 2) yearTotal,
               round(lastYearTotal/10000, 2) lastYearTotal, round(fromTotal/10000, 2) fromTotal
        ,case when fnsrmc = '合计' then round((yearTotal-lastYearTotal)/10000,2) else round(fjnzje/10000,2) end fjnzje
        ,case when fnsrmc = '合计' then round((lastYearTotal-fromTotal)/10000,2) else round(fqnzje/10000,2) end fqnzje
        ,case when fnsrmc = '合计' then case when nvl(lastYearTotal,0) = 0 then 0 else round(((yearTotal-lastYearTotal)/lastYearTotal) *100,2) end else yearOnYearGrowth end yearOnYearGrowth
        ,case when fnsrmc = '合计' then case when nvl(fromTotal,0) = 0 then 0 else round(((lastYearTotal-fromTotal)/fromTotal)*100,2) end else lastYearGrowth end lastYearGrowth
        from
        (
        select case when fnsrmc is null then '合计' else fnsrmc end fnsrmc,
        sum(yearTotal) yearTotal,
        sum(fjnzje) fjnzje,
        sum(fqnzje) fqnzje,
        sum(lastYearTotal) lastYearTotal,
        sum(fromTotal) fromTotal,
        sum(yearOnYearGrowth) yearOnYearGrowth,
        sum(lastYearGrowth) lastYearGrowth


        from (select fnsrmc,
        round(sum(今年总合计),2) yearTotal,
        round(sum(去年总合计),2) lastYearTotal,
        round(sum(前年总合计),2) fromTotal,
        round(sum(今年总合计) - sum(去年总合计),2) fjnzje,
        round(sum(去年总合计) - sum(前年总合计),2) fqnzje,
        case
        when sum(去年总合计) = 0 then 0
        when sum(去年总合计) is null then 0
        when sum(去年总合计) = '' then 0
        else round((sum(今年总合计) - sum(去年总合计)) / sum(去年总合计) * 100, 2)
        end yearOnYearGrowth,
        case
        when sum(前年总合计) = 0 then 0
        when sum(前年总合计) is null then 0
        when sum(前年总合计) = '' then 0
        else round((sum(去年总合计) - sum(前年总合计)) / sum(前年总合计) * 100, 2)
        end lastYearGrowth


        from (select fnsrmc,
        <choose>
            <when test="frkkj == '全口径税收'">
                sum(fhj)
            </when>
            <when test="frkkj == '地方税收'">
                sum(fdfsr)
            </when>
        </choose>
        as 今年总合计,0 去年总合计,0 前年总合计
        from tb_dw_srfx_srfx_main where frkrq between str_to_date(CONCAT(#{frk_year}, '-', #{frk_startMonth}, '-01'), '%Y-%m-%d')
        and last_day(str_to_date(CONCAT(#{frk_year}, '-', #{frk_endMonth}, '-01'), '%Y-%m-%d'))
        AND (fnsrmc IS NOT NULL)
        and fnsrmc not in (select distinct fqymc from TB_DW_ZHZS_WBNSRMD_GEN)
        and fhyml !='房地产业' and fhyml !='建筑业' and fnsrsbh not like '1%' and CHAR_length(fnsrmc) > 4

        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fssqyList != null">AND
            fskgk IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null">AND
            fzsxm IN
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhydlList != null">AND
            <!-- 标准版需要区分这个地方的写法， 我觉得标准版应该使用税收表的行业大类作为条件，最后展示的所属行业门类还是最大的那个缴税行业门类 -->
            fhydl IN
            <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        group by fnsrmc
        having round(sum(case when '全口径税收' = '全口径税收' then fhj when '全口径税收' = '地方税收' then fdfsr end
        /10000)
        , 2)
        <if test="yeartotal != null and yeartotal != ''">
            >= #{yeartotal}
        </if>

        union all
        select fnsrmc
        , 0 今年总合计,

        <choose>
            <when test="frkkj == '全口径税收'">
                sum(fhj)
            </when>
            <when test="frkkj == '地方税收'">
                sum(fdfsr)
            </when>
        </choose>
        as
        去年总合计 ,0 前年总合计

        from tb_dw_srfx_srfx_main where frkrq between str_to_date(CONCAT(#{frk_year}-1, '-', #{frk_startMonth}, '-01'), '%Y-%m-%d')
        and last_day(str_to_date(CONCAT(#{frk_year}-1, '-', #{frk_endMonth}, '-01'), '%Y-%m-%d'))
        AND (fnsrmc IS NOT NULL)
        and fnsrmc not in (select distinct fqymc from TB_DW_ZHZS_WBNSRMD_GEN)
        and fhyml !='房地产业' and fhyml !='建筑业' and fnsrsbh not like '1%' and CHAR_length(fnsrmc) > 4

        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fssqyList != null">AND
            fskgk IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null">AND
            fzsxm IN
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhydlList != null">AND
            <!-- 标准版需要区分这个地方的写法， 我觉得标准版应该使用税收表的行业大类作为条件，最后展示的所属行业门类还是最大的那个缴税行业门类 -->
            fhydl IN
            <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        group by fnsrmc

        union all
        select fnsrmc, 0 今年总合计,0 去年总合计
        ,
        <choose>
            <when test="frkkj == '全口径税收'">
                sum(fhj)
            </when>
            <when test="frkkj == '地方税收'">
                sum(fdfsr)
            </when>
        </choose>  as 前年总合计 from tb_dw_srfx_srfx_main where frkrq between str_to_date(CONCAT(#{frk_year}-2, '-', #{frk_startMonth}, '-01'),
        '%Y-%m-%d')
        and last_day(str_to_date(CONCAT(#{frk_year}-2, '-', #{frk_endMonth}, '-01'), '%Y-%m-%d'))
        AND (fnsrmc IS NOT NULL)
        and fnsrmc not in (select distinct fqymc from TB_DW_ZHZS_WBNSRMD_GEN)
        and fhyml !='房地产业' and fhyml !='建筑业' and fnsrsbh not like '1%' and CHAR_length(fnsrmc) > 4

        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fssqyList != null">AND
            fskgk IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null">AND
            fzsxm IN
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhydlList != null">AND
            <!-- 标准版需要区分这个地方的写法， 我觉得标准版应该使用税收表的行业大类作为条件，最后展示的所属行业门类还是最大的那个缴税行业门类 -->
            fhydl IN
            <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        group by fnsrmc) t1

        group by FNSRMC
        having (sum(今年总合计) >= '100')

        order by FNSRMC) t2

        <where>
            <if test="fzjf != null and fzjf != ''">
                <if test="fzjf &gt; 0">
                    <if test="fzjfType == 1">
                        and yearOnYearGrowth &gt;= #{fzjf}
                    </if>
                    <if test="fzjfType == 2">
                        and yearOnYearGrowth &gt;= #{fzjf}
                        and lastYearGrowth &gt;=#{fzjf}
                    </if>
                </if>
                <if test="fzjf &lt;= 0">
                    <if test="fzjfType == 1">
                        and yearOnYearGrowth &lt;= #{fzjf}
                    </if>
                    <if test="fzjfType == 2">
                        and yearOnYearGrowth &lt;= #{fzjf}
                        and lastYearGrowth &lt;=#{fzjf}
                    </if>
                </if>
            </if>
            <if test="fzje != null and fzje != ''">
                <if test="fzje &gt; 0">
                    <if test="fzjfType == 1">
                        and fjnzje / 10000 &gt;= #{fzje}
                    </if>
                    <if test="fzjfType == 2">
                        and fjnzje / 10000 &gt;= #{fzje}
                        and fqnzje / 10000&gt;=#{fzje}
                    </if>
                </if>
                <if test="fzje &lt;= 0">
                    <if test="fzjfType == 1">
                        and fjnzje/ 10000 &lt;= #{fzje}
                    </if>
                    <if test="fzjfType == 2">
                        and fjnzje/ 10000 &lt;= #{fzje}
                        and fqnzje/ 10000 &lt;=#{fzje}
                    </if>
                </if>
            </if>
        </where>
        group by fnsrmc with rollup
        ) b order by case when fnsrmc = '合计' then 1 else 0 end desc , yearTotal desc
    </select>

    <!--企业利润变化情况表   enterpriseProfitChanges-->
    <select id="enterpriseProfitChanges" parameterType="com.hnbp.local.sszbfx.model.QueryBusinessStatistics"
            resultType="com.hnbp.local.sszbfx.model.CorporateTaxAnalysis.TotalTaxRanking">
        select case when grouping(纳税人名称) = 1 then '合计' else 纳税人名称 end fnsrmc,
        round(sum(今年税收), 2) yearTotal,
        round(sum(去年税收), 2) lastYearTotal,
        round(sum(前年税收), 2) fromTotal,
        case when sum(去年税收) = 0 then 0 else round((sum(今年税收) - sum(去年税收)) / sum(去年税收) * 100, 2) end as
        YearOnYearGrowth,
        case when sum(前年税收) = 0 then 0 else round((sum(去年税收) - sum(前年税收)) / sum(前年税收) * 100, 2) end as
        lastYearGrowth
        from (select case when 纳税人名称 = 1 then '合计' else 纳税人名称 end 纳税人名称,
        sum(今年税收) 今年税收,
        sum(去年税收) 去年税收,
        sum(前年税收) 前年税收,
        case when sum(去年税收) = 0 then 0 else round((sum(今年税收) - sum(去年税收)) / sum(去年税收) * 100,2) end as
        今年同比,
        case when sum(前年税收) = 0 then 0 else round((sum(去年税收) - sum(前年税收)) / sum(前年税收) * 100,2) end as
        去年同比
        from (
        select
        纳税人名称,
        case when 年份 = #{start_year} then sum(fjsyj) / 10000 else 0 end as 今年税收,
        case when 年份 = #{start_year}-1 then sum(fjsyj) / 10000 else 0 end as 去年税收,
        case when 年份 = #{start_year}-2 then sum(fjsyj) / 10000 else 0 end as 前年税收
        from
        (
        select 计税依据 fjsyj,
        纳税人名称,
        to_char(入库日期,'yyyy') 年份, 收款国库 fskgk
        from ZHZS_T_SS_SRFX
        where 征收项目 = '企业所得税'
        and 入库日期 between str_to_date(left(#{start_year}-2,4),'%Y') and
        str_to_date(CONCAT(#{start_year},'-','12','-','31'),'%Y-%m-%d')
        group by 纳税人名称
        ) as t7
        where 1=1
        <if test="fssqyList != null and fssqyList.size>0">
            and fskgk in
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        group by 纳税人名称, 年份
        ) as t3
        group by (纳税人名称)) as t4
        where length(纳税人名称) &gt; 4
        <if test="fzjf &gt; 0">
            <if test="fzjfType == 1">and
                今年同比 &gt;= #{fzjf}
            </if>
            <if test="fzjfType != 1">and
                今年同比 &gt;= #{fzjf} and 去年同比&gt;=#{fzjf}
            </if>
        </if>
        <if test="fzjf &lt;= 0">
            <if test="fzjfType == 1">and
                今年同比 &lt;= #{fzjf}
            </if>
            <if test="fzjfType != 1">and
                今年同比 &lt;= #{fzjf} and 去年同比&lt;=#{fzjf}
            </if>
        </if>
        and 今年税收 &gt;= #{yeartotal}
        group by 纳税人名称 with rollup
        order by yearTotal desc
    </select>


    <!--饼图-->
    <!--增值税分行业情况(饼图) getQQDetailZZHY  VATSectorInformationEcharts-->
    <select id="VATSectorInformationEcharts" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select case
                   when fhyml is null then
                       '其他'
                   else
                       fhyml
                   end name,
               case
                   when round(sum(fzzs) / 10000, 2) &lt; 0 then
                       0
                   else
                       round(sum(fzzs) / 10000, 2)
                   end as value
        from tb_dw_srfx_srfx_main a
        where fzzs != 0
          and frkrq between str_to_date(CONCAT(#{start_year}
            , '-'
            , #{fstartmonth})
            , '%Y-%m')
          and last_day(str_to_date(CONCAT(#{start_year}
            , '-'
            , #{fendmonth})
            , '%Y-%m'))
        group by fhyml
        order by value desc
    </select>
    <!--增值税分行业情况明细(饼图) getQQDetailZZHYDL     DetailsOfVATIndustryEcharts-->
    <select id="detailsOfVATIndustryEcharts" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select fhydl as name, round(sum(fzzs) / 10000, 2) as value
        from tb_dw_srfx_srfx_main a
        where fzzs != 0
          and frkrq between str_to_date(CONCAT(#{start_year}
            , '-'
            , #{fstartmonth})
            , '%Y-%m')
          and last_day(str_to_date(CONCAT(#{start_year}
            , '-'
            , #{fendmonth})
            , '%Y-%m'))
          and fhyml = #{fhyml}
        group by fhyml
        order by value desc
    </select>

    <!--增值税分行业情况明细(表格) getJDNSRPMByZZHY   DetailsOfVATIndustryTable-->
    <select id="detailsOfVATIndustryTable" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.local.sszbfx.model.CorporateTaxAnalysis.DetailsOfVATIndustry">
        select
        case when grouping(fnsrmc) = 1 then '合计' else fnsrmc end fnsrmc,
        case when grouping(fnsrmc) = 1 then '' else fhyml end fhyml,
        case when grouping(fnsrmc) = 1 then '' else fhydl end fhydl,
        round(sum(nvl(本年增值税, 0)) / 10000, 2) as yearTotal,
        round(sum(nvl(去年增值税, 0)) / 10000, 2) as lastYearTotal,
        case
        when nvl(sum(去年增值税), 0) = 0 then
        0
        else
        round(sum(本年增值税 - 去年增值税) * 100 / sum(去年增值税), 2)
        end as YearOnYearGrowth
        from (
        select
        fzzs 本年增值税,
        0 as 去年增值税,
        fnsrmc fnsrmc,
        fhyml fhyml,
        fhydl fhydl
        from tb_dw_srfx_srfx_main sz
        where fzzs != 0
        and frkrq between str_to_date(CONCAT(#{start_year} ,'-' , #{fstartmonth}),'%Y-%m') and
        last_day(str_to_date(CONCAT(#{start_year} ,'-'
        , #{fendmonth}),'%Y-%m'))
        union all
        select
        0 as 本年增值税,
        fzzs 去年增值税,
        fnsrmc fnsrmc,
        fhyml fhyml,
        fhydl fhydl
        from tb_dw_srfx_srfx_main sz
        where fzzs != 0
        and frkrq between str_to_date(CONCAT(#{start_year} -1 ,'-' , #{fstartmonth}),'%Y-%m') and
        last_day(str_to_date(CONCAT(#{start_year} -1
        ,'-' , #{fendmonth}),'%Y-%m'))
        ) as t5
        where 1 = 1
        <if test="fhyml != null and fhyml != ''">and
            fhyml = #{fhyml}
        </if>
        <if test="fhydl != null and fhydl != ''">and
            fhydl = #{fhydl}
        </if>
        group by fnsrmc with rollup order by yearTotal desc
    </select>
    <!--注册企业增长分析 getzcqyzzfx registeredBusinessGrowthAnalysis-->
    <select id="registeredBusinessGrowthAnalysis" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        WITH
        new_tax_source AS(
        select FNSRMC, fzcrq, fdjzclx from(
        SELECT FNSRMC, fdjrq as fzcrq, fjdxz as FSWSSJG_XSQ,fdjzclx
        , row_number() over(partition by fnsrmc order by fdjrq asc) rn
        FROM tb_dw_srfx_swdjxx_main where fnsrzt='正常' and fkzztdjlx like '单位%'
        ) a
        WHERE rn=1
        <if test="fStartDjrq != null and fStartDjrq != ''">AND
            fzcrq BETWEEN #{fStartDjrq} AND #{fEndDjrq}
        </if>
        <if test="fssqyList != null">AND
            FSWSSJG_XSQ IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        ),
        ss AS(
        SELECT
        a.fnsrmc 纳税人名称 , max(b.fskgk) fssqy
        , SUM(fhj) fhj
        , SUM(fzyj) fzyj, SUM(fssj) fssj, SUM(fdsj) fdsj, SUM(fqxj) fqxj,max(b.fhyml) fhy
        FROM tb_dw_srfx_srfx_main a
        left join tb_dw_zhzs_nsr_hyml_skgk_gen b
        on a.fnsrmc = b.fnsrmc
        WHERE ifnull(a.fnsrmc, '') != ''
        <if test="fStartRkrq != null and fStartRkrq != ''">AND
            a.frkrq BETWEEN #{fStartRkrq} AND #{fEndRkrq}
        </if>
        <if test="fssqyList != null">AND
            b.fskgk IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null">AND
            a.fzsxm IN
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhydlList != null">AND
            a.fhydl IN
            <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fldts == 2">AND
            a.fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND a.fyskm != '免抵调增增值税'</if>
        GROUP BY a.fnsrmc
        <if test="start_je != null and start_je != ''">HAVING
            fhj &gt;= 10000*#{start_je}
        </if>
        )
        SELECT
        if(grouping(t1.FNSRMC)=1,'合计',fnsrmc) fnsrmc
        ,if(grouping(t1.FNSRMC)=1,'',max(t1.fzcrq)) fzcsj
        ,if(grouping(t1.FNSRMC)=1,'',max(t2.fhy)) fhy
        ,if(grouping(t1.FNSRMC)=1,'',max(t2.fssqy)) fssqy
        ,ROUND(sum(t2.fhj)/10000, 2) fhj_jn
        ,ROUND(sum(t2.fzyj)/10000, 2) fzyj_jn
        ,ROUND(sum(t2.fssj)/10000, 2) fssj_jn
        ,ROUND(sum(t2.fdsj)/10000, 2) fdsj_jn
        ,ROUND(sum(t2.fqxj)/10000, 2) fqxj_jn
        FROM new_tax_source t1 INNER JOIN ss t2 ON t1.FNSRMC = t2.纳税人名称
        where 1 = 1
        <if test="fnsrzt == 1">AND
            case when t2.fhj = '' then null else t2.fhj end &gt; 0
        </if>
        <if test="fnsrzt == 2">AND
            case when t2.fhj = '' then null else t2.fhj end IS NULL
        </if>
        group by t1.FNSRMC with rollup
        ORDER BY if(grouping(t1.FNSRMC)=1,1,2),fhj_jn DESC
    </select>

    <select id="enterpriseScaleAnalysis2" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        WITH
        ssTab AS(
        select fnsrmc, ssjn
        , case
        when ssjn &gt;= 10000 * 10000 then '大于一亿'
        when ssjn &gt;= 5000 * 10000 then '五千万至一亿'
        when ssjn &gt;= 1000 * 10000 then '一千万至五千万'
        when ssjn &gt;= 500 * 10000 then '五百万至一千万'
        when ssjn &gt;= 50 * 10000 then '五十万至五百万'
        else '小于五十万'
        end fssgm
        from (
        SELECT
        a.fnsrmc fnsrmc
        , SUM(case when #{frkkj} = '全口径税收' then a.fhj when #{frkkj} = '地方税收' then a.fdfsr end) ssjn
        FROM tb_dw_srfx_srfx_main a
        WHERE fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="fStartRkrq != null and fStartRkrq != ''">AND
            a.frkrq BETWEEN str_to_date(concat(#{fStartRkrq},'-01') , '%Y-%m-%d')
                AND last_day(str_to_date(concat(#{fEndRkrq},'-01'), '%Y-%m-%d'))
        </if>
        <if test="fssqyList != null">AND
            a.fskgk IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null">AND
            a.fzsxm IN
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fldts == 2">AND
            a.fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND a.fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        GROUP BY a.fnsrmc
        ) a
        ),
        ssqnTab AS(
        select fnsrmc, ssqn
        , case
        when ssqn &gt;= 10000 * 10000 then '大于一亿'
        when ssqn &gt;= 5000 * 10000 then '五千万至一亿'
        when ssqn &gt;= 1000 * 10000 then '一千万至五千万'
        when ssqn &gt;= 500 * 10000 then '五百万至一千万'
        when ssqn &gt;= 50 * 10000 then '五十万至五百万'
        else '小于五十万'
        end fssgm
        from (
        SELECT
        a.fnsrmc fnsrmc
        , SUM(case when #{frkkj} = '全口径税收' then a.fhj when #{frkkj} = '地方税收' then a.fdfsr end) ssqn
        FROM tb_dw_srfx_srfx_main a
        WHERE fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        <if test="fStartRkrqLastYear != null and fStartRkrqLastYear != ''">AND
            a.frkrq BETWEEN str_to_date(concat(#{fStartRkrqLastYear},'-01'), '%Y-%m-%d') AND
            last_day(str_to_date(concat(#{fEndRkrqLastYear},'-01'), '%Y-%m-%d'))
        </if>
        <if test="fssqyList != null">AND
            a.fskgk IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null">AND
            a.fzsxm IN
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fldts == 2">AND
            a.fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND a.fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        GROUP BY a.fnsrmc
        ) a
        )
        select IF(GROUPING(fssgm) = 1, '合计', fssgm) fssgm
        , round(sum(ssjn), 2) ssjn, sum(hsjn) hsjn, round(sum(ssqn), 2) ssqn, sum(hsqn) hsqn
        , round(ifnull(sum(ssjn), 0) - ifnull(sum(ssqn), 0), 2) as sszje
        , round(ifnull(sum(hsjn), 0) - ifnull(sum(hsqn), 0), 2) as hszje
        , case when ifnull(sum(ssqn), 0) = 0 then null else round((ifnull(sum(ssjn), 0) - ifnull(sum(ssqn), 0)) /
        ifnull(sum(ssqn), 0) * 100, 2) end as sszjb
        , case when ifnull(sum(hsqn), 0) = 0 then null else round((ifnull(sum(hsjn), 0) - ifnull(sum(hsqn), 0)) /
        ifnull(sum(hsqn), 0) * 100, 2) end as hszjb
        from (
        SELECT fssgm, sum(ssjn) / 10000 ssjn, round(count(1),2) hsjn, 0 ssqn, 0 hsqn FROM ssTab group by fssgm
        union all
        SELECT fssgm, 0 ssjn, 0 hsjn, sum(ssqn) / 10000 ssqn, round(count(1),2) hsqn FROM ssqnTab group by fssgm
        ) a
        GROUP BY fssgm WITH ROLLUP
        order by grouping(fssgm) desc
        , case
        when fssgm = '大于一亿' then 1
        when fssgm = '五千万至一亿' then 2
        when fssgm = '一千万至五千万' then 3
        when fssgm = '五百万至一千万' then 4
        when fssgm = '五十万至五百万' then 5
        else 6 end
    </select>


    <!--重点税源企业分析   getzdsyfxtj  AnalysisOfKeyTaxSources-->
    <select id="analysisOfKeyTaxSources" parameterType="com.hnbp.local.sszbfx.model.QueryBusinessStatistics"
            resultType="com.hnbp.local.sszbfx.model.CorporateTaxAnalysis.AnalysisOfKeyTaxSources">
        select fsshy,
        纳税人名称 fnsrmc,
        round(税收本年累计 / 10000, 2) yearTotal,
        round(税收去年累计 / 10000, 2) lastYearTotal,
        round((税收本年累计-税收去年累计) / 10000, 2) as ftbzje,
        case
        when (税收去年累计) = 0 then
        0
        else
        round((税收本年累计 - 税收去年累计) * 100 / 税收去年累计, 2)
        end ftbzjbj,
        round(增值税本年累计 / 10000, 2) fzzsbnlj,
        round(增值税去年累计 / 10000, 2) fzzsqnlj,
        (round(增值税本年累计 / 10000, 2) - round(增值税去年累计 / 10000, 2)) as fzzszje,
        case
        when (增值税去年累计) = 0 then
        0
        else
        round((增值税本年累计 - 增值税去年累计) * 100 / 增值税去年累计, 2)
        end fzzstqzjbl,
        round(企业所得税本年累计 / 10000, 2) fqysdsbnlj,
        round(企业所得税去年累计 / 10000, 2) fqysdsqnlj,
        (round(企业所得税本年累计 / 10000, 2) - round(企业所得税去年累计 / 10000, 2)) as fqysdszje,
        case
        when (企业所得税去年累计) = 0 then
        0
        else
        round((企业所得税本年累计 - 企业所得税去年累计) * 100 / 企业所得税去年累计,
        2)
        end fqysdstqzjbl
        from (select fsshy,
        case when grouping(fsshy) = 1 then '合计' else case when grouping(fname)= 1 then '小计' else fname end end
        纳税人名称,
        fname,
        sum(税收本年累计) 税收本年累计,
        sum(税收去年累计) 税收去年累计,
        sum(增值税本年累计) 增值税本年累计,
        sum(增值税去年累计) 增值税去年累计,
        sum(企业所得税本年累计) 企业所得税本年累计,
        sum(企业所得税去年累计) 企业所得税去年累计
        from (select case when fsshy in ('金融业', '保险业') then '金融保险业'
        else
        fsshy
        end fsshy,
        fname,
        合计 as 税收本年累计,
        0 as 税收去年累计,
        增值税 as 增值税本年累计,
        0 as 增值税去年累计,
        企业所得税 as 企业所得税本年累计,
        0 as 企业所得税去年累计
        from ZHZS_T_SS_SRFX a
        right join (select *
        from zhzs_srfx_keycompanies
        where 1 = 1
        <if test="fssqyList != null and fssqyList.size>0">
            and fssdq in
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fsshyList != null and fsshyList.size>0">
            and fsshy in
            <foreach collection="fsshyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>

        <if test="fnsrmc != null and fnsrmc != ''">and
            fname like CONCAT('%' , #{fnsrmc} , '%')
        </if>) k
        on a.纳税人名称 = k.fname
        <if test="fStartSkssqq != null and fStartSkssqq != ''">and
            税款所属期起 between
            str_to_date(#{fStartSkssqq}, '%Y-%m-%d ') and
            str_to_date(#{fEndSkssqq}, '%Y-%m-%d ')
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">and
            入库日期 between
            str_to_date(#{fStartRkrq}, '%Y-%m-%d ') and
            str_to_date(#{fEndRkrq}, '%Y-%m-%d ')
        </if>
        <if test="fssqyList != null and fssqyList.size>0">
            and 收款国库 in
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzcdjlxList != null and fzcdjlxList.size>0">
            and 登记注册类型 in
            <foreach collection="fzcdjlxList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fyskmList != null and fyskmList.size>0">
            and 征收项目 in
            <foreach collection="fyskmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>

        union all
        select case when fsshy in ('金融业', '保险业') then
        '金融保险业'
        else
        fsshy
        end fsshy,
        fname,
        0 as 税收本年累计,
        合计 as 税收去年累计,
        0 as 增值税本年累计,
        增值税 as 增值税去年累计,
        0 as 企业所得税今年累计,
        企业所得税 as 企业所得税去年累计
        from ZHZS_T_SS_SRFX a
        right join (select * from zhzs_srfx_keycompanies where 1 = 1
        <if test="fssqyList != null and fssqyList.size>0">
            and fssdq in
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fsshyList != null and fsshyList.size>0">
            and fsshy in
            <foreach collection="fsshyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>

        <if test="fnsrmc != null and fnsrmc != ''">and
            fname like CONCAT('%' , #{fnsrmc} , '%')
        </if>) k
        on a.纳税人名称 = k.fname
        <if test="fStartSkssqqLastYear != null and fStartSkssqqLastYear != ''">and
            税款所属期起 between
            str_to_date(#{fStartSkssqqLastYear}, '%Y-%m-%d ') and
            str_to_date(#{fEndSkssqqLastYear}, '%Y-%m-%d ')
        </if>

        <if test="fStartRkrqLastYear != null and fStartRkrqLastYear != ''">and
            入库日期 between
            str_to_date(#{fStartRkrqLastYear}, '%Y-%m-%d ') and
            str_to_date(#{fEndRkrqLastYear}, '%Y-%m-%d ')
        </if>
        <if test="fssqyList != null and fssqyList.size>0">
            and 收款国库 in
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzcdjlxList != null and fzcdjlxList.size>0">
            and a.登记注册类型 in
            <foreach collection="fzcdjlxList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null and fzsxmList.size>0">
            and 征收项目 in
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>) as t7
        group by fsshy, fname with rollup
        order by case when fsshy = '' then '001' else fsshy end ,
        case when fname = '' then '001' else fname end ,
        税收本年累计 desc) as t8
    </select>

    <!--个人所得税增收品目分析 (饼图)   AnalysisOfAdditionalItemsEcharts-->
    <select id="analysisOfAdditionalItemsEcharts" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select case
                   when fzspm like '%1%' then
                       '其他'
                   else
                       fzspm
                   end as name,
               round(sum(fsjje) / 10000, 2) as value
        from (select fzsxm, fzspm, fsl, 合计 as fsjje, 年份 f_sys_year, 月份 f_sys_month
            from zhzs_srfx_ss_all
            where fzsxm = '个人所得税') as t1
        where 1=1
          and f_sys_year = #{start_year}
          and f_sys_month &gt;= #{fstartmonth}
          and f_sys_month &lt;= #{fendmonth}
        group by fzspm
        order by value desc
    </select>
    <!--个人所得税增收品目分析 (表格)  AnalysisOfAdditionalItemsTable-->
    <select id="analysisOfAdditionalItemsTable" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.local.sszbfx.model.CorporateTaxAnalysis.AnalysisOfAdditionalItems">
        select case when INSTR(fzspm, ' ') &gt; 0
        then concat('按' , (case when substr(SUBSTR(fzspm, instr(fzspm, ' ') + 1) * 100, 1, 1) = '.'then concat('0' ,
        SUBSTR(fzspm, instr(fzspm, ' ') + 1) * 100) else concat(SUBSTR(fzspm, instr(fzspm, ' ') + 1) * 100 , '%税率')
        end))
        else concat( '***' , fzspm , '***' )
        end as fzspm,
        bn yearTotal,
        qn lastYearTotal,
        tb YearOnYearGrowth
        from (
        select fzspm, sum(bn) bn, sum(qn) qn,
        case
        when sum(qn) = 0 then
        0
        else
        round(sum(bn - qn) * 100 / sum(qn), 2)
        end as tb
        from (
        select fzspm,
        round(sum(bn) / 10000, 2) bn,
        round(sum(qn) / 10000, 2) qn
        from (
        select fzspm,
        (fsjje) bn,
        0 as qn
        from zhzs_srfx_zspm
        where 1=1
        and f_sys_year = #{start_year}
        and f_sys_month &gt;= #{fstartmonth}
        and f_sys_month &lt;= #{fendmonth}
        <if test="fzspm != null and fzspm != ''">and
            fzspm = #{fzspm}
        </if>
        union all
        select fzspm,
        0 as bn,
        (fsjje) qn
        from zhzs_srfx_zspm
        where 1=1
        and f_sys_year = #{start_year}-1
        and f_sys_month &gt;= #{fstartmonth}
        and f_sys_month &lt;= #{fendmonth}
        <if test="fzspm != null and fzspm != ''">and
            fzspm = #{fzspm}
        </if>
        ) t1
        group by fzspm
        ) t2
        group by fzspm
        union all
        select
        CONCAT(fzspm , ' ' , (case when substr(fsl, 1, 1) = '.' then CONCAT('0' , fsl) else fsl end)),
        bn,
        qn,
        tb
        from (select fzspm,
        fsl,
        round(sum(bn) / 10000, 2) bn,
        round(sum(qn) / 10000, 2) qn,
        case when sum(qn) = 0 then 0 else
        round(sum(bn - qn) * 100 / sum(qn), 2)end as tb
        from (
        select fzspm,
        case when substr(fsl, 1, 1)='.' then CONCAT('0' , fsl) else fsl end fsl,
        (fsjje) bn,
        0 as qn
        from zhzs_srfx_zspm
        where 1=1
        and f_sys_year = #{start_year}
        and f_sys_month &gt;= #{fstartmonth}
        and f_sys_month &lt;= #{fendmonth}
        <if test="fzspm != null and fzspm != ''">and
            fzspm = #{fzspm}
        </if>
        union all
        select fzspm,
        case when substr(fsl, 1, 1) = '.' then CONCAT('0' , fsl) else fsl end fsl,
        0 as bn,
        (fsjje) qn
        from zhzs_srfx_zspm
        where 1=1
        and f_sys_year = #{start_year}-1
        and f_sys_month &gt;= #{fstartmonth}
        and f_sys_month &lt;= #{fendmonth}
        <if test="fzspm != null and fzspm != ''">and
            fzspm = #{fzspm}
        </if>
        ) t4
        group by fzspm, fsl
        order by fzspm) t3
        ) t5
        where 1 = 1
        <if test="fzspm == null or fzspm == ''">and
            INSTR(fzspm, ' ') &lt;= 0
        </if>
        order by fzspm
    </select>
    <!--个人所得税增收品目分析明细 (饼图)     AnalysisOfAdditionalItemsDetailEchart-->
    <select id="analysisOfAdditionalItemsDetailEchart" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select fsl name, round(sum(fsjje) / 10000, 2) as value
        from (select fzsxm, fzspm, fsl, 合计 as fsjje, 年份 f_sys_year, 月份 f_sys_month
            from zhzs_srfx_ss_all
            where fzsxm = '个人所得税') as t1
        where 1=1
          and fzspm = #{fzspm}
          and f_sys_year = #{start_year}
          and f_sys_month &gt;= #{fstartmonth}
          and f_sys_month &lt;= #{fendmonth}
        group by fsl
        order by value desc
    </select>
    <!--企业所得税分行业(饼图) getQQDetailGRHY EnterpriseIncomeTaxSubsectorsEchart-->
    <select id="enterpriseIncomeTaxSubsectorsEchart" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select case
                   when fhyml is null then
                       '其他'
                   else
                       fhyml
                   end name,
               case
                   when sum(fqysds) &lt; 0 then
                       0
                   else
                       round(sum(fqysds) / 10000, 2)
                   end as value
        from tb_dw_srfx_srfx_main a
        where fqysds != 0
          and frkrq between str_to_date(CONCAT(#{start_year}
            , '-'
            , #{fstartmonth})
            , '%Y-%m')
          and last_day(str_to_date(CONCAT(#{start_year}
            , '-'
            , #{fendmonth})
            , '%Y-%m'))
        group by fhyml
        order by value desc
    </select>
    <!--企业所得税分行业明细(饼图) DetailsEnterpriseIncomeTaxSubsectorsEchart-->
    <select id="detailsEnterpriseIncomeTaxSubsectorsEchart" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select case
                   when fhydl is null then
                       '其他'
                   else
                       fhydl
                   end name,
               case
                   when sum(fqysds) &lt; 0 then
                       0
                   else
                       round(sum(fqysds) / 10000, 2)
                   end as value
        from tb_dw_srfx_srfx_main a
        where fqysds != 0
          and frkrq between str_to_date(CONCAT(#{start_year}
            , '-'
            , #{fstartmonth})
            , '%Y-%m')
          and last_day(str_to_date(CONCAT(#{start_year}
            , '-'
            , #{fendmonth})
            , '%Y-%m'))
          and fhyml =#{fhyml}
        group by fhydl
        order by value desc
    </select>

    <!--企业所得税分行业明细(表格) DetailsEnterpriseIncomeTaxSubsectorsTable-->
    <select id="detailsEnterpriseIncomeTaxSubsectorsTable"
            parameterType="com.hnbp.local.sszbfx.model.QueryBusinessStatistics"
            resultType="com.hnbp.local.sszbfx.model.CorporateTaxAnalysis.EnterpriseIncomeTaxSubsectors">
        select
        case when grouping(fnsrmc) = 1 then'合计' else fnsrmc end fnsrmc,
        case when grouping(fnsrmc) = 1 then'' else fhyml end fhyml,
        case when grouping(fnsrmc) = 1 then'' else fhydl end fhydl,
        round(sum(nvl(本年企业所得税, 0)) / 10000, 2) as yeartotal,
        round(sum(nvl(去年企业所得税, 0)) / 10000, 2) as lastyeartotal,
        case
        when nvl(sum(去年企业所得税), 0) = 0 then
        0
        else
        round(sum(本年企业所得税 - 去年企业所得税) * 100 / sum(去年企业所得税),
        2)
        end as YearOnYearGrowth
        from (
        select fqysds 本年企业所得税,
        0 as 去年企业所得税,
        fnsrmc fnsrmc,
        fhyml fhyml,
        fhydl fhydl
        from tb_dw_srfx_srfx_main sz
        where fqysds != 0
        and frkrq between str_to_date(CONCAT(#{start_year} ,'-' , #{fstartmonth}),'%Y-%m') and
        last_day(str_to_date(CONCAT(#{start_year} ,'-'
        , #{fendmonth}),'%Y-%m'))
        union all
        select 0 as 本年企业所得税,
        fqysds 去年企业所得税,
        fnsrmc fnsrmc,
        fhyml fhyml,
        fhydl fhydl
        from tb_dw_srfx_srfx_main
        where fqysds != 0
        and frkrq between str_to_date(CONCAT(#{start_year} -1 ,'-' , #{fstartmonth}),'%Y-%m') and
        last_day(str_to_date(CONCAT(#{start_year} -1
        ,'-' , #{fendmonth}),'%Y-%m'))
        ) as t1
        where 1 = 1
        <if test="fhyml != null and fhyml != ''">and
            fhyml = #{fhyml}
        </if>
        <if test="fhydl != null and fhydl != ''">and
            fhydl = #{fhydl}
        </if>
        group by fnsrmc with rollup
        order by yearTotal desc
    </select>
    <!--企业纳税规模分析  getQYInfo  enterpriseScaleAnalysis-->
    <select id="enterpriseScaleAnalysis" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        WITH
        swdj AS (
        SELECT a.fnsrmc
        FROM tb_dw_srfx_swdjxx_main a
        where 1 = 1
        <if test="fStartDjrq != null and fStartDjrq != ''">AND
            fkyslrq BETWEEN #{fStartDjrq} AND #{fEndDjrq}
        </if>
        group by fnsrmc
        ),
        ss AS(
        select fhyml,fnsrmc,sum(fhj) fhj
        from (
        select first_value(fhyml) over(partition BY fnsrmc order by fhj desc) fhyml,fnsrmc,fhj
        from(
        SELECT
        a.fnsrmc fnsrmc
        , max(fhyml) as fhyml
        , SUM(case when #{frkkj} = '全口径税收' then a.fhj when #{frkkj} = '地方税收' then a.fdfsr end) fhj
        FROM tb_dw_srfx_srfx_main a
        WHERE fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        and fhyml is not null
        <if test="frkrq != null and frkrq != ''">AND
            a.frkrq between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth},'-','01'),'%Y-%m-%d')
            and last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth},'-','01'),'%Y-%m-%d'))
        </if>
        <if test="fssqyStr != null and fssqyStr != ''">AND
            a.fskgk IN (${fssqyStr})
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">AND
            a.fzsxm IN (${fzsxmStr})
        </if>
        <if test="fldts == 2">AND
            a.fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND a.fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        GROUP BY a.fnsrmc,fhyml
        )a
        )aa
        group by fnsrmc,fhyml
        )
        SELECT
        IF(GROUPING(ss.fhyml) = 1, '合计', ss.fhyml) fhyml,
        SUM(IF(ss.fhj &gt;= 10000 * 10000, 1, 0)) fsyyqw, /*'大于一亿'*/
        SUM(IF(ss.fhj &gt;= 5000 * 10000 and ss.fhj &lt; 10000 * 10000, 1, 0)) fwzq, /*'五千万至一亿'*/
        SUM(IF(ss.fhj &gt;= 1000 * 10000 and ss.fhj &lt; 5000 * 10000, 1, 0)) fszw, /*'一千万至五千万'*/
        SUM(IF(ss.fhj &gt;= 500 * 10000 and ss.fhj &lt; 1000 * 10000, 1, 0)) fyze, /*'五百万至一千万'*/
        SUM(IF(ss.fhj &gt;= 50 * 10000 and ss.fhj &lt; 500 * 10000, 1, 0)) fwzs, /*'五十万至五百万'*/
        SUM(IF(ss.fhj &lt; 50 * 10000, 1, 0)) fxyw, /*'小于五'*/
        count(1) total
        FROM ss
        LEFT JOIN swdj ON ss.fnsrmc = swdj.fnsrmc
        WHERE 1=1 and ifnull(ss.fhj, '') != ''
        <if test="fStartDjrq != null and fStartDjrq != ''">AND
            swdj.fnsrmc is not null
        </if>
        GROUP BY ss.fhyml WITH ROLLUP
        order by grouping(ss.fhyml) desc, total desc
    </select>


    <!--企业纳税规模分析  getQYInfo  enterpriseScaleAnalysis-->
    <select id="enterpriseScaleAnalysisMx" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        WITH
        swdj AS(
        SELECT a.fnsrmc
        FROM tb_dw_srfx_swdjxx_main a
        WHERE 1=1
        <if test="fStartDjrq != null and fStartDjrq != ''">AND
            fkyslrq BETWEEN #{fStartDjrq} AND #{fEndDjrq}
        </if>
        group by fnsrmc
        ),
        ss AS(
        SELECT
        a.fnsrmc fnsrmc
        , max(b.fhyml) as fhyml
        , SUM(case when #{frkkj} = '全口径税收' then a.fhj when #{frkkj} = '地方税收' then a.fdfsr end) fhj
        FROM tb_dw_srfx_srfx_main a
        left join tb_dw_zhzs_nsr_hyml_skgk_gen b
        on a.fnsrmc = b.fnsrmc
        WHERE char_LENGTH(a.fnsrmc) &gt; 4
        <if test="frknf != null and frknf != ''">AND
            a.frkrq BETWEEN str_to_date(substr(#{frknf}, 1, 10), '%Y-%m-%d') AND str_to_date(substr(#{frknf}, -10, 10),
            '%Y-%m-%d')
        </if>
        <if test="fssqyList != null">AND
            b.fskgk IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null">AND
            a.fzsxm IN
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhydlList != null">AND
            a.fhydl IN
            <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fldts == 2">AND
            a.fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND a.fyskm != '免抵调增增值税'</if>
        <if test="fyskmStr != null and fyskmStr != ''">and
            fyskm in (${fyskmStr})
        </if>
        GROUP BY a.fnsrmc
        <if test="fsrlx == &quot;大于一亿&quot;">HAVING
            fhj &gt;= 10000*10000
        </if>
        <if test="fsrlx == &quot;五千万至一亿&quot;">HAVING
            fhj &gt;= 5000 * 10000 AND fhj &lt; 10000 * 10000
        </if>
        <if test="fsrlx == &quot;一千万至五千万&quot;">HAVING
            fhj &gt;= 1000 * 10000 AND fhj &lt; 5000 * 10000
        </if>
        <if test="fsrlx == &quot;五百万至一千万&quot;">HAVING
            fhj &gt;= 500 * 10000 AND fhj &lt; 1000 * 10000
        </if>
        <if test="fsrlx == &quot;五十万至五百万&quot;">HAVING
            fhj &gt;= 50 * 10000 AND fhj &lt; 500 * 10000
        </if>
        <if test="fsrlx == &quot;小于五十万&quot;">HAVING
            fhj &lt; 50 * 10000
        </if>
        )
        SELECT ss.fhyml, ss.fnsrmc, ROUND(ss.fhj/10000, 2) fhj
        FROM ss
        LEFT JOIN swdj ON ss.fnsrmc = swdj.fnsrmc
        where ifnull(ss.fhj, '') != ''
        <if test="fStartDjrq != null and fStartDjrq != ''">AND
            swdj.fnsrmc is not null
        </if>
        order by fhj desc
    </select>


    <!--查询产业对应行业门类行业大类信息-->
    <select id="findCy_hyml_hydl" parameterType="java.lang.String" resultType="java.util.HashMap">
        select *
        from (select CONCAT('cy', fcy_xh)              "id",
                     fcy                               "title",
                     '"{"type": "0", "checked": "0"}"' checkArr,
                     '0' as                            "parentId"
              from TB_DM_CYJS_CYHYXXB_GEN
              group by fcy, fcy_xh
              order by fcy_xh) as t1
        union all
        select *
        from (select CONCAT('ml', fml_xh) as           id
                   , fhyml             as           title
                   , '"{"type": "0", "checked": "0"}"' checkArr
                   , max(CONCAT('cy', fcy_xh))         parentId
              from TB_DM_CYJS_CYHYXXB_GEN
              group by fhyml, fml_xh
              order by fml_xh) as t2
        union all
        select *
        from (select CONCAT('dl', fdl_xh) as           id
                   , fhydl             as           title
                   , '"{"type": "0", "checked": "0"}"' checkArr
                   , max(CONCAT('ml', fml_xh))         parentId
              from TB_DM_CYJS_CYHYXXB_GEN
              group by fdl_xh, fhydl
              order by fdl_xh) as t3
    </select>

    <!--查询行业门类对应行业大类信息-->
    <select id="findCy_hyml_hydl_by_fyml" resultType="java.util.HashMap">
        select GROUP_CONCAT(fhydl) as 行业大类
        from (select fhydl, max(fdl_xh) fpx
              from TB_DM_CYJS_CYHYXXB_GEN
              where fhyml = #{fhyml}
              group by fhydl
              order by fpx) a
    </select>

    <!--查询注册登记类型信息-->
    <select id="findZcdjlx" resultType="java.util.HashMap">
        select fzcdjlx FZCDJLX
        from zhzs_srfx_zcdjlx
        order by fpx
    </select>

    <!--查询预算科目信息-->
    <select id="findYskm" resultType="java.util.HashMap">
        select fyskm fyskm
        from TB_DW_ZHZS_SRFXFYSKM_GEN
        order by fpx
    </select>

    <!--查询所属税务机关信息-->
    <select id="findFssswjg" resultType="java.util.HashMap">
        select fssswjg
        from zhzs_srfx_fssswjg
        order by fpx
    </select>

    <!--查询征收项目信息-->
    <select id="findZsxm" resultType="java.util.HashMap">
        select fzsxm FZSXM
        from TB_DW_ZHZS_SRFXFZSXM_GEN
        where (fzsxm in
               ('教育费附加', '地方教育附加', '增值税', '企业所得税', '契税', '土地增值税', '耕地占用税', '资源税',
                '房产税', '城市维护建设税', '车辆购置税', '城镇土地使用税', '车船税', '个人所得税', '烟叶税', '印花税',
                '船舶吨税', '消费税', '环境保护税', '营业税'))
        order by fpx
    </select>

    <!--查询用户对应收款国库权限，没有权限则是所有权限-->
    <select id="findFssqy" resultType="java.util.HashMap">
        select text, name, id, text FSSQY
        from (select fskgk as text, fskgk as name, fid as id, fpx
              from zhzs_bill_user_skgk
              where userid = #{userid}
              union all
              select fskgk as text, fskgk as name, fid as id, fpx
              from zhzs_bill_skgk
              where (select count(1) from zhzs_bill_user_skgk where userid = #{userid}) = 0) tab1
        order by fpx
    </select>

    <!--查询所属单位-->
    <select id="findFssdw" resultType="java.util.HashMap">
        select user_name text, user_name name, user_id id, user_name FSSQY
        from sys_user
        where user_name!='admin'
    </select>

    <!--查询纳税人状态信息-->
    <select id="findFnsrzt" resultType="java.util.HashMap">
        select fnsrzt FNSRZT
        from zhzs_swdj_fnsrzt
        order by fpx
    </select>

    <select id="statisticsByEconomicType" parameterType="com.hnbp.local.sszbfx.model.QueryBusinessStatistics"
            resultType="com.hnbp.local.sszbfx.model.TaxStatistics">
        select nvl(fjjlx,'合计') fjjlx,
        round(sum(本年累计) / 10000, 2) yeartotal,
        round(sum(上年同期累计) / 10000, 2) lastYeartotal,
        round((sum(本年累计) - sum(上年同期累计)) / 10000, 2) fzje,
        round(sum(今年中央级) / 10000, 2) as fjnqzzyj,
        round(sum(去年中央级) / 10000, 2) as fqnqzzyj,
        round(sum(今年省市级) / 10000, 2) as fjnqzssj,
        round(sum(去年省市级) / 10000, 2) as fqnqzssj,
        round(sum(今年地市级) / 10000, 2) as fjnqzdsj,
        round(sum(去年地市级) / 10000, 2) as fqnqzdsj,
        round(sum(今年区县级) / 10000, 2) as fjnqzqxj,
        round(sum(去年区县级) / 10000, 2) as fqnqzqxj,
        case
        when sum(上年同期累计) = 0 then
        0
        else
        round((sum(本年累计) - sum(上年同期累计)) / sum(上年同期累计), 4) * 100
        end yearOverYear
        from zhzs_srfx_jjdjlx a
        left join (select fzcdjlx,
        round(sum(合计), 2) 本年累计,
        0 as 上年同期累计,
        sum(中央级) 今年中央级,
        0 as 去年中央级,
        sum(省市级) 今年省市级,
        0 as 去年省市级,
        sum(地市级) 今年地市级,
        0 as 去年地市级,
        sum(区县级) 今年区县级,
        0 as 去年区县级
        from zhzs_srfx_ss_all_ztfx_zcdjlx
        where 1 = 1
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            行业大类 in (${fhydlStr})
        </if>
        <if test="fStartSkssqq != null and fStartSkssqq != ''">and
            str_to_date(substr(税款所属期起, 0, 10), '%Y-%m-%d') between
            str_to_date(#{fStartSkssqq}, '%Y-%m-%d') and
            str_to_date(#{fEndSkssqq}, '%Y-%m-%d')
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">and
            str_to_date(substr(frkrq, 0, 10), '%Y-%m-%d') between
            str_to_date(#{fStartRkrq}, '%Y-%m-%d') and
            str_to_date(#{fEndRkrq}, '%Y-%m-%d')
        </if>
        group by fzcdjlx
        union all
        select fzcdjlx,
        0 as 本年累计,
        round(sum(合计), 2) 去年税收合计,
        0 as 上年同期累计,
        sum(中央级) as 去年中央级,
        0 as 今年省市级,
        sum(省市级) as 去年省市级,
        0 as 今年地市级,
        sum(地市级) as 去年地市级,
        0 as 今年区县级,
        sum(区县级) as 去年区县级
        from zhzs_srfx_ss_all_ztfx_zcdjlx
        where 1 = 1
        <if test="fssqyList != null and fssqyList.size>0">
            and fskgk in
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null and fzsxmList.size>0">
            and fzsxm in
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhydlList != null and fhydlList.size>0">
            and 行业大类 in
            <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>

        <if test="fStartSkssqqLastYear != null and fStartSkssqqLastYear != ''">and
            str_to_date(substr(税款所属期起, 0, 10), '%Y-%m-%d') between
            str_to_date(#{fStartSkssqqLastYear}, '%Y-%m-%d') and
            str_to_date(#{fEndSkssqqLastYear}, '%Y-%m-%d')
        </if>
        <if test="fStartRkrqLastYear != null and fStartRkrqLastYear != ''">and
            str_to_date(substr(frkrq, 0, 10), '%Y-%m-%d') between
            str_to_date(#{fStartRkrqLastYear}, '%Y-%m-%d') and
            str_to_date(#{fEndRkrqLastYear}, '%Y-%m-%d')
        </if>
        group by fzcdjlx) b on a.fdjlx = b.fzcdjlx
        where fzcdjlx is not null
        group by rollup(fjjlx)
        order by decode(fjjlx,'合计',01,02),yeartotal desc
    </select>

    <!--收入分市州（外） incomeByCity-->
    <select id="incomeByCity" parameterType="com.hnbp.local.sszbfx.model.QueryBusinessStatistics"
            resultType="java.util.HashMap">
        select
        fdw,
        round(fdfybggyssrdywc,2)fdfybggyssrdywc
        ,fdfybggyssrdywcpm fdfybggyssrpmq
        ,round(fdfybggyssrljwc,2) fdfybggyssrljwc
        ,fdfybggyssrljwcpm fdfybggyssrpma
        ,round(fdfybggyssrzz,2) fdfybggyssrzz
        ,fdfybggyssrzzpm fdfybggyssrpm
        ,round(fdfsssrdywc,2) fdfsssrdywc
        ,fdfsssrdywcpm fdfsssrpmz
        ,round(fdfsssrljwc,2) fdfsssrljwc
        ,fdfsssrljwcpm fdfsssrpmb
        ,round(fdfsssrzz,2) fdfsssrzz
        ,fdfsssrzzpm fdfsssrpmss
        ,round(fdfsssrsszb,2) fdfsssrsszb
        ,fdfsssrpm fdfsssrpm
        ,round(ffssrdywc,2) ffssrdywc
        ,ffssrdywcpm ffssrpmx
        ,round(ffssrljwc,2) ffssrljwc
        ,ffssrljwcpm ffssrpme
        ,round(ffssrzz,2) ffssrzz
        ,ffssrzzpm ffssrpm
        from zhzs_bill_srfsz where f_sys_year=#{start_year} and f_sys_month=#{fstartmonth} and fdw is not null
        <if test="fssqyList != null and fssqyList.size>0">
            and fdw in
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        order by cast(f_sys_no as signed)
    </select>


    <select id="subCompaniesIndustryAnalysis" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="java.util.HashMap">
        select
        case grouping(纳税人名称) when 1 then '合计' else 纳税人名称 end fnsrmc ,round(nvl(sum(fsshjjn),0),2)
        fsshjjn,round(nvl(sum(fdfsr),0),2) fdfsr,round(nvl(sum(fsshjqn),0),2) fsshjqn,
        round(nvl(sum(fsshjjn),0)-nvl(sum(fsshjqn),0),2) fzj,
        round(case nvl(sum(fsshjqn),0) when 0 then 100 else
        (nvl(sum(fsshjjn),0)-nvl(sum(fsshjqn),0))/nvl(sum(fsshjqn),0) * 100 end,2) fbl
        , case grouping(纳税人名称) when 1 then null else max(fpx) end fpx
        from (
        select jn.*, row_number() over(
        order by
        case
        when #{ftype} = '增长'
        then ifnull(fsshjjn, 0) - ifnull(fsshjqn, 0)
        when #{ftype} = '下降'
        then ifnull(fsshjqn, 0) - ifnull(fsshjjn, 0)
        end
        desc) fpx from (
        select fnsrmc 纳税人名称
        , sum(case when YEAR(frkrq) = #{start_year} then fhj else 0 end)/10000 fsshjjn
        , sum(case when YEAR(frkrq) = #{ago_year} then fhj else 0 end)/10000 fsshjqn
        , sum(case when YEAR(frkrq) = #{start_year} then fdfsr else 0 end) / 10000 fdfsr
        from tb_dw_srfx_srfx_main
        where (
        frkrq between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth},'-','01'),'%Y-%m-%d')
        and last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth},'-','01'),'%Y-%m-%d'))
        or
        frkrq between str_to_date(CONCAT(#{ago_year},'-',#{fstartmonth},'-','01'),'%Y-%m-%d')
        and last_day(str_to_date(CONCAT(#{ago_year},'-',#{fendmonth},'-','01'),'%Y-%m-%d'))
        )
        <if test="fhymlStr != null and fhymlStr != ''">and
            fhyml in (${fhymlStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        <if test="fldts == 2">AND
            fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        group by fnsrmc
        ) jn
        ) tab1
        where
        case
        when #{ftype} = '增长'
        and ifnull(fsshjjn, 0) &gt;= ifnull(fsshjqn, 0)
        then 'ok'
        when #{ftype} = '下降'
        and ifnull(fsshjqn, 0) &gt;= ifnull(fsshjjn, 0)
        then 'ok'
        else 'err'
        end = 'ok'
        and fpx &lt;= ${fnsrpm}
        group by 纳税人名称 with rollup
        order by grouping(纳税人名称) desc, max(fpx)
    </select>


    <select id="districtAndCountyCompletionTable_v2" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.local.sszbfx.model.AreaCountyTax">
        select case when grouping(jd) = 1 then '合计' else jd end fjdxz,
        round(sum(今年合计), 2) jnss,
        round(sum(去年合计), 2) snss,
        round(sum(今年地方收入), 2) jnss_df,
        round(sum(去年地方收入), 2) snss_df,
        case
        when sum(去年合计) = 0 then 0
        else
        round((sum(今年合计) - sum(去年合计)) / sum(去年合计) * 100, 2)
        end as zjf,
        case
        when sum(去年地方收入) = 0 then 0
        else
        round((sum(今年地方收入) - sum(去年地方收入)) / sum(去年地方收入) * 100, 2)
        end as zjf_df,
        round(sum(今年合计) - sum(去年合计), 2) as zje,
        round(sum(今年地方收入) - sum(去年地方收入), 2) as zje_df
        from (
        select fskgk as jd,
        round(sum(case when YEAR(frkrq) = #{start_year} then fhj else 0 end) / 10000, 2) 今年合计,
        round(sum(case when YEAR(frkrq) = #{start_year} - 1 then fhj else 0 end) / 10000, 2) as 去年合计,
        round(sum(case when YEAR(frkrq) = #{start_year} then fdfsr else 0 end) / 10000, 2) 今年地方收入,
        round(sum(case when YEAR(frkrq) = #{start_year} - 1 then fdfsr else 0 end) / 10000, 2) as 去年地方收入
        from tb_dw_srfx_srfx_main a
        where
        (
        frkrq between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth}),'%Y-%m') and
        last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth}),'%Y-%m'))
        or
        frkrq between str_to_date(CONCAT(#{start_year} - 1,'-',#{fstartmonth}),'%Y-%m') and
        last_day(str_to_date(CONCAT(#{start_year} - 1,'-',#{fendmonth}),'%Y-%m'))
        )
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        group by fskgk
        ) as t1
        group by jd with rollup
        order by case when fjdxz='合计' then 1 else 2 end, jnss desc
    </select>


    <select id="revenueByIndustryMxTable" parameterType="java.util.Map" resultType="java.util.Map">
        select case when grouping(纳税人名称) = 1 then '合计' else 纳税人名称 end 纳税人名称,
        case when grouping(纳税人名称) = 1 then '' else max(行业门类) end 行业门类,
        case when grouping(纳税人名称) = 1 then '' else max(收款国库) end 收款国库,
        case when grouping(纳税人名称) = 1 then null else max(fpx) end fpx,
        ROUND(SUM(fhj_jn), 2) fhj_jn,
        ROUND(SUM(fhj_qn), 2) fhj_qn,
        <!--增减额 -->
        ROUND(ifnull(SUM(fhj_jn), 0) - ifnull(SUM(fhj_qn), 0), 2) fzje_hj,
        <!-- 增减比 -->
        CASE ifnull(SUM(fhj_qn), 0)
        WHEN 0 THEN 0
        ELSE ROUND((ifnull(SUM(fhj_jn), 0) - ifnull(SUM(fhj_qn), 0)) / ifnull(SUM(fhj_qn), 0) * 100, 2)
        END fzjb_hj
        from (
        SELECT t2.*,
        row_number() over(order by
        case
        when #{fcollation} = 'DESC'
        then ifnull(fhj_jn, 0) - ifnull(fhj_qn, 0)
        when #{fcollation} = 'ASC'
        then ifnull(fhj_qn, 0) - ifnull(fhj_jn, 0)
        end
        desc) fpx
        FROM (
        SELECT
        ss.fnsrmc 纳税人名称,
        tab1.fhyml 行业门类,
        tab1.fskgk 收款国库,
        ROUND(SUM(


        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4) = YEAR(fskssqq)
            then
            case when #{ftype} = 'qkj' then fhj else fqxj end
            else 0 end
        </if>

        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4) = YEAR(frkrq)
            then
            case when #{ftype} = 'qkj' then fhj else fqxj end
            else 0 end
        </if>

        )/10000, 2) fhj_jn,
        <!-- 去年 -->
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4) -1 = YEAR(fskssqq)
            then
            case when #{ftype} = 'qkj' then fhj else fqxj end
            else 0 end
        </if>

        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4) -1 = YEAR(frkrq)
            then
            case when #{ftype} = 'qkj' then fhj else fqxj end
            else 0 end
        </if>

        )/10000, 2) fhj_qn
        FROM tb_dw_srfx_srfx_main ss
        left join TB_DW_ZHZS_NSR_HYML_SKGK_GEN tab1
        on ss.fnsrmc = tab1.fnsrmc
        WHERE 1=1
        <if test="fStartSkssqq != null and fStartSkssqq != ''">and
            (
            ss.fskssqq
            between #{fStartSkssqq} and #{fEndSkssqq}
            OR ss.fskssqq between #{fStartSkssqqLastYear} and #{fEndSkssqqLastYear}
            )
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">and
            (
            ss.frkrq between #{fStartRkrq} and #{fEndRkrq}
            OR ss.frkrq between #{fStartRkrqLastYear} and #{fEndRkrqLastYear}
            )
        </if>
        <if test="fssqyList != null">AND
            ss.fskgk IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fzsxmList != null">AND
            ss.fzsxm IN
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhydlList != null">AND
            ss.fhydl IN
            <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhyml != &quot;合计&quot;">AND
            ss.fhyml = #{fhyml}
        </if>
        AND (ss.fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税'))
        GROUP BY ss.fnsrmc, tab1.fhyml, tab1.fskgk
        ) t2
        ) t3
        where 1 = 1
        <if test="fpmLimit != null and fpmLimit != ''">AND
            #{fpmLimit} * 1 &gt;= fpx * 1
        </if>
        group by 纳税人名称 with rollup
        order by grouping(纳税人名称) desc, fpx * 1
    </select>

    <select id="revenueByAreaMxTable" parameterType="java.util.Map" resultType="java.util.Map">
        select case when grouping(纳税人名称) = 1 then '合计' else 纳税人名称 end 纳税人名称,
        case when grouping(纳税人名称) = 1 then '' else max(行业门类) end 行业门类,
        case when grouping(纳税人名称) = 1 then '' else max(收款国库) end 收款国库,
        case when grouping(纳税人名称) = 1 then null else max(fpx) end fpx,
        ROUND(SUM(fhj_jn), 2) fhj_jn,
        ROUND(SUM(fhj_qn), 2) fhj_qn,
        <!--增减额 -->
        ROUND(ifnull(SUM(fhj_jn), 0) - ifnull(SUM(fhj_qn), 0), 2) fzje_hj,
        <!-- 增减比 -->
        CASE ifnull(SUM(fhj_qn), 0)
        WHEN 0 THEN 0
        ELSE ROUND((ifnull(SUM(fhj_jn), 0) - ifnull(SUM(fhj_qn), 0)) / ifnull(SUM(fhj_qn), 0) * 100, 2)
        END fzjb_hj
        from (
        SELECT t2.*,
        row_number() over(order by
        case
        when #{fcollation} = 'DESC'
        then ifnull(fhj_jn, 0) - ifnull(fhj_qn, 0)
        when #{fcollation} = 'ASC'
        then ifnull(fhj_qn, 0) - ifnull(fhj_jn, 0)
        end
        desc) fpx
        FROM (
        SELECT
        ss.fnsrmc 纳税人名称,
        tab1.fhyml,
        tab1.fskgk,
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4) = YEAR(fskssqq)
            then
            case when #{ftype} = 'qkj' then fhj else fkycl end
            else 0 end
        </if>

        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4) = YEAR(frkrq)
            then
            case when #{ftype} = 'qkj' then fhj else fkycl end
            else 0 end
        </if>

        )/10000, 2) fhj_jn,
        <!-- 去年 -->
        ROUND(SUM(

        <if test="fStartSkssqq != null and fStartSkssqq != ''">
            case when
            substr(ifnull(#{fStartSkssqq}, ''), 1, 4)-1 = YEAR(fskssqq)
            then
            case when #{ftype} = 'qkj' then fhj else fkycl end
            else 0 end
        </if>

        <if test="fStartRkrq != null and fStartRkrq != ''">
            case when
            substr(ifnull(#{fStartRkrq}, ''), 1, 4)-1 = YEAR(frkrq)
            then
            case when #{ftype} = 'qkj' then fhj else fkycl end
            else 0 end
        </if>

        )/10000, 2) fhj_qn
        FROM tb_dw_srfx_srfx_main ss
        left join TB_DW_ZHZS_NSR_HYML_SKGK_GEN tab1
        on ss.fnsrmc = tab1.fnsrmc
        WHERE 1=1
        <if test="fStartSkssqq != null and fStartSkssqq != ''">and
            (
            ss.fskssqq
            between #{fStartSkssqq} and #{fEndSkssqq}
            OR ss.fskssqq between #{fStartSkssqqLastYear} and #{fEndSkssqqLastYear}
            )
        </if>
        <if test="fStartRkrq != null and fStartRkrq != ''">and
            (
            ss.frkrq between #{fStartRkrq} and #{fEndRkrq}
            OR ss.frkrq between #{fStartRkrqLastYear} and #{fEndRkrqLastYear}
            )
        </if>
        <if test="fzsxmList != null">AND
            ss.fzsxm IN
            <foreach collection="fzsxmList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fhydlList != null">AND
            ss.fhydl IN
            <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fssqy != &quot;合计&quot;">AND
            ss.fskgk = #{fssqy}
        </if>
        AND (ss.fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税'))
        GROUP BY ss.fnsrmc, tab1.fhyml, tab1.fskgk
        ) t2
        ) t3
        where 1 = 1
        <if test="fpmLimit != null and fpmLimit != ''">AND
            #{fpmLimit} * 1 &gt;= fpx * 1
        </if>
        group by 纳税人名称 with rollup
        order by grouping(纳税人名称) desc, fpx * 1
    </select>


    <select id="Sszbfxztfx_table" parameterType="java.util.Map" resultType="java.util.Map">
        select fyear fnf
        , round(fshj / 10000, 2) fqssze
        , round(fce_ss / 10000, 2) fqtbzj
        , round(ftb_ss, 2) fqzs
        , round(fdfsr / 10000, 2) fdssze
        , round(fce_dfsr / 10000, 2) fdtbzje
        , round(ftb_fdfsr, 2) fdzs
        from (select fyear
        , ifnull(fshj, 0) fshj
        , ifnull(fshj, 0) - ifnull(fshj_qn, 0) fce_ss
        , ifnull(fdfsr, 0) fdfsr
        , ifnull(fdfsr, 0) - ifnull(fdfsr_qn, 0) fce_dfsr
        , case
        when fshj_qn is null or fshj_qn = '' or fshj_qn = '0' then '100'
        else (fshj - fshj_qn) / fshj_qn end ftb_ss
        , case
        when fdfsr_qn is null or fdfsr_qn = '' or fdfsr_qn = '0' then '100'
        else (fdfsr - fdfsr_qn) / fdfsr_qn end ftb_fdfsr
        from (select year(frkrq) fyear, sum(fshj) fshj, sum(fdfsr) fdfsr
        from tb_dw_srfx_srfx_main
        where date_format(frkrq, '%Y-%m') between
        date_format(str_to_date(concat(#{fyear},'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and date_format(str_to_date(concat(#{fyear}, '-', #{fmonth_e}),'%Y-%m'),'%Y-%m')
        <if test="fssqyStr != null and fssqyStr != ''">
            and fjdxz in (${fssqyStr})
        </if>
        group by year(frkrq)) t1
        left join
        (select sum(fshj) fshj_qn, sum(fdfsr) fdfsr_qn
        from tb_dw_srfx_srfx_main
        where date_format(frkrq, '%Y-%m') between date_format(str_to_date(concat(#{fyear} -
        1,'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and date_format(str_to_date(concat(#{fyear} - 1, '-', #{fmonth_e}),'%Y-%m'),'%Y-%m')
        <if test="fssqyStr != null and fssqyStr != ''">
            and fjdxz in (${fssqyStr})
        </if>
        ) t2
        on 1 = 1

        union all

        select fyear
        , ifnull(fshj, 0) fshj
        , ifnull(fshj, 0) - ifnull(fshj_qn, 0) fce_ss
        , ifnull(fdfsr, 0) fdfsr
        , ifnull(fdfsr, 0) - ifnull(fdfsr_qn, 0) fce_dfsr
        , case
        when fshj_qn is null or fshj_qn = '' or fshj_qn = '0' then '100'
        else (fshj - fshj_qn) / fshj_qn end ftb_ss
        , case
        when fdfsr_qn is null or fdfsr_qn = '' or fdfsr_qn = '0' then '100'
        else (fdfsr - fdfsr_qn) / fdfsr_qn end ftb_fdfsr
        from (select year(frkrq) fyear, sum(fshj) fshj, sum(fdfsr) fdfsr
        from tb_dw_srfx_srfx_main
        where date_format(frkrq, '%Y-%m') between
        date_format(str_to_date(concat(#{fyear}-1,'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and date_format(str_to_date(concat(#{fyear}-1, '-', #{fmonth_e}),'%Y-%m'),'%Y-%m')
        <if test="fssqyStr != null and fssqyStr != ''">
            and fjdxz in (${fssqyStr})
        </if>
        group by year(frkrq)) t1
        left join
        (select sum(fshj) fshj_qn, sum(fdfsr) fdfsr_qn
        from tb_dw_srfx_srfx_main
        where date_format(frkrq, '%Y-%m') between date_format(str_to_date(concat(#{fyear} -
        2,'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and date_format(str_to_date(concat(#{fyear} - 2, '-', #{fmonth_e}),'%Y-%m'),'%Y-%m')
        <if test="fssqyStr != null and fssqyStr != ''">
            and fjdxz in (${fssqyStr})
        </if>
        ) t2
        on 1 = 1

        union all

        select fyear
        , ifnull(fshj, 0) fshj
        , ifnull(fshj, 0) - ifnull(fshj_qn, 0) fce_ss
        , ifnull(fdfsr, 0) fdfsr
        , ifnull(fdfsr, 0) - ifnull(fdfsr_qn, 0) fce_dfsr
        , case
        when fshj_qn is null or fshj_qn = '' or fshj_qn = '0' then '100'
        else (fshj - fshj_qn) / fshj_qn end ftb_ss
        , case
        when fdfsr_qn is null or fdfsr_qn = '' or fdfsr_qn = '0' then '100'
        else (fdfsr - fdfsr_qn) / fdfsr_qn end ftb_fdfsr
        from (select year(frkrq) fyear, sum(fshj) fshj, sum(fdfsr) fdfsr
        from tb_dw_srfx_srfx_main
        where date_format(frkrq, '%Y-%m') between date_format(str_to_date(concat(#{fyear}-2,'-',#{fmonth_s})
        ,'%Y-%m'),'%Y-%m')
        and date_format(str_to_date(concat(#{fyear}-2, '-', #{fmonth_e}),'%Y-%m'),'%Y-%m')
        <if test="fssqyStr != null and fssqyStr != ''">
            and fjdxz in (${fssqyStr})
        </if>
        group by year(frkrq)) t1
        left join
        (select sum(fshj) fshj_qn, sum(fdfsr) fdfsr_qn
        from tb_dw_srfx_srfx_main
        where date_format(frkrq, '%Y-%m') between date_format(str_to_date(concat(#{fyear} - 3,'-',#{fmonth_s})
        ,'%Y-%m'),'%Y-%m')
        and date_format(str_to_date(concat(#{fyear} - 3, '-', #{fmonth_e}),'%Y-%m'),'%Y-%m')
        <if test="fssqyStr != null and fssqyStr != ''">
            and fjdxz in (${fssqyStr})
        </if>
        ) t2
        on 1 = 1) a

    </select>

    <select id="Sszbfxztfx_Chart" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData">
        select round(fshj, 2) yAxis, fyear xAxis, fxm legend, type
        from (select sum(fshj) / 10000 fshj, year(frkrq) fyear, '全口径税收' fxm, 'bar' type
        from tb_dw_srfx_srfx_main
        where (
        date_format(frkrq,'%Y-%m') between date_format(str_to_date( concat(#{fyear},'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and
        date_format(str_to_date( concat(#{fyear},'-',#{fmonth_e}),'%Y-%m'),'%Y-%m')
        or
        date_format(frkrq,'%Y-%m') between date_format(str_to_date( concat(#{fyear}-1,'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and
        date_format(str_to_date( concat(#{fyear}-1,'-',#{fmonth_e}),'%Y-%m'),'%Y-%m')
        or
        date_format(frkrq,'%Y-%m') between date_format(str_to_date( concat(#{fyear}-2,'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and
        date_format(str_to_date( concat(#{fyear}-2,'-',#{fmonth_e}),'%Y-%m'),'%Y-%m')
        )
        <if test="fssqyStr != null and fssqyStr != ''">
            and fjdxz in (${fssqyStr})
        </if>
        group by year(frkrq)
        union all
        select sum(fdfsr)/10000 fdfsr, year(frkrq) fyear, '地方收入' fxm, 'bar' type
        from tb_dw_srfx_srfx_main
        where (
        date_format(frkrq,'%Y-%m') between date_format(str_to_date( concat(#{fyear},'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and
        date_format(str_to_date( concat(#{fyear},'-',#{fmonth_e}),'%Y-%m'),'%Y-%m')
        or
        date_format(frkrq,'%Y-%m') between date_format(str_to_date( concat(#{fyear}-1,'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and
        date_format(str_to_date( concat(#{fyear}-1,'-',#{fmonth_e}),'%Y-%m'),'%Y-%m')
        or
        date_format(frkrq,'%Y-%m') between date_format(str_to_date( concat(#{fyear}-2,'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and
        date_format(str_to_date( concat(#{fyear}-2,'-',#{fmonth_e}),'%Y-%m'),'%Y-%m')
        )
        <if test="fssqyStr != null and fssqyStr != ''">
            and fjdxz in (${fssqyStr})
        </if>
        group by year(frkrq)
        union all
        select case when fshj_qn is null or fshj_qn = '' or fshj_qn = '0' then '100' else
        round((fshj_qn-fshj_jn)/fshj_qn, 2) end fzs_jn
        , #{fyear} fyear, '全口径税收增速' fxm, 'line' type
        from (
        select sum(case when year(frkrq)= #{fyear} then fshj else 0 end) fshj_jn
        , sum(case when year(frkrq)= #{fyear}-1 then fshj else 0 end) fshj_qn
        from tb_dw_srfx_srfx_main where (
        date_format(frkrq,'%Y-%m') between date_format(str_to_date( concat(#{fyear},'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and
        date_format(str_to_date( concat(#{fyear},'-',#{fmonth_e}),'%Y-%m'),'%Y-%m')
        or
        date_format(frkrq,'%Y-%m') between date_format(str_to_date( concat(#{fyear}-1,'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and
        date_format(str_to_date( concat(#{fyear}-1,'-',#{fmonth_e}),'%Y-%m'),'%Y-%m')
        )
        <if test="fssqyStr != null and fssqyStr != ''">
            and fjdxz in (${fssqyStr})
        </if>
        ) t1
        union all
        select case when fshj_qn is null or fshj_qn = '' or fshj_qn = '0' then '100' else
        round((fshj_qn-fshj_jn)/fshj_qn, 2) end fzs_jn
        , #{fyear}-1 fyear, '全口径税收增速' fxm, 'line' type
        from (
        select sum(case when year(frkrq)= #{fyear}-1 then fshj else 0 end) fshj_jn
        , sum(case when year(frkrq)= #{fyear}-2 then fshj else 0 end) fshj_qn
        from tb_dw_srfx_srfx_main where (
        date_format(frkrq,'%Y-%m') between date_format(str_to_date( concat(#{fyear}-1,'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and
        date_format(str_to_date( concat(#{fyear}-1,'-',#{fmonth_e}),'%Y-%m'),'%Y-%m')
        or
        date_format(frkrq,'%Y-%m') between date_format(str_to_date( concat(#{fyear}-2,'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and
        date_format(str_to_date( concat(#{fyear}-2,'-',#{fmonth_e}),'%Y-%m'),'%Y-%m')
        )
        <if test="fssqyStr != null and fssqyStr != ''">
            and fjdxz in (${fssqyStr})
        </if>
        ) t1
        union all
        select case when fshj_qn is null or fshj_qn = '' or fshj_qn = '0' then '100' else
        round((fshj_qn-fshj_jn)/fshj_qn, 2) end fzs_jn
        , #{fyear}-2 fyear, '全口径税收增速' fxm, 'line' type
        from (
        select sum(case when year(frkrq)= #{fyear}-2 then fshj else 0 end) fshj_jn
        , sum(case when year(frkrq)= #{fyear}-3 then fshj else 0 end) fshj_qn
        from tb_dw_srfx_srfx_main where (
        date_format(frkrq,'%Y-%m') between date_format(str_to_date( concat(#{fyear}-2,'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and
        date_format(str_to_date( concat(#{fyear}-2,'-',#{fmonth_e}),'%Y-%m'),'%Y-%m')
        or
        date_format(frkrq,'%Y-%m') between date_format(str_to_date( concat(#{fyear}-3,'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and
        date_format(str_to_date( concat(#{fyear}-3,'-',#{fmonth_e}),'%Y-%m'),'%Y-%m')
        )
        <if test="fssqyStr != null and fssqyStr != ''">
            and fjdxz in (${fssqyStr})
        </if>
        ) t1
        union all
        select case when fshj_qn is null or fshj_qn = '' or fshj_qn = '0' then '100' else
        round((fshj_qn-fshj_jn)/fshj_qn, 2) end fzs_jn
        , #{fyear} fyear, '地方收入增速' fxm, 'line' type
        from (
        select sum(case when year(frkrq)= #{fyear} then fdfsr else 0 end) fshj_jn
        , sum(case when year(frkrq)= #{fyear}-1 then fdfsr else 0 end) fshj_qn
        from tb_dw_srfx_srfx_main where (
        date_format(frkrq,'%Y-%m') between date_format(str_to_date( concat(#{fyear},'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and
        date_format(str_to_date( concat(#{fyear},'-',#{fmonth_e}),'%Y-%m'),'%Y-%m')
        or
        date_format(frkrq,'%Y-%m') between date_format(str_to_date( concat(#{fyear}-1,'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and
        date_format(str_to_date( concat(#{fyear}-1,'-',#{fmonth_e}),'%Y-%m'),'%Y-%m')
        )
        <if test="fssqyStr != null and fssqyStr != ''">
            and fjdxz in (${fssqyStr})
        </if>
        ) t1
        union all
        select case when fshj_qn is null or fshj_qn = '' or fshj_qn = '0' then '100' else
        round((fshj_qn-fshj_jn)/fshj_qn, 2) end fzs_jn
        , #{fyear}-1 fyear, '地方收入增速' fxm, 'line' type
        from (
        select sum(case when year(frkrq)= #{fyear}-1 then fdfsr else 0 end) fshj_jn
        , sum(case when year(frkrq)= #{fyear}-2 then fdfsr else 0 end) fshj_qn
        from tb_dw_srfx_srfx_main where (
        date_format(frkrq,'%Y-%m') between date_format(str_to_date( concat(#{fyear}-1,'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and
        date_format(str_to_date( concat(#{fyear}-1,'-',#{fmonth_e}),'%Y-%m'),'%Y-%m')
        or
        date_format(frkrq,'%Y-%m') between date_format(str_to_date( concat(#{fyear}-2,'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and
        date_format(str_to_date( concat(#{fyear}-2,'-',#{fmonth_e}),'%Y-%m'),'%Y-%m')
        )
        <if test="fssqyStr != null and fssqyStr != ''">
            and fjdxz in (${fssqyStr})
        </if>
        ) t1
        union all
        select case when fshj_qn is null or fshj_qn = '' or fshj_qn = '0' then '100' else
        round((fshj_qn-fshj_jn)/fshj_qn, 2) end fzs_jn
        , #{fyear}-2 fyear, '地方收入增速' fxm, 'line' type
        from (
        select sum(case when year(frkrq)= #{fyear}-2 then fdfsr else 0 end) fshj_jn
        , sum(case when year(frkrq)= #{fyear}-3 then fdfsr else 0 end) fshj_qn
        from tb_dw_srfx_srfx_main where (
        date_format(frkrq,'%Y-%m') between date_format(str_to_date( concat(#{fyear}-2,'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and
        date_format(str_to_date( concat(#{fyear}-2,'-',#{fmonth_e}),'%Y-%m'),'%Y-%m')
        or
        date_format(frkrq,'%Y-%m') between date_format(str_to_date( concat(#{fyear}-3,'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and
        date_format(str_to_date( concat(#{fyear}-3,'-',#{fmonth_e}),'%Y-%m'),'%Y-%m')
        )
        <if test="fssqyStr != null and fssqyStr != ''">
            and fjdxz in (${fssqyStr})
        </if>
        ) t1) t2
    </select>

    <select id="Sszbfxztfxfdq_Chart" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData">
        select round(sum(fshj)/10000,2) yAxis,fjdxz xAxis,'全口径税收' legend ,'line' type from tb_dw_srfx_srfx_main
        where date_format(frkrq,'%Y-%m') between
        date_format(str_to_date(concat(#{fyear},'-',#{fmonth_s}),'%Y-%m'),'%Y-%m')
        and date_format(str_to_date(concat(#{fyear},'-',#{fmonth_e}),'%Y-%m'),'%Y-%m')
        <if test="fssqyStr != null and fssqyStr != ''">
            and fjdxz in (${fssqyStr})
        </if>
        group by fjdxz
    </select>

    <select id="Sszbfxztfxfsz_Chart" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select round(fshj, 2) `value`, fzsxm `name`
        from (select sum(fshj) / 10000 fshj, fzsxm
        from tb_dw_srfx_srfx_main
        where date_format(frkrq, '%Y-%m') between
        date_format(str_to_date(concat(#{fyear},'-',#{fmonth_s}),'%Y-%m'),'%Y-%m') and
        date_format(str_to_date(concat(#{fyear}, '-', #{fmonth_e}),'%Y-%m'),'%Y-%m')
        <if test="fssqyStr != null and fssqyStr != ''">
            and fjdxz in (${fssqyStr})
        </if>
        group by fzsxm) t1
    </select>

    <select id="Sszbfxztfxfhy_Chart" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData">
        select round(fshj, 2) yAxis, fhyml xAxis, '税收收入' legend, 'bar' type
        from (select sum(fshj) / 10000 fshj, fhyml
        from tb_dw_srfx_srfx_main
        where date_format(frkrq, '%Y-%m') between
        date_format(str_to_date(concat(#{fyear},'-',#{fmonth_s}),'%Y-%m'),'%Y-%m') and
        date_format(str_to_date(concat(#{fyear}, '-', #{fmonth_e}),'%Y-%m'),'%Y-%m')
        <if test="fssqyStr != null and fssqyStr != ''">
            and fjdxz in (${fssqyStr})
        </if>
        group by fhyml) t1
    </select>

    <select id="Sszbfxxzsy_table" parameterType="java.util.Map" resultType="com.hnbp.local.sszbfx.model.NewTaxSource5Year">
        SELECT sum(CASE WHEN fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc},'-',#{fmonthzc_e}),'%Y-%m')) THEN 1 ELSE 0 END ) thisYearTotalHouseholds
        , sum(CASE WHEN fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc}-1,'-',#{fmonthzc_e}),'%Y-%m')) THEN 1 ELSE 0 END ) lastYearTotalHouseholds
        , sum(CASE WHEN fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc}-2,'-',#{fmonthzc_e}),'%Y-%m')) THEN 1 ELSE 0 END ) twoYearsAgoTotalHouseholds
        , sum(CASE WHEN fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc}-3,'-',#{fmonthzc_e}),'%Y-%m')) THEN 1 ELSE 0 END ) threeYearsAgoTotalHouseholds
        , sum(CASE WHEN fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc}-4,'-',#{fmonthzc_e}),'%Y-%m')) THEN 1 ELSE 0 END ) fourYearsAgoTotalHouseholds
        , sum(CASE WHEN fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc}-5,'-',#{fmonthzc_e}),'%Y-%m')) THEN 1 ELSE 0 END ) fiveYearsAgoTotalHouseholds

        , sum(CASE WHEN fxzrq = '' THEN 0 WHEN fswdjrq >= str_to_date(concat(#{fyearzc},'-',#{fmonthzc}),'%Y-%m')
        AND fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc},'-',#{fmonthzc_e}),'%Y-%m')) THEN 1 ELSE 0 END ) thisYearNewTaxSource
        , sum(CASE WHEN fxzrq = '' THEN 0 WHEN fswdjrq >= str_to_date(concat(#{fyearzc}-1,'-',#{fmonthzc}),'%Y-%m')
        AND fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc}-1,'-',#{fmonthzc_e}),'%Y-%m')) THEN 1 ELSE 0 END ) lastYearNewTaxSource
        , sum(CASE WHEN fxzrq = '' THEN 0 WHEN fswdjrq >= str_to_date(concat(#{fyearzc}-2,'-',#{fmonthzc}),'%Y-%m')
        AND fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc}-2,'-',#{fmonthzc_e}),'%Y-%m')) THEN 1 ELSE 0 END ) twoYearsAgoNewTaxSource
        , sum(CASE WHEN fxzrq = '' THEN 0 WHEN fswdjrq >= str_to_date(concat(#{fyearzc}-3,'-',#{fmonthzc}),'%Y-%m')
        AND fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc}-3,'-',#{fmonthzc_e}),'%Y-%m')) THEN 1 ELSE 0 END ) threeYearsAgoNewTaxSource
        , sum(CASE WHEN fxzrq = '' THEN 0 WHEN fswdjrq >= str_to_date(concat(#{fyearzc}-4,'-',#{fmonthzc}),'%Y-%m')
        AND fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc}-4,'-',#{fmonthzc_e}),'%Y-%m')) THEN 1 ELSE 0 END ) fourYearsAgoNewTaxSource
        , sum(CASE WHEN fxzrq = '' THEN 0 WHEN fswdjrq >= str_to_date(concat(#{fyearzc}-5,'-',#{fmonthzc}),'%Y-%m')
        AND fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc}-5,'-',#{fmonthzc_e}),'%Y-%m')) THEN 1 ELSE 0 END ) fiveYearsAgoNewTaxSource


        , round(sum(CASE WHEN fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc},'-',#{fmonthzc_e}),'%Y-%m')) THEN fs1 ELSE 0 END )/10000,2) thisYearTotalTax
        , round(sum(CASE WHEN fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc}-1,'-',#{fmonthzc_e}),'%Y-%m')) THEN fs2 ELSE 0 END )/10000,2) lastYearTotalTax
        , round(sum(CASE WHEN fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc}-2,'-',#{fmonthzc_e}),'%Y-%m')) THEN fs3 ELSE 0 END )/10000,2) twoYearsAgoTotalTax
        , round(sum(CASE WHEN fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc}-3,'-',#{fmonthzc_e}),'%Y-%m')) THEN fs4 ELSE 0 END )/10000,2) threeYearsAgoTotalTax
        , round(sum(CASE WHEN fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc}-4,'-',#{fmonthzc_e}),'%Y-%m')) THEN fs5 ELSE 0 END )/10000,2) fourYearsAgoTotalTax
        , round(sum(CASE WHEN fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc}-5,'-',#{fmonthzc_e}),'%Y-%m')) THEN fs5 ELSE 0 END )/10000,2) fiveYearsAgoTotalTax

        , round(sum(CASE WHEN fxzrq = '' THEN 0 WHEN fswdjrq >= str_to_date(concat(#{fyearzc},'-',#{fmonthzc}),'%Y-%m')
        AND fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc},'-',#{fmonthzc_e}),'%Y-%m'))
        THEN fs1 ELSE 0 END )/10000,2) thisYearNewTaxSourceTax
        , round(sum(CASE WHEN fxzrq = '' THEN 0 WHEN fswdjrq >= str_to_date(concat(#{fyearzc}-1,'-',#{fmonthzc}),'%Y-%m')
        AND fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc}-1,'-',#{fmonthzc_e}),'%Y-%m'))
        THEN fs2 ELSE 0 END )/10000,2) lastYearNewTaxSourceTax
        , round(sum(CASE WHEN fxzrq = '' THEN 0 WHEN fswdjrq >= str_to_date(concat(#{fyearzc}-2,'-',#{fmonthzc}),'%Y-%m')
        AND fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc}-2,'-',#{fmonthzc_e}),'%Y-%m'))
        THEN fs3 ELSE 0 END )/10000,2) twoYearsAgoNewTaxSourceTax
        , round(sum(CASE WHEN fxzrq = '' THEN 0 WHEN fswdjrq >= str_to_date(concat(#{fyearzc}-3,'-',#{fmonthzc}),'%Y-%m')
        AND fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc}-3,'-',#{fmonthzc_e}),'%Y-%m'))
        THEN fs4 ELSE 0 END )/10000,2) threeYearsAgoNewTaxSourceTax
        , round(sum(CASE WHEN fxzrq = '' THEN 0 WHEN fswdjrq >= str_to_date(concat(#{fyearzc}-4,'-',#{fmonthzc}),'%Y-%m')
        AND fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc}-4,'-',#{fmonthzc_e}),'%Y-%m'))
        THEN fs5 ELSE 0 END )/10000,2) fourYearsAgoNewTaxSourceTax
        , round(sum(CASE WHEN fxzrq = '' THEN 0 WHEN fswdjrq >= str_to_date(concat(#{fyearzc}-5,'-',#{fmonthzc}),'%Y-%m')
        AND fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc}-5,'-',#{fmonthzc_e}),'%Y-%m'))
        THEN fs6 ELSE 0 END )/10000,2) fiveYearsAgoNewTaxSourceTax
        FROM (SELECT FSHXYDM,MIN(FXZRQ) FXZRQ,
        MIN(FSWDJRQ) FSWDJRQ
        FROM TB_DM_ZHZS_XZSYMD_GEN
        WHERE fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc},'-',#{fmonthzc_e}),'%Y-%m'))
        <if test="fcode_ss == 1">
            AND FDJZCLX NOT IN ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        GROUP BY FSHXYDM) t1
        LEFT JOIN (
        SELECT fnsrsbh
        , sum(CASE WHEN FNF = #{fyear} THEN FSHJ ELSE 0 END ) fs1
        , sum(CASE WHEN FNF = #{fyear} -1 THEN FSHJ ELSE 0 END ) fs2
        , sum(CASE WHEN FNF = #{fyear} -2 THEN FSHJ ELSE 0 END ) fs3
        , sum(CASE WHEN FNF = #{fyear} -3 THEN FSHJ ELSE 0 END ) fs4
        , sum(CASE WHEN FNF = #{fyear} -4 THEN FSHJ ELSE 0 END ) fs5
        , sum(CASE WHEN FNF = #{fyear} -5 THEN FSHJ ELSE 0 END ) fs6
        FROM TB_DM_ZHZS_SYLXLDT_GEN
        WHERE fnsrsbh IN (
            SELECT distinct fshxydm from TB_DM_ZHZS_XZSYMD_GEN
            WHERE fswdjrq &lt;= last_day(str_to_date(concat(#{fyearzc},'-',#{fmonthzc_e}),'%Y-%m'))
            <if test="fcode_ss == 1">
                AND FDJZCLX NOT IN ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
                ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
                ,'民办非企业单位（个体）','农村集体经济组织')
            </if>
            GROUP BY FSHXYDM
        )
        AND FNF >= #{fyear} -5 AND FNF &lt;= #{fyear}
        AND FYF >= #{fmonth} AND FYF &lt;= #{fmonth_e}
        <if test="fldts == 0">
            AND fldt NOT LIKE '%留抵退%'
        </if>
        <if test="fcode_ss == 1">
            AND fsslx = '企业'
        </if>
        GROUP BY fnsrsbh
        ) t2 ON t1.fshxydm = t2.fnsrsbh
    </select>

    <select id="Sszbfxycsy_table" parameterType="java.util.Map" resultType="java.util.Map">
        select * from (
        select a.fnsrmc,a.fnsrsbh,round(fshj_jn/10000,2) fshj_jn,round(fshj_qn/10000,2)
        fshj_qn,round((fshj_jn-fshj_qn)/10000,2) fzje,
        round((fshj_jn-fshj_qn)/case when fshj_qn = 0 then null else fshj_qn end *100,2) ftb from
        (select
        fnsrmc,fnsrsbh,frkrq,sum(fshj) fshj_jn
        from tb_dw_srfx_srfx_main
        where frkrq between str_to_date(concat(#{fStartRkrq},'-01'),'%Y-%m-%d')
        and last_day(str_to_date(#{fEndRkrq},'%Y-%m'))
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        group by fnsrmc,fnf)a
        left join
        (
        select fnsrmc,fnsrsbh,frkrq,sum(fshj) fshj_qn from
        tb_dw_srfx_srfx_main
        where frkrq between str_to_date(concat(#{fStartRkrqLastYear},'-01'),'%Y-%m-%d')
        and last_day(str_to_date(#{fEndRkrqLastYear},'%Y-%m'))
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        group by fnsrmc,fnf
        )b on b.fnsrmc = a.fnsrmc
        )tmp
        where 1=1
        <if test="fsssr != null and fsssr != ''">AND
            fshj_jn &gt;= ${fsssr}
        </if>
        <if test="fssxhl != null and fssxhl != ''">AND
            ftb &lt;= -${fssxhl}
        </if>
        order by fshj_jn desc
    </select>

    <select id="Sszbfxztfxsysr_Chart" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData">
        select fsj xAxis, round(fshj, 2) yAxis, fxm legend, type
        from (select t1.fsj, sum(fshj) fshj, '新增税源收入' fxm, 'bar' type
        from (select distinct fnsrmc, #{fyearzc} fsj
        from tb_dw_srfx_swdjxx_main
        where date_format(fdjrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyearzc}, '-', #{fmonthzc_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc}, '-', #{fmonthzc_e}), '%Y-%m'), '%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        ) t1
        left join
        (select fnsrmc, sum(fshj) / 10000 fshj, #{fyear} fsj
        from tb_dw_srfx_srfx_main
        where date_format(frkrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyear}, '-', #{fmonth_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyear}, '-', #{fmonth_e}), '%Y-%m'), '%Y-%m')
        <if test="fldts == 2">AND
            a.fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND a.fyskm != '免抵调增增值税'</if>
        group by fnsrmc) t2 on t1.fnsrmc = t2.fnsrmc and t1.fsj = t2.fsj
        group by t1.fsj
        union all
        select t1.fsj, sum(fshj) fshj, '新增税源收入' fxm, 'bar' type
        from (select distinct fnsrmc, #{fyearzc} - 1 fsj
        from tb_dw_srfx_swdjxx_main
        where date_format(fdjrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyearzc} - 1, '-', #{fmonthzc_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc} - 1, '-', #{fmonthzc_e}), '%Y-%m'), '%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        ) t1
        left join
        (select fnsrmc, sum(fshj) / 10000 fshj, #{fyear} - 1 fsj
        from tb_dw_srfx_srfx_main
        where date_format(frkrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyear} - 1, '-', #{fmonth_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyear} - 1, '-', #{fmonth_e}), '%Y-%m'), '%Y-%m')
        <if test="fldts == 2">AND
            a.fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND a.fyskm != '免抵调增增值税'</if>
        group by fnsrmc) t2 on t1.fnsrmc = t2.fnsrmc and t1.fsj = t2.fsj
        group by t1.fsj
        union all
        select t1.fsj, sum(fshj) fshj, '新增税源收入' fxm, 'bar' type
        from (select distinct fnsrmc, #{fyearzc} - 2 fsj
        from tb_dw_srfx_swdjxx_main
        where date_format(fdjrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyearzc} - 2, '-', #{fmonthzc_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc} - 2, '-', #{fmonthzc_e}), '%Y-%m'), '%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        ) t1
        left join
        (select fnsrmc, sum(fshj) / 10000 fshj, #{fyear} - 2 fsj
        from tb_dw_srfx_srfx_main
        where date_format(frkrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyear} - 2, '-', #{fmonth_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyear} - 2, '-', #{fmonth_e}), '%Y-%m'), '%Y-%m')
        <if test="fldts == 2">AND
            a.fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND a.fyskm != '免抵调增增值税'</if>
        group by fnsrmc) t2 on t1.fnsrmc = t2.fnsrmc and t1.fsj = t2.fsj
        group by t1.fsj
        union all
        select t1.fsj, sum(fshj) fshj, '税源总收入' fxm, 'bar' type
        from (select distinct fnsrmc, #{fyearzc} fsj
        from tb_dw_srfx_swdjxx_main
        where date_format(fdjrq, '%Y-%m') &lt;
        date_format(STR_TO_DATE(CONCAT(#{fyearzc}, '-', #{fmonthzc_e}), '%Y-%m'), '%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        ) t1
        left join
        (select fnsrmc, sum(fshj) / 10000 fshj, #{fyear} fsj
        from tb_dw_srfx_srfx_main
        where (
        date_format(frkrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyear}, '-', #{fmonth_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyear}, '-', #{fmonth_e}), '%Y-%m'), '%Y-%m')
        )
        <if test="fldts == 2">AND
            a.fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND a.fyskm != '免抵调增增值税'</if>
        group by fnsrmc) t2 on t1.fnsrmc = t2.fnsrmc
        group by fsj
        union all
        select t1.fsj, sum(fshj) fshj, '税源总收入' fxm, 'bar' type
        from (select distinct fnsrmc, #{fyearzc} - 1 fsj
        from tb_dw_srfx_swdjxx_main
        where date_format(fdjrq, '%Y-%m') &lt;
        date_format(STR_TO_DATE(CONCAT(#{fyearzc} - 1, '-', #{fmonthzc_e}), '%Y-%m'), '%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        ) t1
        left join
        (select fnsrmc, sum(fshj) / 10000 fshj
        from tb_dw_srfx_srfx_main
        where (
        date_format(frkrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyear} - 1, '-', #{fmonth_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyear} - 1, '-', #{fmonth_e}), '%Y-%m'), '%Y-%m')
        )
        <if test="fldts == 2">AND
            a.fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND a.fyskm != '免抵调增增值税'</if>
        group by fnsrmc) t2 on t1.fnsrmc = t2.fnsrmc
        group by fsj
        union all
        select t1.fsj, sum(fshj) fshj, '税源总收入' fxm, 'bar' type
        from (select distinct fnsrmc, #{fyearzc} - 2 fsj
        from tb_dw_srfx_swdjxx_main
        where date_format(fdjrq, '%Y-%m') &lt;
        date_format(STR_TO_DATE(CONCAT(#{fyearzc} - 2, '-', #{fmonthzc_e}), '%Y-%m'), '%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        ) t1
        left join
        (select fnsrmc, sum(fshj) / 10000 fshj
        from tb_dw_srfx_srfx_main
        where (
        date_format(frkrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyear} - 2, '-', #{fmonth_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyear} - 2, '-', #{fmonth_e}), '%Y-%m'), '%Y-%m')
        )
        <if test="fldts == 2">AND
            a.fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND a.fyskm != '免抵调增增值税'</if>
        group by fnsrmc) t2 on t1.fnsrmc = t2.fnsrmc
        group by fsj
        union all
        select #{fyearzc} fsj
        , case
        when fshj_qn is null or fshj_qn = '0' or fshj_qn = '' then '100'
        else round((fshj_jn - fshj_qn) * 100 / fshj_qn, 2) end fzb
        , '新增税源同比增长' fxm
        , 'line' type
        from (select sum(fshj) fshj_jn
        from (select distinct fnsrmc
        from tb_dw_srfx_swdjxx_main
        where date_format(fdjrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyearzc}, '-', #{fmonthzc_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc}, '-', #{fmonthzc_e}), '%Y-%m'), '%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        ) t1
        left join
        (select fnsrmc, sum(fshj) / 10000 fshj
        from tb_dw_srfx_srfx_main
        where date_format(frkrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyear}, '-', #{fmonth_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyear}, '-', #{fmonth_e}), '%Y-%m'), '%Y-%m')
        <if test="fldts == 2">AND
            a.fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND a.fyskm != '免抵调增增值税'</if>
        group by fnsrmc) t2 on t1.fnsrmc = t2.fnsrmc) t1
        left join
        (select sum(fshj) fshj_qn
        from (select distinct fnsrmc
        from tb_dw_srfx_swdjxx_main
        where date_format(fdjrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyearzc} - 1, '-', #{fmonthzc_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc} - 1, '-', #{fmonthzc_e}), '%Y-%m'), '%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        ) t1
        left join
        (select fnsrmc, sum(fshj) / 10000 fshj
        from tb_dw_srfx_srfx_main
        where date_format(frkrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyear} - 1, '-', #{fmonth_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyear} - 1, '-', #{fmonth_e}), '%Y-%m'), '%Y-%m')
        <if test="fldts == 2">AND
            a.fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND a.fyskm != '免抵调增增值税'</if>
        group by fnsrmc) t2 on t1.fnsrmc = t2.fnsrmc) t2 on 1 = 1

        union all

        select #{fyearzc} - 1 fsj
        , case
        when fshj_qn is null or fshj_qn = '0' or fshj_qn = '' then '100'
        else round((fshj_jn - fshj_qn) * 100 / fshj_qn, 2) end fzb
        , '新增税源同比增长' fxm
        , 'line' type
        from (select sum(fshj) fshj_jn
        from (select distinct fnsrmc
        from tb_dw_srfx_swdjxx_main
        where date_format(fdjrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyearzc} - 1, '-', #{fmonthzc_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc} - 1, '-', #{fmonthzc_e}), '%Y-%m'), '%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        ) t1
        left join
        (select fnsrmc, sum(fshj) / 10000 fshj
        from tb_dw_srfx_srfx_main
        where date_format(frkrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyear} - 1, '-', #{fmonth_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyear} - 1, '-', #{fmonth_e}), '%Y-%m'), '%Y-%m')
        <if test="fldts == 2">AND
            a.fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND a.fyskm != '免抵调增增值税'</if>
        group by fnsrmc) t2 on t1.fnsrmc = t2.fnsrmc) t1
        left join
        (select sum(fshj) fshj_qn
        from (select distinct fnsrmc
        from tb_dw_srfx_swdjxx_main
        where date_format(fdjrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyearzc} - 2, '-', #{fmonthzc_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc} - 2, '-', #{fmonthzc_e}), '%Y-%m'), '%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        ) t1
        left join
        (select fnsrmc, sum(fshj) / 10000 fshj
        from tb_dw_srfx_srfx_main
        where date_format(frkrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyear} - 2, '-', #{fmonth_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyear} - 2, '-', #{fmonth_e}), '%Y-%m'), '%Y-%m')
        <if test="fldts == 2">AND
            a.fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND a.fyskm != '免抵调增增值税'</if>
        group by fnsrmc) t2 on t1.fnsrmc = t2.fnsrmc) t2 on 1 = 1

        union all

        select #{fyearzc} - 2 fsj
        , case
        when fshj_qn is null or fshj_qn = '0' or fshj_qn = '' then '100'
        else round((fshj_jn - fshj_qn) * 100 / fshj_qn, 2) end fzb
        , '新增税源同比增长' fxm
        , 'line' type
        from (select sum(fshj) fshj_jn
        from (select distinct fnsrmc
        from tb_dw_srfx_swdjxx_main
        where date_format(fdjrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyearzc} - 2, '-', #{fmonthzc_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc} - 2, '-', #{fmonthzc_e}), '%Y-%m'), '%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        ) t1
        left join
        (select fnsrmc, sum(fshj) / 10000 fshj
        from tb_dw_srfx_srfx_main
        where date_format(frkrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyear} - 2, '-', #{fmonth_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyear} - 2, '-', #{fmonth_s}), '%Y-%m'), '%Y-%m')
        <if test="fldts == 2">AND
            a.fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND a.fyskm != '免抵调增增值税'</if>
        group by fnsrmc) t2 on t1.fnsrmc = t2.fnsrmc) t1
        left join
        (select sum(fshj) fshj_qn
        from (select distinct fnsrmc
        from tb_dw_srfx_swdjxx_main
        where date_format(fdjrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyearzc} - 3, '-', #{fmonthzc_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc} - 3, '-', #{fmonthzc_e}), '%Y-%m'), '%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        ) t1
        left join
        (select fnsrmc, sum(fshj) / 10000 fshj
        from tb_dw_srfx_srfx_main
        where date_format(frkrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyear} - 3, '-', #{fmonth_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyear} - 3, '-', #{fmonth_e}), '%Y-%m'), '%Y-%m')
        <if test="fldts == 2">AND
            a.fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND a.fyskm != '免抵调增增值税'</if>
        group by fnsrmc) t2 on t1.fnsrmc = t2.fnsrmc) t2 on 1 = 1) a
    </select>

    <select id="Sszbfxztfxsyhs_Chart" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData">

        select fsl yAxis,fsj xAxis,fxm legend,type from (
        select count(distinct fnsrmc) fsl ,#{fyearzc} fsj,'新增税源户数' fxm,'bar' type from tb_dw_srfx_swdjxx_main
        where
        date_format(fdjrq,'%Y-%m') between
        date_format(STR_TO_DATE(CONCAT(#{fyearzc},'-',#{fmonthzc_s}),'%Y-%m'),'%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc},'-',#{fmonthzc_e}),'%Y-%m'),'%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        union all
        select count(distinct fnsrmc) fsl ,#{fyearzc}-1 fsj,'新增税源户数' fxm,'bar' type from tb_dw_srfx_swdjxx_main
        where
        date_format(fdjrq,'%Y-%m') between
        date_format(STR_TO_DATE(CONCAT(#{fyearzc}-1,'-',#{fmonthzc_s}),'%Y-%m'),'%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc}-1,'-',#{fmonthzc_e}),'%Y-%m'),'%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        union all
        select count(distinct fnsrmc) fsl ,#{fyearzc}-2 fsj,'新增税源户数' fxm,'bar' type from tb_dw_srfx_swdjxx_main
        where
        date_format(fdjrq,'%Y-%m') between
        date_format(STR_TO_DATE(CONCAT(#{fyearzc}-2,'-',#{fmonthzc_s}),'%Y-%m'),'%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc}-2,'-',#{fmonthzc_e}),'%Y-%m'),'%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        union all
        select count(distinct fnsrmc) fsl ,#{fyearzc} fsj,'税源户数' fxm,'bar' type from tb_dw_srfx_swdjxx_main where
        date_format(fdjrq,'%Y-%m') &lt; date_format(STR_TO_DATE(CONCAT(#{fyearzc},'-',#{fmonthzc_e}),'%Y-%m'),'%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        union all
        select count(distinct fnsrmc) fsl ,#{fyearzc}-1 fsj,'税源户数' fxm,'bar' type from tb_dw_srfx_swdjxx_main where
        date_format(fdjrq,'%Y-%m') &lt; date_format(STR_TO_DATE(CONCAT(#{fyearzc}-1,'-',#{fmonthzc_e}),'%Y-%m'),'%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        union all
        select count(distinct fnsrmc) fsl ,#{fyearzc}-2 fsj,'税源户数' fxm,'bar' type from tb_dw_srfx_swdjxx_main where
        date_format(fdjrq,'%Y-%m') &lt; date_format(STR_TO_DATE(CONCAT(#{fyearzc}-2,'-',#{fmonthzc_e}),'%Y-%m'),'%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        union all
        select case when fsl_qn is null or fsl_qn = '' or fsl_qn = '0' then '100' else
        round((fsl_jn-fsl_qn)*100/fsl_qn,2) end fsl
        ,#{fyearzc} fsj,'新增税源户数同比增长' fxm,'line' type
        from (
        select count(distinct fnsrmc) fsl_jn from tb_dw_srfx_swdjxx_main where
        date_format(fdjrq,'%Y-%m') between
        date_format(STR_TO_DATE(CONCAT(#{fyearzc},'-',#{fmonthzc_s}),'%Y-%m'),'%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc},'-',#{fmonthzc_e}),'%Y-%m'),'%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        ) t1 left join
        (
        select count(distinct fnsrmc) fsl_qn from tb_dw_srfx_swdjxx_main where
        date_format(fdjrq,'%Y-%m') between
        date_format(STR_TO_DATE(CONCAT(#{fyearzc}-1,'-',#{fmonthzc_s}),'%Y-%m'),'%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc}-1,'-',#{fmonthzc_e}),'%Y-%m'),'%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        ) t2 on 1=1
        union all
        select case when fsl_qn is null or fsl_qn = '' or fsl_qn = '0' then '100' else
        round((fsl_jn-fsl_qn)*100/fsl_qn,2) end fsl
        ,#{fyearzc}-1 fsj,'新增税源户数同比增长' fxm,'line' type
        from (
        select count(distinct fnsrmc) fsl_jn from tb_dw_srfx_swdjxx_main where
        date_format(fdjrq,'%Y-%m') between
        date_format(STR_TO_DATE(CONCAT(#{fyearzc}-1,'-',#{fmonthzc_s}),'%Y-%m'),'%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc}-1,'-',#{fmonthzc_e}),'%Y-%m'),'%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        ) t1 left join
        (
        select count(distinct fnsrmc) fsl_qn from tb_dw_srfx_swdjxx_main where
        date_format(fdjrq,'%Y-%m') between
        date_format(STR_TO_DATE(CONCAT(#{fyearzc}-2,'-',#{fmonthzc_s}),'%Y-%m'),'%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc}-2,'-',#{fmonthzc_e}),'%Y-%m'),'%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        ) t2 on 1=1
        union all
        select case when fsl_qn is null or fsl_qn = '' or fsl_qn = '0' then '100' else
        round((fsl_jn-fsl_qn)*100/fsl_qn,2) end fsl
        ,#{fyearzc}-2 fsj,'新增税源户数同比增长' fxm,'line' type
        from (
        select count(distinct fnsrmc) fsl_jn from tb_dw_srfx_swdjxx_main where
        date_format(fdjrq,'%Y-%m') between
        date_format(STR_TO_DATE(CONCAT(#{fyearzc}-2,'-',#{fmonthzc_s}),'%Y-%m'),'%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc}-2,'-',#{fmonthzc_e}),'%Y-%m'),'%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        ) t1 left join
        (
        select count(distinct fnsrmc) fsl_qn from tb_dw_srfx_swdjxx_main where
        date_format(fdjrq,'%Y-%m') between
        date_format(STR_TO_DATE(CONCAT(#{fyearzc}-3,'-',#{fmonthzc_s}),'%Y-%m'),'%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc}-3,'-',#{fmonthzc_e}),'%Y-%m'),'%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        ) t2 on 1=1
        ) t


    </select>

    <select id="Sszbfxztfxsyfdq_Chart" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select count(distinct t1.fnsrmc) `value`, t1.fjdxz `name` from tb_dw_srfx_swdjxx_main t1
        left join (
        select fnsrmc, fjdxz,ROW_NUMBER() OVER(PARTITION BY fnsrmc ORDER BY fhj desc) fno
        from tb_dw_srfx_srfx_main group by fnsrmc
        ) t2 on t1.fnsrmc = t2.fnsrmc and fno=1 and t1.fjdxz=t2.fjdxz
        where date_format(fdjrq, '%Y-%m') between date_format(STR_TO_DATE(CONCAT(#{fyearzc}, '-', #{fmonthzc_s}),
        '%Y-%m'),'%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc}, '-', #{fmonthzc_e}), '%Y-%m'), '%Y-%m') and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and t1.fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        group by t1.fjdxz

    </select>

    <select id="Sszbfxztfxsysrfdq_Chart" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select round(sum(fshj), 2) `value`, t1.fjdxz `name`
        from tb_dw_srfx_swdjxx_main t1
        left join
        (select sum(fshj) / 10000 fshj, fjdxz, fnsrmc
        from tb_dw_srfx_srfx_main
        where date_format(frkrq, '%Y-%m') between date_format(STR_TO_DATE(CONCAT(#{fyear}, '-', #{fmonth_s}), '%Y-%m'),
        '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyear}, '-', #{fmonth_e}), '%Y-%m'), '%Y-%m')
        <if test="fldts == 2">AND
            a.fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND a.fyskm != '免抵调增增值税'</if>
        group by fjdxz, fnsrmc) t2 on t1.fnsrmc = t2.fnsrmc and t1.fjdxz=t2.fjdxz
        where date_format(fdjrq, '%Y-%m') between date_format(STR_TO_DATE(CONCAT(#{fyearzc}, '-', #{fmonthzc_s}),
        '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc}, '-', #{fmonthzc_e}), '%Y-%m'), '%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        group by t1.fjdxz
    </select>

    <select id="Sszbfxztfxsyfhy_Chart" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select fhyml `name`, count(distinct fnsrmc) `value`
        from tb_dw_srfx_swdjxx_main t1
        left join TB_DW_ZHZS_HYBZXXB_GEN hy on t1.fhy = hy.fhyxl
        where date_format(fdjrq, '%Y-%m') between date_format(STR_TO_DATE(CONCAT(#{fyearzc}, '-', #{fmonthzc_s}),
        '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc}, '-', #{fmonthzc_e}), '%Y-%m'), '%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        group by fhyml

    </select>

    <select id="Sszbfxztfxsyfhysr_Chart" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select t1.`fhyml` name, round(sum(ifnull(fshj, 0)), 2) `value`
        from (select hy.fhyml, t1.fnsrmc
        from tb_dw_srfx_swdjxx_main t1
        left join TB_DW_ZHZS_HYBZXXB_GEN hy on t1.fhy = hy.fhyxl
        where date_format(fdjrq, '%Y-%m') between date_format(STR_TO_DATE(CONCAT(#{fyearzc}, '-', #{fmonthzc_s}),
        '%Y-%m'),
        '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc}, '-', #{fmonthzc_e}), '%Y-%m'), '%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        ) t1
        left join (select sum(fshj) / 10000 fshj, fhyml, fnsrmc
        from tb_dw_srfx_srfx_main
        where date_format(frkrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyear}, '-', #{fmonth_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyear}, '-', #{fmonth_e}), '%Y-%m'), '%Y-%m')
        <if test="fldts == 2">AND
            a.fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND a.fyskm != '免抵调增增值税'</if>
        group by fhyml, fnsrmc) ss on t1.fnsrmc = ss.fnsrmc and t1.fhyml = ss.fhyml
        group by t1.fhyml
    </select>

    <select id="Sszbfxztfxsyfzclx_Chart" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select fdjzclx `name`, count(distinct fnsrmc) `value`
        from tb_dw_srfx_swdjxx_main t1
        where date_format(fdjrq, '%Y-%m') between date_format(STR_TO_DATE(CONCAT(#{fyearzc}, '-', #{fmonthzc_s}),
        '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc}, '-', #{fmonthzc_e}), '%Y-%m'), '%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        group by fdjzclx

    </select>

    <select id="Sszbfxztfxsyfzclxsr_Chart" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select fdjzclx `name`, round(sum(ifnull(fshj, 0)), 2) `value`
        from (select fdjzclx, fnsrmc
        from tb_dw_srfx_swdjxx_main t1
        where date_format(fdjrq, '%Y-%m') between date_format(STR_TO_DATE(CONCAT(#{fyearzc}, '-', #{fmonthzc_s}),
        '%Y-%m'),
        '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyearzc}, '-', #{fmonthzc_e}), '%Y-%m'), '%Y-%m')
        and fnsrzt = '正常'
        <if test="fcode_ss == 1">
            and fdjzclx
            not in ('内资个体','内资个体','外资个体','外资个人','国家机关','事业单位','社会团体'
            ,'其他','内资合伙','基层群众自治组织','政党机关','基金会','民办非企业单位（法人）'
            ,'民办非企业单位（个体）','农村集体经济组织')
        </if>
        ) t1
        left join (select sum(fshj) / 10000 fshj, fnsrmc
        from tb_dw_srfx_srfx_main
        where date_format(frkrq, '%Y-%m') between date_format(
        STR_TO_DATE(CONCAT(#{fyear}, '-', #{fmonth_s}), '%Y-%m'), '%Y-%m')
        and date_format(STR_TO_DATE(CONCAT(#{fyear}, '-', #{fmonth_e}), '%Y-%m'), '%Y-%m')
        <if test="fldts == 2">AND
            a.fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND a.fyskm != '免抵调增增值税'</if>
        group by fhyml, fnsrmc) ss on t1.fnsrmc = ss.fnsrmc
        group by fdjzclx

    </select>

    <select id="subIndustryAnalysis_new_bzb" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="java.util.HashMap">
        select case when fname is null then '合计' else fname end fname
        ,round(nvl(sum(fsshjjn),0),2) fsshjjn
        ,round(nvl(sum(fsshjqn),0),2) fsshjqn,
        round(nvl(sum(fsshjjn),0)-nvl(sum(fsshjqn),0),2) fzj
        ,round(case nvl(sum(fsshjqn),0) when 0 then 100 else
        (nvl(sum(fsshjjn),0)-nvl(sum(fsshjqn),0))/nvl(sum(fsshjqn),0) * 100 end,2) fbl
        from
        (
        select fname
        , sum(case when fyear = #{start_year} then fhj else 0 end)/10000 fsshjjn
        , sum(case when fyear = #{ago_year} then fhj else 0 end)/10000 fsshjqn
        from (
        select
        <choose>
            <when test='type == "行业"'>fhyml</when>
            <when test='type == "地区"'>fdq</when>
            <when test='type == "税种"'>fsz</when>
        </choose> fname
        , YEAR(to_date(frkrq,'YYYY-MM')) AS fyear
        , sum(fje) fhj
        from ${tableName}
        where
        (
        to_date(frkrq,'YYYY-MM') between to_date(CONCAT(#{start_year},'-',#{fstartmonth}),'YYYY-MM')
        and last_day(to_date(CONCAT(#{start_year},'-',#{fendmonth}),'YYYY-MM'))
        or
        to_date(frkrq,'YYYY-MM') between to_date(CONCAT(#{ago_year},'-',#{fstartmonth}),'YYYY-MM')
        and last_day(to_date(CONCAT(#{ago_year},'-',#{fendmonth}),'YYYY-MM'))
        )

        <if test="fzsxmStr != null and fzsxmStr!= ''">and
            fsz in (${fzsxmStr})
        </if>
        <if test="fhydlStr != null and fhydlStr!= ''">and
            fhydl in (${fhydlStr})
        </if>
        <if test="fssqyStr != null and fssqyStr != ''">AND
            fdq IN (${fssqyStr})
        </if>

        /*and fsz in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        */  group by
        <choose>
            <when test='type == "行业"'>fhyml</when>
            <when test='type == "地区"'>fdq</when>
            <when test='type == "税种"'>fsz</when>
        </choose>, YEAR(to_date(frkrq,'YYYY-MM'))
        ) tmp1 GROUP BY fname
        ) ss
        where
        case
        when #{ftype} = '增长'
        and ifnull(fsshjjn, 0) &gt;= ifnull(fsshjqn, 0)
        then 'ok'
        when #{ftype} = '下降'
        and ifnull(fsshjqn, 0) &gt;= ifnull(fsshjjn, 0)
        then 'ok'
        else 'err'
        end = 'ok'
        group by fname with rollup
        order by grouping(fname) desc
        , case
        when #{ftype} = '增长'
        then ifnull(fsshjjn, 0) - ifnull(fsshjqn, 0)
        when #{ftype} = '下降'
        then ifnull(fsshjqn, 0) - ifnull(fsshjjn, 0)
        end
        desc
    </select>

</mapper>
