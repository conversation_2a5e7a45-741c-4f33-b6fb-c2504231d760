<?xml version="1.0" encoding="UTF-8"?><!--

    Copyright 2004-2024 the original author or authors.

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

--><!--Converted at: Sat Sep 14 10:20:55 CST 2024-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.sszbfx.mapper.TaxAnalyseMapper">


    <select id="queryOverallTaxPicture" parameterType="com.hnbp.local.sszbfx.model.QueryCondition" resultType="hashmap">
        select round(sum(合计) / 10000, 2) value, fskgk name
        from (select 合计, 年份, 月份, fskgk
            from zhzs_srfx_ss_all
            where 1 = 1
            and 年份 = '2019'
            and 月份 &gt;= '01'
            and 月份 &lt;= '06'
            and fskgk =#{fssqy}
            and fzsxm =#{fzsxm}
            ) as t1
        group by fskgk, 年份
    </select>
    <select id="overallStreetConditionsChart" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select round(sum(合计) / 10000, 2) value, fskgk name
        from (select 合计, 收款国库 fskgk
        from ZHZS_T_SS_SRFX
        where 1 = 1
        and 入库日期 between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth}),'%Y-%m') and
        last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth}),'%Y-%m'))
        <if test="fssqyStr != null and fssqyStr != ''">and
            收款国库 in (${fssqyStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            行业大类 in (${fhydlStr})
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            征收项目 in (${fzsxmStr})
        </if>
        ) as t1
        group by fskgk
        order by value desc
    </select>
    <select id="queryStreetDetails" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData">
        select sum(实缴金额) yAxis, 年份 legend, 月份 xAxis
        from (select round(sum(合计) / 10000, 2) 实缴金额,
        to_char(入库日期,'yyyy') 年份,
        to_char(入库日期,'mm') 月份
        from ZHZS_T_SS_SRFX
        where 入库日期 between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth}),'%Y-%m') and
        last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth}),'%Y-%m'))

        <if test="fssqyStr != null and fssqyStr != ''">and
            收款国库 in (${fssqyStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            行业大类 in (${fhydlStr})
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            征收项目 in (${fzsxmStr})
        </if>
        group by to_char(入库日期,'yyyy'), to_char(入库日期,'mm') ) as t1
        group by 年份, 月份
        order by 年份, 月份
    </select>
    <!--区县完成情况图chart-->
    <select id="districtAndCountyCompletionChart" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData">
        select jd xAxis , nf legend, yAxis
        from (
        select fskgk as jd, YEAR(frkrq) as nf
        , round(sum(a.fhj) / 10000, 2) as yAxis
        from tb_dw_srfx_srfx_main a
        where (
        frkrq between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth}),'%Y-%m')
        and last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth}),'%Y-%m'))
        or
        frkrq between str_to_date(CONCAT(#{start_year} - 1,'-',#{fstartmonth}),'%Y-%m')
        and last_day(str_to_date(CONCAT(#{start_year} - 1,'-',#{fendmonth}),'%Y-%m'))
        or
        frkrq between str_to_date(CONCAT(#{start_year} - 2,'-',#{fstartmonth}),'%Y-%m')
        and last_day(str_to_date(CONCAT(#{start_year} - 2,'-',#{fendmonth}),'%Y-%m'))
        )
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        <if test="fldts == 2">AND
            fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        group by fskgk, YEAR(frkrq)
        ) as t1
        order by nf, SUM(yAxis) over(partition by jd) desc
    </select>

    <select id="TjdistrictAndCountyCompletionChart" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData">
        select jd xAxis , nf legend, yAxis
        from (
        select fdq as jd, substr(frkrq,1,4) as nf
        , round(sum(a.fje) / 10000, 2) as yAxis
        from TB_DW_ZHZS_DQSZ_GEN a
        where (
        frkrq between CONCAT(#{start_year},'-',#{fstartmonth})
        and CONCAT(#{start_year},'-',#{fendmonth})
        or
        frkrq between CONCAT(#{start_year} - 1,'-',#{fstartmonth})
        and CONCAT(#{start_year} - 1,'-',#{fendmonth})
        or
        frkrq between CONCAT(#{start_year} - 2,'-',#{fstartmonth})
        and CONCAT(#{start_year} - 2,'-',#{fendmonth})
        )
        <if test="fssqyStr != null and fssqyStr != ''">and
            fdq in (${fssqyStr})
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fsz in (${fzsxmStr})
        </if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        and fsz in ('增值税','消费税','企业所得税','个人所得税','资源税','城市维护建设税','印花税','房产税','城镇土地使用税','土地增值税','车船税','车辆购置税','烟叶税','耕地占用税','契税','环境保护税','其他税收')
        group by fdq, substr(frkrq,1,4)
        ) as t1
        order by nf, SUM(yAxis) over(partition by jd) desc
    </select>


    <!--区县完成情况表Table-->
    <select id="districtAndCountyCompletionTable" parameterType="java.util.Map" resultType="java.util.Map">
        select case when grouping(fskgk) = 1 then '合计' else fskgk end fskgk,
        round(SUM(fhj), 2) fhj,
        round(SUM(fzyj), 2) fzyj,
        round(SUM(fssj), 2) fssj,
        round(SUM(fdsj), 2) fdsj,
        round(SUM(fqxj), 2) fqxj,
        round(SUM(fhj_qn), 2) fhj_qn,
        round(SUM(fzyj_qn), 2) fzyj_qn,
        round(SUM(fssj_qn), 2) fssj_qn,
        round(SUM(fdsj_qn), 2) fdsj_qn,
        round(SUM(fqxj_qn), 2) fqxj_qn,
        round(ifnull(SUM(fhj), 0) - ifnull(SUM(fhj_qn), 0), 2) fzje_hj,
        case when ifnull(SUM(fhj_qn), 0) = 0 then null
        else round((ifnull(SUM(fhj), 0) - ifnull(SUM(fhj_qn), 0)) / ifnull(SUM(fhj_qn), 0) * 100, 2)
        end fzjb_hj,
        round(ifnull(SUM(fqxj), 0) - ifnull(SUM(fqxj_qn), 0), 2) fzje_qxj,
        case when ifnull(SUM(fqxj_qn), 0) = 0 then null
        else round((ifnull(SUM(fqxj), 0) - ifnull(SUM(fqxj_qn), 0)) / ifnull(SUM(fqxj_qn), 0) * 100, 2)
        end fzjb_qxj
        from (
        SELECT
        fskgk fskgk,
        SUM(case when year(
        <if test="fskssqq != null and fskssqq != ''">fskssqq</if>
        <if test="fskssqq == null or fskssqq == ''">frkrq</if>) = #{start_year} then srfx.fhj else 0 end)/10000 fhj,
        SUM(case when year(
        <if test="fskssqq != null and fskssqq != ''">fskssqq</if>
        <if test="fskssqq == null or fskssqq == ''">frkrq</if>) = #{start_year} then srfx.fzyj else 0 end)/10000 fzyj,
        SUM(case when year(
        <if test="fskssqq != null and fskssqq != ''">fskssqq</if>
        <if test="fskssqq == null or fskssqq == ''">frkrq</if>) = #{start_year} then srfx.fssj else 0 end)/10000 fssj,
        SUM(case when year(
        <if test="fskssqq != null and fskssqq != ''">fskssqq</if>
        <if test="fskssqq == null or fskssqq == ''">frkrq</if>) = #{start_year} then srfx.fdsj else 0 end)/10000 fdsj,
        SUM(case when year(
        <if test="fskssqq != null and fskssqq != ''">fskssqq</if>
        <if test="fskssqq == null or fskssqq == ''">frkrq</if>) = #{start_year} then srfx.fqxj else 0 end)/10000 fqxj,
        SUM(case when year(
        <if test="fskssqq != null and fskssqq != ''">fskssqq</if>
        <if test="fskssqq == null or fskssqq == ''">frkrq</if>) = #{start_year} - 1 then srfx.fhj else 0 end)/10000
        fhj_qn,
        SUM(case when year(
        <if test="fskssqq != null and fskssqq != ''">fskssqq</if>
        <if test="fskssqq == null or fskssqq == ''">frkrq</if>) = #{start_year} - 1 then srfx.fzyj else 0 end)/10000
        fzyj_qn,
        SUM(case when year(
        <if test="fskssqq != null and fskssqq != ''">fskssqq</if>
        <if test="fskssqq == null or fskssqq == ''">frkrq</if>) = #{start_year} - 1 then srfx.fssj else 0 end)/10000
        fssj_qn,
        SUM(case when year(
        <if test="fskssqq != null and fskssqq != ''">fskssqq</if>
        <if test="fskssqq == null or fskssqq == ''">frkrq</if>) = #{start_year} - 1 then srfx.fdsj else 0 end)/10000
        fdsj_qn,
        SUM(case when year(
        <if test="fskssqq != null and fskssqq != ''">fskssqq</if>
        <if test="fskssqq == null or fskssqq == ''">frkrq</if>) = #{start_year} - 1 then srfx.fqxj else 0 end)/10000
        fqxj_qn
        FROM tb_dw_srfx_srfx_main srfx
        WHERE 1=1
        <if test="fskssqq != null and fskssqq != ''">and
            (
            srfx.fskssqq BETWEEN #{fskssqq} and #{fskssqq}
            OR srfx.fskssqq BETWEEN #{fskssqq} AND #{fskssqq}
            )
        </if>
        <if test="fskssqq == null or fskssqq == ''">and
            (srfx.frkrq BETWEEN CONCAT(#{start_year}, '-', #{fstartmonth}, '-01')
            AND last_day(str_to_date(CONCAT(#{start_year}, '-', #{fendmonth}), '%Y-%m'))
            OR srfx.frkrq BETWEEN CONCAT(#{start_year} - 1, '-', #{fstartmonth}, '-01')
            AND last_day(str_to_date(CONCAT(#{start_year} - 1, '-', #{fendmonth}), '%Y-%m'))
            )
        </if>
        <if test="fssqyStr != null and fssqyStr != ''">AND
            fskgk IN (${fssqyStr})
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">AND
            fzsxm IN (${fzsxmStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">AND
            fhydl IN (${fhydlStr})

        </if>
        <if test="fzcdjlxStr != null and fzcdjlxStr != ''">AND
            fdjzclx in (${fzcdjlxStr})
        </if>
        <if test="fldts == 2">AND
            fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        GROUP BY fskgk
        ) a
        GROUP BY fskgk WITH ROLLUP
        order by grouping(fskgk) desc, fhj * 1 desc
    </select>

    <select id="TjdistrictAndCountyCompletionTable" parameterType="java.util.Map" resultType="java.util.Map">
        select case when grouping(fskgk) = 1 then '合计' else fskgk end fskgk,
        round(SUM(fhj), 2) fhj,
        round(SUM(fhj_qn), 2) fhj_qn,
        round(ifnull(SUM(fhj), 0) - ifnull(SUM(fhj_qn), 0), 2) fzje_hj,
        case when ifnull(SUM(fhj_qn), 0) = 0 then null
        else round((ifnull(SUM(fhj), 0) - ifnull(SUM(fhj_qn), 0)) / ifnull(SUM(fhj_qn), 0) * 100, 2)
        end fzjb_hj
        from (
        SELECT
        fdq fskgk,
        SUM(case when substr(frkrq,1,4) = #{start_year} then srfx.fje else 0 end)/10000 fhj,
        SUM(case when substr(frkrq,1,4) = #{start_year} - 1 then srfx.fje else 0 end)/10000 fhj_qn
        FROM TB_DW_ZHZS_DQSZ_GEN srfx
        WHERE 1=1
        <if test="fskssqq != null and fskssqq != ''">and
            (
            srfx.fskssqq BETWEEN #{fskssqq} and #{fskssqq}
            OR srfx.fskssqq BETWEEN #{fskssqq} AND #{fskssqq}
            )
        </if>
        <if test="fskssqq == null or fskssqq == ''">and
            (srfx.frkrq BETWEEN CONCAT(#{start_year}, '-', #{fstartmonth})
            AND CONCAT(#{start_year}, '-', #{fendmonth})
            OR srfx.frkrq BETWEEN CONCAT(#{start_year} - 1, '-', #{fstartmonth})
            AND CONCAT(#{start_year} - 1, '-', #{fendmonth})
            )
        </if>
        <if test="fssqyStr != null and fssqyStr != ''">AND
            fdq IN (${fssqyStr})
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">AND
            fsz IN (${fzsxmStr})
        </if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        and fsz in ('增值税','消费税','企业所得税','个人所得税','资源税','城市维护建设税','印花税','房产税','城镇土地使用税','土地增值税','车船税','车辆购置税','烟叶税','耕地占用税','契税','环境保护税','其他税收')
        GROUP BY fdq
        ) a
        GROUP BY fskgk WITH ROLLUP
        order by grouping(fskgk) desc, fhj * 1 desc
    </select>


    <!--税种税收走势-->
    <select id="TaxTrends" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select 征收项目 name, round(sum(税合计) / 10000, 0) value
        from ZHZS_T_SS_SRFX
        where
            入库日期 between str_to_date(CONCAT(#{start_year}
            , '-'
            , #{fstartmonth})
            , '%Y-%m')
          and last_day(str_to_date(CONCAT(#{start_year}
            , '-'
            , #{fendmonth})
            , '%Y-%m'))
          and (征收项目 like '%税'
           or 征收项目 in ('教育费附加'
            , '地方教育附加'))
        group by 征收项目
        order by sum (税合计) desc
    </select>
    <!--按区县税收走势 DistrictAndCountyTaxTrends 02-->
    <select id="DistrictAndCountyTaxTrends" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select 收款国库 name, round(sum(合计) / 10000, 0) value
        from ZHZS_T_SS_SRFX
        where 入库日期 between left (#{start_year}
            , 4)
          and ADDDATE(left (#{start_year}
            , 4)
            , INTERVAL 12 MONTH)-1
        group by 收款国库
        order by sum (合计) desc
    </select>
    <!--按产业税收走势  IndustryTaxTrend 03-->
    <select id="IndustryTaxTrend" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select fcymc name, round(sum(合计) / 10000, 0) value
        from ZHZS_T_SS_SRFX a
            left join (select distinct fcy as fcymc, fhyml from TB_DM_CYJS_CYHYXXB_GEN) b
        on a.行业门类 = b.fhyml
        where 入库日期 between left (#{start_year}
            , 4)
          and ADDDATE(left (#{start_year}
            , 4)
            , INTERVAL 12 MONTH)-1
        group by fcymc
        order by case when `name`='第一产业' then 01 when `name`='第二产业' then 02 when `name`='第三产业' then 03
        end
    </select>
    <!--整体情况税收分析-->
    <select id="OverallTaxTrends" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.line.LineSeriesInitData">
        select round(sum(合计) / 10000, 0) yAxis,
               #{start_year}               legend,
               to_char(入库日期, 'mm')     xAxis
        from ZHZS_T_SS_SRFX a
        where 入库日期 between str_to_date(CONCAT(#{start_year}, '-', #{fstartmonth}), '%Y-%m') and str_to_date(
                CONCAT(#{start_year}, '-', #{fendmonth}, '-', '31'), '%Y-%m-%d')
        group by to_char(入库日期, 'mm')

        union all

        select round(sum(合计) / 10000, 0) yAxis,
               #{start_year} - 1           legend,
               to_char(入库日期, 'mm')     xAxis
        from ZHZS_T_SS_SRFX a
        where 入库日期 between str_to_date(CONCAT(#{start_year} - 1, '-', #{fstartmonth}), '%Y-%m') and str_to_date(
                CONCAT(#{start_year} - 1, '-', #{fendmonth}, '-', '31'), '%Y-%m-%d')
        group by to_char(入库日期, 'mm')

        union all

        select round(sum(合计) / 10000, 0) yAxis,
               #{start_year} - 2           legend,
               to_char(入库日期, 'mm')     xAxis
        from ZHZS_T_SS_SRFX a
        where 入库日期 between str_to_date(CONCAT(#{start_year} - 2, '-', #{fstartmonth}), '%Y-%m') and str_to_date(
                CONCAT(#{start_year} - 2, '-', #{fendmonth}, '-', '31'), '%Y-%m-%d')
        group by to_char(入库日期, 'mm')

        union all

        select round(sum(合计) / 10000, 0) yAxis,
               #{start_year} - 3           legend,
               to_char(入库日期, 'mm')     xAxis
        from ZHZS_T_SS_SRFX a
        where 入库日期 between str_to_date(CONCAT(#{start_year} - 3, '-', #{fstartmonth}), '%Y-%m') and str_to_date(
                CONCAT(#{start_year} - 3, '-', #{fendmonth}, '-', '31'), '%Y-%m-%d')
        group by to_char(入库日期, 'mm')
    </select>

    <!--税种明细-->
    <select id="theTaxSubsidiary" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData">
        select round(sum(合计) / 10000, 0) yAxis,
        #{start_year} legend,
        to_char(入库日期,'mm') xAxis
        from ZHZS_T_SS_SRFX
        where 入库日期 between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth}),'%Y-%m') and
        str_to_date(CONCAT(#{start_year},'-',#{fendmonth},'-','31'),'%Y-%m-%d')
        <if test="fzsxm != null and fzsxm != ''">and
            征收项目 = #{fzsxm}
        </if>
        group by to_char(入库日期,'mm')

        union all

        select round(sum(合计) / 10000, 0) yAxis,
        #{start_year}-1 legend,
        to_char(入库日期,'mm') xAxis
        from ZHZS_T_SS_SRFX
        where 入库日期 between str_to_date(CONCAT(#{start_year}-1,'-',#{fstartmonth}),'%Y-%m') and
        str_to_date(CONCAT(#{start_year}-1,'-',#{fendmonth},'-','31'),'%Y-%m-%d')
        <if test="fzsxm != null and fzsxm != ''">and
            征收项目 = #{fzsxm}
        </if>
        group by to_char(入库日期,'mm')

        union all

        select round(sum(合计) / 10000, 0) yAxis,
        #{start_year}-2 legend,
        to_char(入库日期,'mm') xAxis
        from ZHZS_T_SS_SRFX
        where 入库日期 between str_to_date(CONCAT(#{start_year}-2,'-',#{fstartmonth}),'%Y-%m') and
        str_to_date(CONCAT(#{start_year}-2,'-',#{fendmonth},'-','31'),'%Y-%m-%d')
        <if test="fzsxm != null and fzsxm != ''">and
            征收项目 = #{fzsxm}
        </if>
        group by to_char(入库日期,'mm')

        union all

        select round(sum(合计) / 10000, 0) yAxis,
        #{start_year}-3 legend,
        to_char(入库日期,'mm') xAxis
        from ZHZS_T_SS_SRFX
        where 入库日期 between str_to_date(CONCAT(#{start_year}-3,'-',#{fstartmonth}),'%Y-%m') and
        str_to_date(CONCAT(#{start_year}-3,'-',#{fendmonth},'-','31'),'%Y-%m-%d')
        <if test="fzsxm != null and fzsxm != ''">and
            征收项目 = #{fzsxm}
        </if>
        group by to_char(入库日期,'mm')
    </select>
    <!--按地区明细-->
    <select id="areaCountyDetail" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData">
        select round(sum(合计) / 10000, 0) yAxis,
        #{start_year} legend,
        to_char(入库日期,'mm') xAxis
        from ZHZS_T_SS_SRFX
        where 入库日期 between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth}),'%Y-%m') and
        str_to_date(CONCAT(#{start_year},'-',#{fendmonth},'-','31'),'%Y-%m-%d')
        <if test="fssqy != null and fssqy != ''">and
            收款国库 = #{fssqy}
        </if>
        group by to_char(入库日期,'mm')

        union all

        select round(sum(合计) / 10000, 0) yAxis,
        #{start_year}-1 legend,
        to_char(入库日期,'mm') xAxis
        from ZHZS_T_SS_SRFX
        where 入库日期 between str_to_date(CONCAT(#{start_year}-1,'-',#{fstartmonth}),'%Y-%m') and
        str_to_date(CONCAT(#{start_year}-1,'-',#{fendmonth},'-','31'),'%Y-%m-%d')
        <if test="fssqy != null and fssqy != ''">and
            收款国库 = #{fssqy}
        </if>
        group by to_char(入库日期,'mm')


        union all

        select round(sum(合计) / 10000, 0) yAxis,
        #{start_year}-2 legend,
        to_char(入库日期,'mm') xAxis
        from ZHZS_T_SS_SRFX
        where 入库日期 between str_to_date(CONCAT(#{start_year}-2,'-',#{fstartmonth}),'%Y-%m') and
        str_to_date(CONCAT(#{start_year}-2,'-',#{fendmonth},'-','31'),'%Y-%m-%d')
        <if test="fssqy != null and fssqy != ''">and
            收款国库 = #{fssqy}
        </if>
        group by to_char(入库日期,'mm')

        union all

        select round(sum(合计) / 10000, 0) yAxis,
        #{start_year}-3 legend,
        to_char(入库日期,'mm') xAxis
        from ZHZS_T_SS_SRFX
        where 入库日期 between str_to_date(CONCAT(#{start_year}-3,'-',#{fstartmonth}),'%Y-%m') and
        str_to_date(CONCAT(#{start_year}-3,'-',#{fendmonth},'-','31'),'%Y-%m-%d')
        <if test="fssqy != null and fssqy != ''">and
            收款国库 = #{fssqy}
        </if>
        group by to_char(入库日期,'mm')
    </select>
    <!--按产业明细-->
    <select id="theIndustryOfSubsidiary" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData">
        select round(sum(合计) / 10000, 0) yAxis,
        #{start_year} legend,
        to_char(入库日期,'mm') xAxis
        from ZHZS_T_SS_SRFX a
        left join (select distinct fcy as fcymc,fhyml from TB_DM_CYJS_CYHYXXB_GEN) b
        on a.行业门类 = b.fhyml
        where 入库日期 between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth}),'%Y-%m') and
        last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth}),'%Y-%m'))
        <if test="fcymc != null and fcymc != ''">and
            fcymc = #{fcymc}
        </if>
        group by to_char(入库日期,'mm')

        union all

        select round(sum(合计) / 10000, 0) yAxis,
        #{start_year}-1 legend,
        to_char(入库日期,'mm') xAxis
        from ZHZS_T_SS_SRFX a
        left join (select distinct fcy as fcymc,fhyml from TB_DM_CYJS_CYHYXXB_GEN) b
        on a.行业门类 = b.fhyml
        where 入库日期 between str_to_date(CONCAT(#{start_year}-1,'-',#{fstartmonth}),'%Y-%m') and
        last_day(str_to_date(CONCAT(#{start_year}-1,'-',#{fendmonth}),'%Y-%m'))
        <if test="fcymc != null and fcymc != ''">and
            fcymc = #{fcymc}
        </if>
        group by to_char(入库日期,'mm')

        union all

        select round(sum(合计) / 10000, 0) yAxis,
        #{start_year}-2 legend,
        to_char(入库日期,'mm') xAxis
        from ZHZS_T_SS_SRFX a
        left join (select distinct fcy as fcymc,fhyml from TB_DM_CYJS_CYHYXXB_GEN) b
        on a.行业门类 = b.fhyml
        where 入库日期 between str_to_date(CONCAT(#{start_year}-2,'-',#{fstartmonth}),'%Y-%m') and
        last_day(str_to_date(CONCAT(#{start_year}-2,'-',#{fendmonth}),'%Y-%m'))
        <if test="fcymc != null and fcymc != ''">and
            fcymc = #{fcymc}
        </if>
        group by to_char(入库日期,'mm')

        union all

        select round(sum(合计) / 10000, 0) yAxis,
        #{start_year}-3 legend,
        to_char(入库日期,'mm') xAxis
        from ZHZS_T_SS_SRFX a
        left join (select distinct fcy as fcymc,fhyml from TB_DM_CYJS_CYHYXXB_GEN) b
        on a.行业门类 = b.fhyml
        where 入库日期 between str_to_date(CONCAT(#{start_year}-3,'-',#{fstartmonth}),'%Y-%m') and
        last_day(str_to_date(CONCAT(#{start_year}-3,'-',#{fendmonth}),'%Y-%m'))
        <if test="fcymc != null and fcymc != ''">and
            fcymc = #{fcymc}
        </if>
        group by to_char(入库日期,'mm')
    </select>


    <!--行业情况分析明细(图) -->
    <select id="industryAnalysisDetails" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select z.*,'' rownum
        from (select fhydl as name,
        round(sum(fhj) / 10000, 2) as value
        from tb_dw_srfx_srfx_main a
        where frkrq between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth}),'%Y-%m')
        and last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth}),'%Y-%m'))
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhymlStr != null and fhymlStr != ''">and
            fhyml in (${fhymlStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        <if test="fldts == 2">AND
            fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        group by fhydl
        order by value desc) z
    </select>

    <select id="industryAnalysisDetailsV2" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.common.core.utils.zhzs.echarts.pie.PieChartsSeriesData">
        select z.*
        from (
        select fhydl as name,
        round(sum(fje) / 10000, 2) as value
        from TB_DW_ZHZS_HYSZ_GEN a
        where STR_TO_DATE(frkrq, '%Y-%m') between to_date(CONCAT(#{start_year},'-',#{fstartmonth}),'%Y-%m')
        and last_day(to_date(CONCAT(#{start_year},'-',#{fendmonth}),'%Y-%m'))
        <if test="fhymlStr != null and fhymlStr != ''">
            and fhyml in (${fhymlStr})
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">
            and fsz in (${fzsxmStr})
        </if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        and fsz in ('教育费附加','地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        group by fhydl
        order by value desc) z
    </select>


    <!--地区税收情况table    RegionalTaxSituation-->
    <select id="regionalTaxSituation" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.local.sszbfx.model.IncomeInformation">
        select (增值税 + 个人所得税 + 耕地占用税 + 印花税 + 房产税 + 土地增值税 + 企业所得税 + 营业税 + 资源税 +
        城市维护建设税 + 契税 + 城镇土地使用税 + 烟叶税 + 环境保护税 + 消费税 + 车船税 + 车辆购置税 ) total,
        增值税 fzzs,
        个人所得税 fgrsds,
        耕地占用税 fgdzys,
        印花税 fyhs,
        房产税 ffcs,
        土地增值税 ftdzzs,
        企业所得税 fqysds,
        营业税 fyys,
        资源税 fzys,
        城市维护建设税 fcswhjss,
        契税 fqs,
        城镇土地使用税 fcztdsys,
        烟叶税 fyanys,
        fskgk,
        环境保护税 fhjbhs,
        消费税 fxfs,
        车船税 fccs,
        车辆购置税 fclgzs,
        税务部门罚没收入 fzfbmfmsr,
        地方教育附加 fdfjyfj,
        其他收入 fqtsr,
        水利建设专项收入 fsljszxsr,
        其他政府性基金收入 fqtzfxjjsr,
        残疾人就业保障金 fcjrjybzj,
        文化事业建设费 fwhsyjsf,
        教育费附加 fjyffj
        from (select round(sum(增值税) / 10000, 2) 增值税,
        round(sum(个人所得税) / 10000, 2) 个人所得税,
        round(sum(耕地占用税) / 10000, 2) 耕地占用税,
        round(sum(印花税) / 10000, 2) 印花税,
        round(sum(房产税) / 10000, 2) 房产税,
        round(sum(土地增值税) / 10000, 2) 土地增值税,
        round(sum(企业所得税) / 10000, 2) 企业所得税,
        round(sum(营业税) / 10000, 2) 营业税,
        round(sum(nvl(资源税, 0)) / 10000, 2) as 资源税,
        round(sum(nvl(城市维护建设税, 0)) / 10000, 2) as 城市维护建设税,
        round(sum(nvl(契税, 0)) / 10000, 2) as 契税,
        round(sum(nvl(城镇土地使用税, 0)) / 10000, 2) as 城镇土地使用税,
        round(sum(nvl(烟叶税, 0)) / 10000, 2) as 烟叶税,
        case when grouping(fskgk)= 1 then '合计' else fskgk end fskgk,
        round(sum(nvl(环境保护税, 0)) / 10000, 2) as 环境保护税,
        round(sum(nvl(消费税, 0)) / 10000, 2) as 消费税,
        round(sum(nvl(车辆购置税, 0)) / 10000, 2) as 车辆购置税,
        round(sum(nvl(税务部门罚没收入, 0)) / 10000, 2) as 税务部门罚没收入,
        round(sum(nvl(地方教育附加, 0)) / 10000, 2) as 地方教育附加,
        round(sum(nvl(其他收入, 0)) / 10000, 2) as 其他收入,
        round(sum(nvl(水利建设专项收入, 0)) / 10000, 2) as 水利建设专项收入,
        round(sum(nvl(其他政府性基金收入, 0)) / 10000, 2) as 其他政府性基金收入,
        round(sum(nvl(残疾人就业保障金, 0)) / 10000, 2) as 残疾人就业保障金,
        round(sum(nvl(文化事业建设费, 0)) / 10000, 2) as 文化事业建设费,
        round(sum(nvl(教育费附加, 0)) / 10000, 2) as 教育费附加,
        round(sum(nvl(车船税, 0)) / 10000, 2) as 车船税
        from (select sum(增值税) 增值税,
        sum(耕地占用税) 耕地占用税,
        sum( 印花税) 印花税,
        sum(房产税) 房产税,
        sum(个人所得税) 个人所得税,
        sum(土地增值税) 土地增值税,
        sum(企业所得税) 企业所得税,
        sum(营业税) 营业税,
        sum(资源税) 资源税,
        sum(城市维护建设税) 城市维护建设税,
        sum(契税) 契税,
        sum(城镇土地使用税) 城镇土地使用税,
        sum(烟叶税) 烟叶税,
        收款国库 fskgk,
        sum(环境保护税) 环境保护税,
        sum(消费税) 消费税,
        sum(车船税) 车船税,
        sum(车辆购置税) 车辆购置税,
        sum(税务部门罚没收入) 税务部门罚没收入,
        sum(地方教育附加) 地方教育附加,
        sum(其他收入) 其他收入,
        sum(水利建设专项收入) 水利建设专项收入,
        sum(其他政府性基金收入) 其他政府性基金收入,
        sum(残疾人就业保障金) 残疾人就业保障金,
        sum(文化事业建设费) 文化事业建设费,
        sum(教育费附加) 教育费附加
        from ZHZS_T_SS_SRFX sz
        where 入库日期 between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth}) ,'%Y-%m') and
        last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth}) ,'%Y-%m'))
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            征收项目 in (${fzsxmStr})
        </if>
        <if test="fssqyStr != null and fssqyStr != ''">and
            收款国库 in (${fssqyStr})
        </if>
        <if test="fhymlStr != null and fhymlStr != ''">and
            行业大类 in (${fhymlStr})
        </if>
        group by 收款国库
        ) as t1
        group by (fskgk) with rollup) as t2 order by case when fskgk='合计' then 00 else 01 end
    </select>

    <!--单月税收情况 getSZByMonth  MonthlyTaxSituation-->
    <select id="monthlyTaxSituation" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.local.sszbfx.model.IncomeInformation">
        select (增值税 + 个人所得税 + 耕地占用税 + 印花税 + 房产税 + 土地增值税 + 企业所得税 + 营业税 + 资源税 +
        城市维护建设税 + 契税 + 城镇土地使用税 + 环境保护税 + 消费税 + 车船税 + 车辆购置税 + 税务部门罚没收入 +
        地方教育附加 + 其他收入 + 水利建设专项收入 + 其他政府性基金收入 + 残疾人就业保障金 + 文化事业建设费 + 教育费附加
        + 烟叶税) total,
        增值税 fzzs,
        个人所得税 fgrsds,
        耕地占用税 fgdzys,
        印花税 fyhs,
        房产税 ffcs,
        土地增值税 ftdzzs,
        企业所得税 fqysds,
        营业税 fyys,
        资源税 fzys,
        城市维护建设税 fcswhjss,
        契税 fqs,
        城镇土地使用税 fcztdsys,
        烟叶税 fyanys,
        case when 月份='' then '合计' else 月份 end month,
        环境保护税 fhjbhs,
        消费税 fxfs,
        车船税 fccs,
        车辆购置税 fclgzs,
        税务部门罚没收入 fzfbmfmsr,
        地方教育附加 fdfjyfj,
        其他收入 fqtsr,
        水利建设专项收入 fsljszxsr,
        其他政府性基金收入 fqtzfxjjsr,
        残疾人就业保障金 fcjrjybzj,
        文化事业建设费 fwhsyjsf,
        教育费附加 fjyffj
        from (select round(sum(增值税) / 10000, 2) 增值税,
        round(sum(个人所得税) / 10000, 2) 个人所得税,
        round(sum(耕地占用税) / 10000,2) 耕地占用税,
        round(sum(印花税) / 10000,2) 印花税,
        round(sum(房产税) / 10000, 2) 房产税,
        round(sum(土地增值税) / 10000, 2) 土地增值税,
        round(sum(企业所得税) / 10000, 2) 企业所得税,
        round(sum(营业税) / 10000, 2) 营业税,
        round(sum(nvl(资源税, 2)) / 10000,2) as 资源税,
        round(sum(nvl(城市维护建设税, 0)) / 10000, 2) as 城市维护建设税,
        round(sum(nvl(契税, 0)) / 10000, 2) as 契税,
        round(sum(nvl(城镇土地使用税, 0)) / 10000, 2) as 城镇土地使用税,
        f_sys_month 月份,
        round(sum(nvl(环境保护税, 0)) / 10000, 2) as 环境保护税,
        round(sum(nvl(消费税, 0)) / 10000, 2) as 消费税,
        round(sum(nvl(车辆购置税, 0)) / 10000, 2) as 车辆购置税,
        round(sum(nvl(税务部门罚没收入, 0)) / 10000, 2) as 税务部门罚没收入,
        round(sum(nvl(地方教育附加, 0)) / 10000, 2) as 地方教育附加,
        round(sum(nvl(其他收入, 0)) / 10000, 2) as 其他收入,
        round(sum(nvl(水利建设专项收入, 0)) / 10000, 2) as 水利建设专项收入,
        round(sum(nvl(其他政府性基金收入, 0)) / 10000, 2) as 其他政府性基金收入,
        round(sum(nvl(残疾人就业保障金, 0)) / 10000, 2) as 残疾人就业保障金,
        round(sum(nvl(文化事业建设费, 0)) / 10000, 2) as 文化事业建设费,
        round(sum(nvl(教育费附加, 0)) / 10000, 2) as 教育费附加,
        round(sum(nvl(烟叶税, 0)) / 10000, 2) as 烟叶税,
        round(sum(nvl(车船税, 0)) / 10000, 2) as 车船税
        from (select
        sum(增值税) 增值税,
        sum(耕地占用税) 耕地占用税,
        sum(印花税) 印花税,
        sum(房产税) 房产税,
        sum(土地增值税) 土地增值税,
        sum(企业所得税) 企业所得税,
        sum(个人所得税) 个人所得税,
        sum(营业税) 营业税,
        sum(资源税) 资源税,
        sum(城市维护建设税) 城市维护建设税,
        sum(契税) 契税,
        sum(城镇土地使用税) 城镇土地使用税,
        sum(环境保护税) 环境保护税,
        sum(消费税) 消费税,
        sum(车船税) 车船税,
        sum(车辆购置税) 车辆购置税,
        sum(税务部门罚没收入) 税务部门罚没收入,
        sum(地方教育附加) 地方教育附加,
        sum(其他收入) 其他收入,
        sum(水利建设专项收入) 水利建设专项收入,
        sum(其他政府性基金收入) 其他政府性基金收入,
        sum(残疾人就业保障金) 残疾人就业保障金,
        sum(文化事业建设费) 文化事业建设费,
        sum(教育费附加) 教育费附加,
        sum(烟叶税) 烟叶税,
        to_char(入库日期,'mm') f_sys_month
        from ZHZS_T_SS_SRFX
        where 入库日期 between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth}) ,'%Y-%m') and
        last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth}) ,'%Y-%m'))
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            征收项目 in (${fzsxmStr})
        </if>
        <if test="fssqyStr != null and fssqyStr != ''">and
            收款国库 in (${fssqyStr})
        </if>
        <if test="fhymlStr != null and fhymlStr != ''">and
            行业大类 in (${fhymlStr})
        </if>
        group by (to_char(入库日期,'mm'))
        ) as t1
        group by (f_sys_month) with rollup) as t2
        order by case when 月份=''then 00 else 01 end ,月份
    </select>


    <!-- 行业纳税人情况(表)-->
    <select id="industryTaxpayersTable" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.local.sszbfx.model.IncomeInformation">
        select
        case when grouping(fnsrmc) = 1 then concat('合计（共', count(distinct fnsrmc), '户）') else fnsrmc end fnsrmc,
        case when grouping(fnsrmc) = 1 then '' else max(fhyml) end fhyml,
        case when grouping(fnsrmc) = 1 then '' else max(fhydl) end fhydl,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='增值税' then fhj else 0 end else 0
        end)
        / 10000, 2) as fzzs,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='企业所得税' then fhj else 0 end else
        0
        end) / 10000, 2) as fqysds,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='个人所得税' then fhj else 0 end else
        0
        end) / 10000, 2) as fgrsds,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='消费税' then fhj else 0 end else 0
        end)
        / 10000, 2) as fxfs,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='城市维护建设税' then fhj else 0 end
        else 0
        end) / 10000, 2) as fcswhjss,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='车辆购置税' then fhj else 0 end else
        0
        end) / 10000, 2) as fclgzs,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='营业税' then fhj else 0 end else 0
        end)
        / 10000, 2) as fyys,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='房产税' then fhj else 0 end else 0
        end)
        / 10000, 2) as ffcs,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='印花税' then fhj else 0 end else 0
        end)
        / 10000, 2) as fyhs,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='土地增值税' then fhj else 0 end else
        0
        end) / 10000, 2) as ftdzzs,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='耕地占用税' then fhj else 0 end else
        0
        end) / 10000, 2) as fgdzys,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='资源税' then fhj else 0 end else 0
        end)
        / 10000, 2) as fzys,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='契税' then fhj else 0 end else 0
        end)
        / 10000, 2) as fqs,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='车船税' then fhj else 0 end else 0
        end)
        / 10000, 2) as fccs,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='环境保护税' then fhj else 0 end else
        0
        end) / 10000, 2) as fhjbhs,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='城镇土地使用税' then fhj else 0 end
        else 0
        end) / 10000, 2) as fcztdsys,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='烟叶税' then fhj else 0 end else 0
        end)
        / 10000, 2) as fyanys,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='地方教育附加' then fhj else 0 end
        else 0
        end) / 10000, 2) as fdfjyfj,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='教育费附加' then fhj else 0 end else
        0
        end) / 10000, 2) as fjyffj,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='水利建设专项收入' then fhj else 0
        end else 0
        end) / 10000, 2) as fsljszxsr,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='其他收入' then fhj else 0 end else 0
        end) / 10000, 2) as fqtsr,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='残疾人就业保障金' then fhj else 0
        end else 0
        end) / 10000, 2) as fcjrjybzj,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='税务部门罚没收入' then fhj else 0
        end else 0
        end) / 10000, 2) as fzfbmfmsr,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='文化事业建设费' then fhj else 0 end
        else 0
        end) / 10000, 2) as fwhsyjsf,
        round(sum(case when YEAR(frkrq) = #{start_year} then case when fzsxm ='其他政府性基金收入' then fhj else 0
        end else
        0 end) / 10000, 2) as fqtzfxjjsr,
        round(sum(case when YEAR(frkrq) = #{start_year} then fhj else 0 end) / 10000, 2) as yearTotal,
        round(sum(case when YEAR(frkrq) = #{start_year} - 1 then fhj else 0 end) / 10000, 2) as lastYearTotal,
        case
        when sum(case when YEAR(frkrq) = #{start_year} - 1 then fhj else 0 end) = 0 then 0
        else
        round(
        (
        sum(case when YEAR(frkrq) = #{start_year} then fhj else 0 end)
        - sum(case when YEAR(frkrq) = #{start_year} - 1 then fhj else 0 end)
        )
        /
        sum(case when YEAR(frkrq) = #{start_year} - 1 then fhj else 0 end)
        * 100
        , 2)
        end as yearOverYear,
        round((
        sum(case when YEAR(frkrq) = #{start_year} then fhj else 0 end)
        -
        sum(case when YEAR(frkrq) = #{start_year} - 1 then fhj else 0 end)
        ) / 10000 , 2) as fzje
        from tb_dw_srfx_srfx_main sz
        where
        (
        frkrq BETWEEN str_to_date(CONCAT(#{start_year}, '-', #{fstartmonth}, '-01'), '%Y-%m-%d') AND
        last_day(str_to_date(CONCAT(#{start_year}, '-', #{fendmonth}, '-01'), '%Y-%m-%d'))
        or
        frkrq BETWEEN str_to_date(CONCAT(#{start_year} - 1, '-', #{fstartmonth}, '-01'), '%Y-%m-%d') AND
        last_day(str_to_date(CONCAT(#{start_year} - 1, '-', #{fendmonth}, '-01'), '%Y-%m-%d'))
        )
        <if test="fzsxmStr != null and fzsxmStr != ''">and
            fzsxm in (${fzsxmStr})
        </if>
        <if test="fssqyStr != null and fssqyStr != ''">and
            fskgk in (${fssqyStr})
        </if>
        <if test="fhymlStr != null and fhymlStr != ''">and
            fhyml in (${fhymlStr})
        </if>
        <if test="fhydlStr != null and fhydlStr != ''">and
            fhydl in (${fhydlStr})
        </if>
        <if test="fldts == 2">AND
            fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            and region_id in
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        and fzsxm in ('教育费附加',
        '地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
        group by fnsrmc with rollup
        order by yearTotal desc
    </select>

    <!-- 指定纳税人税收查询-->
    <select id="zdnsrsscx" parameterType="com.hnbp.local.sszbfx.model.QueryCondition"
            resultType="com.hnbp.local.sszbfx.model.TaxRevenue">
        select case when grouping(concat(ifnull(fnsrmc, ''), ifnull(纳税人识别号, ''))) = 1 then '合计' else max(fnsrmc)
        end
        fnsrmc,
        case when grouping(concat(ifnull(fnsrmc, ''), ifnull(纳税人识别号, ''))) = 1 then 0 else max(fxh) end fpx,
        case when grouping(concat(ifnull(fnsrmc, ''), ifnull(纳税人识别号, ''))) = 1 then '' else max(纳税人识别号) end
        fnsrsbh,
        case when grouping(concat(ifnull(fnsrmc, ''), ifnull(纳税人识别号, ''))) = 1 then '' else max(行业门类) end
        fhyml,
        case when grouping(concat(ifnull(fnsrmc, ''), ifnull(纳税人识别号, ''))) = 1 then '' else max(收款国库) end
        fskgk,
        round(nvl(sum(本期合计), 0), 2) as yeartotal,
        case
        when nvl(sum(去年同期合计), 0) = 0 then
        0
        else
        round((sum(本期合计) - sum(去年同期合计)) / nvl(sum(去年同期合计), 0) * 100, 2)
        end yearOverYear,
        round(nvl(sum(去年同期合计), 0), 2) as lastYeartotal,
        round(nvl(sum(增值税), 0), 2) as fzzs,
        round(nvl(sum(营业税), 0), 2) as fyys,
        round(nvl(sum(企业所得税), 0), 2) as fqysds,
        round(nvl(sum(个人所得税), 0), 2) as fgrsds,
        round(nvl(sum(土地增值税), 0), 2) as ftdzzs,
        round(nvl(sum(房产税), 0), 2) as ffcs,
        round(nvl(sum(城建税), 0), 2) as fcjs,
        round(nvl(sum(车船税), 0), 2) as fccs,
        round(nvl(sum(车辆购置税), 0), 2) fclgzs,
        round(nvl(sum(耕地占用税), 0), 2) as fgdzys,
        round(nvl(sum(印花税), 0), 2) fyhs,
        round(nvl(sum(环境保护税), 0), 2) fhjbhs,
        round(nvl(sum(契税), 0), 2) fqs,
        round(nvl(sum(消费税), 0), 2) fxfs,
        round(nvl(sum(残疾人就业保障金), 0), 2) fcjrjybzj,
        round(nvl(sum(资源税), 0), 2) fzys,
        round(nvl(sum(地方教育附加), 0), 2) fdfjyfj,
        round(nvl(sum(教育费附加), 0), 2) fjyffj,
        round(nvl(sum(其他收入), 0), 2) fqtsr,
        round(nvl(sum(水利建设专项收入), 0), 2) fsljszxsr,
        round(nvl(sum(城镇土地使用税), 0), 2) fcztdsys,
        round(nvl(sum(烟叶税), 0), 2) fyanys,
        round(nvl(sum(税务部门罚没收入), 0), 2) fzfbmfmsr,
        round(nvl(sum(其他政府性基金收入), 0), 2) fqtzfxjjsr,
        round(nvl(sum(文化事业建设费), 0), 2) fwhsyjsf,
        round(nvl(sum(今年中央级), 0), 2) fjnqzzyj,
        round(nvl(sum(去年中央级), 0), 2) fqnqzzyj,
        round(nvl(sum(今年省市级), 0), 2) fjnqzssj,
        round(nvl(sum(去年省市级), 0), 2) fqnqzssj,
        round(nvl(sum(今年地市级), 0), 2) fjnqzdsj,
        round(nvl(sum(去年地市级), 0), 2) fqnqzdsj,
        round(nvl(sum(今年区县级), 0), 2) fjnqzqxj,
        round(nvl(sum(去年区县级), 0), 2) fqnqzqxj
        from (
        select a.fxh * 1 fxh,
        case when ifnull(fnsrmc, '') = '' then ifnull(sbh.纳税人名称, mc.纳税人名称) else fnsrmc end as fnsrmc,
        case when ifnull(fnsrsbh, '') = '' then ifnull(sbh.纳税人识别号, mc.纳税人识别号) else fnsrsbh end as 纳税人识别号,
        ifnull(sbh.行业门类, mc.行业门类) as 行业门类,
        ifnull(sbh.收款国库, mc.收款国库) as 收款国库,
        ifnull(sbh.本期合计, mc.本期合计) as 本期合计,
        ifnull(sbh.去年同期合计, mc.去年同期合计) as 去年同期合计,
        ifnull(sbh.增值税, mc.增值税) as 增值税,
        ifnull(sbh.营业税, mc.营业税) as 营业税,
        ifnull(sbh.企业所得税, mc.企业所得税) as 企业所得税,
        ifnull(sbh.个人所得税, mc.个人所得税) as 个人所得税,
        ifnull(sbh.土地增值税, mc.土地增值税) as 土地增值税,
        ifnull(sbh.房产税, mc.房产税) as 房产税,
        ifnull(sbh.城建税, mc.城建税) as 城建税,
        ifnull(sbh.车船税, mc.车船税) as 车船税,
        ifnull(sbh.车辆购置税, mc.车辆购置税) as 车辆购置税,
        ifnull(sbh.耕地占用税, mc.耕地占用税) as 耕地占用税,
        ifnull(sbh.印花税, mc.印花税) as 印花税,
        ifnull(sbh.环境保护税, mc.环境保护税) as 环境保护税,
        ifnull(sbh.契税, mc.契税) as 契税,
        ifnull(sbh.消费税, mc.消费税) as 消费税,
        ifnull(sbh.资源税, mc.资源税) as 资源税,
        ifnull(sbh.残疾人就业保障金, mc.残疾人就业保障金) as 残疾人就业保障金,
        ifnull(sbh.地方教育附加, mc.地方教育附加) as 地方教育附加,
        ifnull(sbh.教育费附加, mc.教育费附加) as 教育费附加,
        ifnull(sbh.其他收入, mc.其他收入) as 其他收入,
        ifnull(sbh.水利建设专项收入, mc.水利建设专项收入) as 水利建设专项收入,
        ifnull(sbh.城镇土地使用税, mc.城镇土地使用税) as 城镇土地使用税,
        ifnull(sbh.烟叶税, mc.烟叶税) as 烟叶税,
        ifnull(sbh.税务部门罚没收入, mc.税务部门罚没收入) as 税务部门罚没收入,
        ifnull(sbh.其他政府性基金收入, mc.其他政府性基金收入) as 其他政府性基金收入,
        ifnull(sbh.文化事业建设费, mc.文化事业建设费) as 文化事业建设费,
        ifnull(sbh.今年中央级, mc.今年中央级) as 今年中央级,
        ifnull(sbh.去年中央级, mc.去年中央级) as 去年中央级,
        ifnull(sbh.今年省市级, mc.今年省市级) as 今年省市级,
        ifnull(sbh.去年省市级, mc.去年省市级) as 去年省市级,
        ifnull(sbh.今年地市级, mc.今年地市级) as 今年地市级,
        ifnull(sbh.去年地市级, mc.去年地市级) as 去年地市级,
        ifnull(sbh.今年区县级, mc.今年区县级) as 今年区县级,
        ifnull(sbh.去年区县级, mc.去年区县级) as 去年区县级
        from TB_DW_ZHZS_ZDNSRMD_MAIN a
        left join
        (
        select fnsrsbh 纳税人识别号,
        max(a.fnsrmc) as 纳税人名称,
        max(b.fhyml) as 行业门类,
        max(a.fjdxz) as 收款国库,
        round(sum(case when year(frkrq) = #{start_year} then fhj else 0 end / 10000), 2) 本期合计,
        round(sum(case when year(frkrq) = #{start_year} - 1 then fhj else 0 end / 10000), 2) as 去年同期合计,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='增值税' then fhj/1000 else 0 end else 0
        end
        /
        10000), 2) 增值税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='营业税' then fhj/1000 else 0 end else 0
        end
        /
        10000), 2) 营业税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='企业所得税' then fhj/1000 else 0 end else
        0
        end
        / 10000), 2) 企业所得税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='个人所得税' then fhj/1000 else 0 end else
        0
        end
        / 10000), 2) 个人所得税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='土地增值税' then fhj/1000 else 0 end else
        0
        end
        / 10000), 2) 土地增值税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='房产税' then fhj/1000 else 0 end else 0
        end
        /
        10000), 2) 房产税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='城市维护建设税' then fhj/1000 else 0 end
        else 0
        end / 10000), 2) 城建税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='车船税' then fhj/1000 else 0 end else 0
        end
        /
        10000), 2) 车船税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='车辆购置税' then fhj/1000 else 0 end else
        0
        end
        / 10000), 2) 车辆购置税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='耕地占用税' then fhj/1000 else 0 end else
        0
        end
        / 10000), 2) 耕地占用税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='印花税' then fhj/1000 else 0 end else 0
        end
        /
        10000), 2) 印花税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='环境保护税' then fhj/1000 else 0 end else
        0
        end
        / 10000), 2) 环境保护税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='契税' then fhj/1000 else 0 end else 0 end
        /
        10000), 2) 契税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='消费税' then fhj/1000 else 0 end else 0
        end
        /
        10000), 2) 消费税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='资源税' then fhj/1000 else 0 end else 0
        end
        /
        10000), 2) 资源税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='残疾人就业保障金' then fhj/1000 else 0 end
        else 0
        end / 10000), 2) 残疾人就业保障金,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='地方教育附加' then fhj/1000 else 0 end
        else
        0 end
        / 10000), 2) 地方教育附加,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='教育费附加' then fhj/1000 else 0 end else
        0
        end
        / 10000), 2) 教育费附加,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='其他收入' then fhj/1000 else 0 end else 0
        end /
        10000), 2) 其他收入,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='水利建设专项收入' then fhj/1000 else 0 end
        else 0
        end / 10000), 2) 水利建设专项收入,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='文化事业建设费' then fhj/1000 else 0 end
        else 0
        end / 10000), 2) 文化事业建设费,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='城镇土地使用税' then fhj/1000 else 0 end
        else 0
        end / 10000), 2) 城镇土地使用税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='烟叶税' then fhj/1000 else 0 end else 0
        end
        /
        10000), 2) 烟叶税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='税务部门罚没收入' then fhj/1000 else 0 end
        else 0
        end / 10000), 2) 税务部门罚没收入,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='其他政府性基金收入' then fhj/1000 else 0
        end else 0
        end / 10000), 2) 其他政府性基金收入,
        round(sum(case when year(frkrq) = #{start_year} then fzyj else 0 end / 10000), 2) 今年中央级,
        round(sum(case when year(frkrq) = #{start_year} - 1 then fzyj else 0 end / 10000), 2) as 去年中央级,
        round(sum(case when year(frkrq) = #{start_year} then fssj else 0 end / 10000), 2) 今年省市级,
        round(sum(case when year(frkrq) = #{start_year} - 1 then fssj else 0 end / 10000), 2) as 去年省市级,
        round(sum(case when year(frkrq) = #{start_year} then fdsj else 0 end / 10000), 2) 今年地市级,
        round(sum(case when year(frkrq) = #{start_year} - 1 then fdsj else 0 end / 10000), 2) as 去年地市级,
        round(sum(case when year(frkrq) = #{start_year} then fqxj else 0 end / 10000), 2) 今年区县级,
        round(sum(case when year(frkrq) = #{start_year} - 1 then fqxj else 0 end / 10000), 2) as 去年区县级
        from tb_dw_srfx_srfx_main a
        left join TB_DW_ZHZS_NSR_HYML_SKGK_GEN b on a.fnsrmc = b.fnsrmc
        where fnsrsbh is not null
        <if test="start_year != null and start_year != ''">
            and (
            frkrq between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth}),'%Y-%m') and
            last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth}),'%Y-%m'))
            or
            frkrq between str_to_date(CONCAT(#{start_year} - 1,'-',#{fstartmonth}),'%Y-%m') and
            last_day(str_to_date(CONCAT(#{start_year} - 1,'-',#{fendmonth}),'%Y-%m'))
            )
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">
            and fzsxm in (${fzsxmStr})
        </if>
        group by fnsrsbh
        ) sbh
        on a.fnsrsbh = sbh.纳税人识别号
        left join
        (
        select a.fnsrmc 纳税人名称,
        max(fnsrsbh) as 纳税人识别号,
        max(b.fhyml) as 行业门类,
        max(a.fjdxz) as 收款国库,
        round(sum(case when year(frkrq) = #{start_year} then fhj else 0 end / 10000), 2) 本期合计,
        round(sum(case when year(frkrq) = #{start_year} - 1 then fhj else 0 end / 10000), 2) as 去年同期合计,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='增值税' then fhj/1000 else 0 end else 0
        end
        /
        10000), 2) 增值税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='营业税' then fhj/1000 else 0 end else 0
        end
        /
        10000), 2) 营业税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='企业所得税' then fhj/1000 else 0 end else
        0
        end
        / 10000), 2) 企业所得税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='个人所得税' then fhj/1000 else 0 end else
        0
        end
        / 10000), 2) 个人所得税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='土地增值税' then fhj/1000 else 0 end else
        0
        end
        / 10000), 2) 土地增值税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='房产税' then fhj/1000 else 0 end else 0
        end
        /
        10000), 2) 房产税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='城市维护建设税' then fhj/1000 else 0 end
        else 0
        end / 10000), 2) 城建税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='车船税' then fhj/1000 else 0 end else 0
        end
        /
        10000), 2) 车船税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='车辆购置税' then fhj/1000 else 0 end else
        0
        end
        / 10000), 2) 车辆购置税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='耕地占用税' then fhj/1000 else 0 end else
        0
        end
        / 10000), 2) 耕地占用税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='印花税' then fhj/1000 else 0 end else 0
        end
        /
        10000), 2) 印花税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='环境保护税' then fhj/1000 else 0 end else
        0
        end
        / 10000), 2) 环境保护税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='契税' then fhj/1000 else 0 end else 0 end
        /
        10000), 2) 契税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='消费税' then fhj/1000 else 0 end else 0
        end
        /
        10000), 2) 消费税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='资源税' then fhj/1000 else 0 end else 0
        end
        /
        10000), 2) 资源税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='残疾人就业保障金' then fhj/1000 else 0 end
        else 0
        end / 10000), 2) 残疾人就业保障金,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='地方教育附加' then fhj/1000 else 0 end
        else
        0 end
        / 10000), 2) 地方教育附加,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='教育费附加' then fhj/1000 else 0 end else
        0
        end
        / 10000), 2) 教育费附加,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='其他收入' then fhj/1000 else 0 end else 0
        end /
        10000), 2) 其他收入,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='水利建设专项收入' then fhj/1000 else 0 end
        else 0
        end / 10000), 2) 水利建设专项收入,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='文化事业建设费' then fhj/1000 else 0 end
        else 0
        end / 10000), 2) 文化事业建设费,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='城镇土地使用税' then fhj/1000 else 0 end
        else 0
        end / 10000), 2) 城镇土地使用税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='烟叶税' then fhj/1000 else 0 end else 0
        end
        /
        10000), 2) 烟叶税,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='税务部门罚没收入' then fhj/1000 else 0 end
        else 0
        end / 10000), 2) 税务部门罚没收入,
        round(sum(case when year(frkrq) =#{start_year} then case when fzsxm ='其他政府性基金收入' then fhj/1000 else 0
        end else 0
        end / 10000), 2) 其他政府性基金收入,
        round(sum(case when year(frkrq) = #{start_year} then fzyj else 0 end / 10000), 2) 今年中央级,
        round(sum(case when year(frkrq) = #{start_year} - 1 then fzyj else 0 end / 10000), 2) as 去年中央级,
        round(sum(case when year(frkrq) = #{start_year} then fssj else 0 end / 10000), 2) 今年省市级,
        round(sum(case when year(frkrq) = #{start_year} - 1 then fssj else 0 end / 10000), 2) as 去年省市级,
        round(sum(case when year(frkrq) = #{start_year} then fdsj else 0 end / 10000), 2) 今年地市级,
        round(sum(case when year(frkrq) = #{start_year} - 1 then fdsj else 0 end / 10000), 2) as 去年地市级,
        round(sum(case when year(frkrq) = #{start_year} then fqxj else 0 end / 10000), 2) 今年区县级,
        round(sum(case when year(frkrq) = #{start_year} - 1 then fqxj else 0 end / 10000), 2) as 去年区县级
        from tb_dw_srfx_srfx_main a
        left join TB_DW_ZHZS_NSR_HYML_SKGK_GEN b on a.fnsrmc = b.fnsrmc
        where a.fnsrmc is not null
        <if test="start_year != null and start_year != ''">
            and (
            frkrq between str_to_date(CONCAT(#{start_year},'-',#{fstartmonth}),'%Y-%m') and
            last_day(str_to_date(CONCAT(#{start_year},'-',#{fendmonth}),'%Y-%m'))
            or
            frkrq between str_to_date(CONCAT(#{start_year} - 1,'-',#{fstartmonth}),'%Y-%m') and
            last_day(str_to_date(CONCAT(#{start_year} - 1,'-',#{fendmonth}),'%Y-%m'))
            )
        </if>
        <if test="fzsxmStr != null and fzsxmStr != ''">
            and fzsxm in (${fzsxmStr})
        </if>
        group by a.fnsrmc
        ) mc
        on sbh.纳税人识别号 is null and a.fnsrmc = mc.纳税人名称
        ) as t1
        group by concat(ifnull(fnsrmc, ''), ifnull(纳税人识别号, '')) with rollup
        <if test="type == 1">
            order by fpx
        </if>
        <if test="type == 2">
            order by grouping(concat(ifnull(fnsrmc, ''), ifnull(纳税人识别号, ''))) desc, yeartotal desc
        </if>
    </select>

    <delete id="delNsr">
        delete
        from TB_DW_ZHZS_ZDNSRMD_MAIN
    </delete>

    <insert id="addNsr" parameterType="java.util.HashMap">
        insert INTO TB_DW_ZHZS_ZDNSRMD_MAIN(fnsrsbh, fnsrmc, fxh, fid)
        values (#{fnsrsbh}, #{fnsrmc}, #{fxh}, #{fid})
    </insert>

    <!-- 企业异常分析-->
    <select id="enterpriseAbnormalAnalysis" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select
        case when fnsrmc='合计' then concat('合计(',rn,')') else fnsrmc end fnsrmc
        ,fjdxz,fhydl,jn_sssr,qn_sssr
        from(
        SELECT
        IF( grouping ( t1.fnsrmc ) = 1, '合计', t1.fnsrmc ) fnsrmc,
        IF(grouping ( t1.fnsrmc ) = 1,'',MAX( t1.fjdxz )) fjdxz,
        IF(grouping ( t1.fnsrmc ) = 1,'',MAX( t1.fhydl )) fhydl,
        0 as jn_sssr,
        SUM( t1.sssr ) qn_sssr,
        sum(rn)rn
        FROM
        (
        SELECT
        fnsrmc,
        MAX(fjdxz) fjdxz,
        MAX(fhydl) fhydl,
        sum(fshj) as sssr,
        1 as rn
        FROM
        tb_dw_srfx_srfx_main
        WHERE frkrq BETWEEN DATE_SUB(#{fStartRkrq},INTERVAL 1 YEAR) AND DATE_SUB(#{fEndRkrq},INTERVAL 1 YEAR)
        and LENGTH(fnsrmc)&gt;9
        <if test="fnsrmc != null and fnsrmc != ''">AND
            fnsrmc like concat('%',#{fnsrmc},'%')
        </if>
        <if test="fhydlList != null">AND
            fhydl IN
            <foreach collection="fhydlList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fssqyList != null">AND
            fjdxz IN
            <foreach collection="fssqyList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="fldts == 2">AND
            fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        GROUP BY
        fnsrmc
        ) AS t1
        LEFT JOIN (
        SELECT
        fnsrmc,sum(fshj) as sssr
        FROM
        tb_dw_srfx_srfx_main
        WHERE frkrq BETWEEN #{fStartRkrq} AND #{fEndRkrq}
        <if test="fldts == 2">AND
            fyskm NOT LIKE '%留抵退%'
        </if>
        <if test="fmdts == 2">AND fyskm != '免抵调增增值税'</if>
        GROUP BY
        fnsrmc
        ) AS t2 ON t1.fnsrmc = t2.fnsrmc
        where ifnull(t2.sssr, 0) = 0
        GROUP BY
        t1.fnsrmc WITH ROLLUP
        ORDER BY
        grouping ( t1.fnsrmc ) DESC,
        qn_sssr DESC
        )a
    </select>

</mapper>
