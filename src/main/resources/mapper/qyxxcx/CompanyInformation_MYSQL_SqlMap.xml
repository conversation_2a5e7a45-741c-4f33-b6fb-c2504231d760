<?xml version="1.0" encoding="UTF-8"?><!--

    Copyright 2004-2024 the original author or authors.

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

--><!--Converted at: Tue Oct 22 10:19:50 CST 2024-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.qyxxcx.mapper.CompanyInformationMapper">
    <!-- 指定纳税人税收查询-->
    <select id="zdnsrsscx" parameterType="com.hnbp.local.sszbfx.model.QueryCondition" resultType="java.util.HashMap">
        -- 汇总纳税人数据（合并今年和去年的数据）
        SELECT
        CASE WHEN GROUPING(concat(fnsrsbh, fnsrmc, fhyml, fjdxz)) = 1 THEN '合计' ELSE max(fnsrmc) END AS fnsrmc,
        ROW_NUMBER() OVER (ORDER BY GROUPING(concat(fnsrsbh, fnsrmc, fhyml, fjdxz)) DESC) AS fpx,
        CASE WHEN GROUPING(concat(fnsrsbh, fnsrmc, fhyml, fjdxz)) = 1 THEN '' ELSE max(fnsrsbh) END AS fnsrsbh,
        CASE WHEN GROUPING(concat(fnsrsbh, fnsrmc, fhyml, fjdxz)) = 1 THEN '' ELSE max(fhyml) END AS fhyml,
        CASE WHEN GROUPING(concat(fnsrsbh, fnsrmc, fhyml, fjdxz)) = 1 THEN '' ELSE max(fjdxz) END AS fskgk,
        ROUND(SUM(ffhj) / 10000, 2) AS yeartotal,
        CASE WHEN ROUND(SUM(fqnhj) / 10000, 2) = 0 THEN 0
        ELSE ROUND((SUM(ffhj) - sum(fqnhj)) / sum(fqnhj) * 100, 2) END AS yearOverYear,
        ROUND(SUM(fqnhj) / 10000, 2) AS lastYeartotal,
        ROUND(SUM(fmdt) / 10000, 2) AS fmdt,
        ROUND(SUM(fzzs) / 10000, 2) AS fzzs,
        ROUND(SUM(fyys) / 10000, 2) AS fyys,
        ROUND(SUM(fqysds) / 10000, 2) AS fqysds,
        ROUND(SUM(fgrsds) / 10000, 2) AS fgrsds,
        ROUND(SUM(ftdzzs) / 10000, 2) AS ftdzzs,
        ROUND(SUM(ffcs) / 10000, 2) AS ffcs,
        ROUND(SUM(fcshjjs) / 10000, 2) AS fcjs,
        ROUND(SUM(fccs) / 10000, 2) AS fccs,
        ROUND(SUM(fclgzs) / 10000, 2) AS fclgzs,
        ROUND(SUM(fgdzy) / 10000, 2) AS fgdzy,
        ROUND(SUM(fihs) / 10000, 2) AS fihs,
        ROUND(SUM(fhjbhx) / 10000, 2) AS fhjbhs,
        ROUND(SUM(fqshs) / 10000, 2) AS fqs,
        ROUND(SUM(fxs) / 10000, 2) AS fxfs,
        ROUND(SUM(fzyss) / 10000, 2) AS fzyss,
        ROUND(SUM(fczrjybzj) / 10000, 2) AS fczrjybzj,
        ROUND(SUM(fdfjyfj) / 10000, 2) AS fdfjyfj,
        ROUND(SUM(fjyfj) / 10000, 2) AS fjyffj,
        ROUND(SUM(fqtsr) / 10000, 2) AS fqtsr,
        ROUND(SUM(fsljzxsr) / 10000, 2) AS fsljzxsr,
        ROUND(SUM(fwhsyjsf) / 10000, 2) AS fwhsyjsf,
        ROUND(SUM(fcztsys) / 10000, 2) AS fcztdsys,
        ROUND(SUM(fyy) / 10000, 2) AS fyanys,
        ROUND(SUM(fswbfmsr) / 10000, 2) AS fzfbfmsr,
        ROUND(SUM(fqzzgxxjsr) / 10000, 2) AS fqtzfxjjsr,
        ROUND(SUM(fjyzj) / 10000, 2) AS fjnqzzyj,
        ROUND(SUM(fqnzj) / 10000, 2) AS fqnqzzyj,
        ROUND(SUM(fjsjsj) / 10000, 2) AS fjnqzssj,
        ROUND(SUM(fqnsjsj) / 10000, 2) AS fqnqzssj,
        ROUND(SUM(fjdsj) / 10000, 2) AS fjnqzdsj,
        ROUND(SUM(fqndsj) / 10000, 2) AS fqnqzdsj,
        ROUND(SUM(fjqxj) / 10000, 2) AS fjnqzqxj,
        ROUND(SUM(fqnxj) / 10000, 2) AS fqnqzqxj
        FROM (
        -- 今年数据
        SELECT
        a.fnsrsbh,
        a.fnsrmc,
        fhyml,
        a.fjdxz AS fjdxz,
        SUM(fhj) AS ffhj,
        0 AS fqnhj,
        sum(case when fyskm = '免抵调增增值税' then fhj else 0 end) fmdt,
        SUM(CASE WHEN fzsxm = '增值税' THEN fhj ELSE 0 END) AS fzzs,  -- 增值税
        SUM(CASE WHEN fzsxm = '营业税' THEN fhj ELSE 0 END) AS fyys,  -- 营业税
        SUM(CASE WHEN fzsxm = '企业所得税' THEN fhj ELSE 0 END) AS fqysds, -- 企业所得税
        SUM(CASE WHEN fzsxm = '个人所得税' THEN fhj ELSE 0 END) AS fgrsds, -- 个人所得税
        SUM(CASE WHEN fzsxm = '土地增值税' THEN fhj ELSE 0 END) AS ftdzzs,  -- 土地增值税
        SUM(CASE WHEN fzsxm = '房产税' THEN fhj ELSE 0 END) AS ffcs,   -- 房产税
        SUM(CASE WHEN fzsxm = '城市维护建设税' THEN fhj ELSE 0 END) AS fcshjjs, -- 城市维护建设税
        SUM(CASE WHEN fzsxm = '车船税' THEN fhj ELSE 0 END) AS fccs,   -- 车船税
        SUM(CASE WHEN fzsxm = '车辆购置税' THEN fhj ELSE 0 END) AS fclgzs,  -- 车辆购置税
        SUM(CASE WHEN fzsxm = '耕地占用税' THEN fhj ELSE 0 END) AS fgdzy,  -- 耕地占用税
        SUM(CASE WHEN fzsxm = '印花税' THEN fhj ELSE 0 END) AS fihs,    -- 印花税
        SUM(CASE WHEN fzsxm = '环境保护税' THEN fhj ELSE 0 END) AS fhjbhx, -- 环境保护税
        SUM(CASE WHEN fzsxm = '契税' THEN fhj ELSE 0 END) AS fqshs,  -- 契税
        SUM(CASE WHEN fzsxm = '消费税' THEN fhj ELSE 0 END) AS fxs,   -- 消费税
        SUM(CASE WHEN fzsxm = '资源税' THEN fhj ELSE 0 END) AS fzyss,  -- 资源税
        SUM(CASE WHEN fzsxm = '残疾人就业保障金' THEN fhj ELSE 0 END) AS fczrjybzj,  -- 残疾人就业保障金
        SUM(CASE WHEN fzsxm = '地方教育附加' THEN fhj ELSE 0 END) AS fdfjyfj, -- 地方教育附加
        SUM(CASE WHEN fzsxm = '教育费附加' THEN fhj ELSE 0 END) AS fjyfj,   -- 教育费附加
        SUM(CASE WHEN fzsxm = '其他收入' THEN fhj ELSE 0 END) AS fqtsr,  -- 其他收入
        SUM(CASE WHEN fzsxm = '水利建设专项收入' THEN fhj ELSE 0 END) AS fsljzxsr, -- 水利建设专项收入
        SUM(CASE WHEN fzsxm = '文化事业建设费' THEN fhj ELSE 0 END) AS fwhsyjsf, -- 文化事业建设费
        SUM(CASE WHEN fzsxm = '城镇土地使用税' THEN fhj ELSE 0 END) AS fcztsys, -- 城镇土地使用税
        SUM(CASE WHEN fzsxm = '烟叶税' THEN fhj ELSE 0 END) AS fyy,     -- 烟叶税
        SUM(CASE WHEN fzsxm = '税务部门罚没收入' THEN fhj ELSE 0 END) AS fswbfmsr, -- 税务部门罚没收入
        SUM(CASE WHEN fzsxm = '其他政府性基金收入' THEN fhj ELSE 0 END) AS fqzzgxxjsr, -- 其他政府性基金收入
        SUM(fzyj) AS fjyzj,
        0 AS fqnzj,
        SUM(fssj) AS fjsjsj,
        0 AS fqnsjsj,
        SUM(fdsj) AS fjdsj,
        0 AS fqndsj,
        SUM(fqxj) AS fjqxj,
        0 AS fqnxj
        FROM tb_dw_srfx_srfx_main a
        RIGHT JOIN TB_DW_ZHZS_ZDNSRMD_MAIN tmp ON a.FNSRMC = tmp.FNSRMC OR a.fnsrsbh = tmp.fnsrsbh
        WHERE frkrq BETWEEN STR_TO_DATE(CONCAT(#{start_year},'-',#{fstartmonth},'-01'), '%Y-%m-%d')
        AND LAST_DAY(STR_TO_DATE(CONCAT(#{start_year},'-',#{fendmonth},'-01'), '%Y-%m-%d'))
        AND fzsxm IN ('增值税','企业所得税','个人所得税','资源税','城市维护建设税','印花税','消费税','关税','船舶吨税','车辆购置税',
        '土地增值税','烟叶税','城镇土地使用税','契税','耕地占用税','车船税','房产税','环境保护税','营业税','其他收入','教育费附加','地方教育附加'
        ,'水利建设专项收入','文化事业建设费','残疾人就业保障金','税务部门罚没收入','其他政府性基金收入')
        <if test="fzsxmStr != null and fzsxmStr != ''">
            AND fzsxm IN (${fzsxmStr})
        </if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            AND a.region_id IN
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="ftype == 2">
            AND fyskm NOT LIKE '%免抵调%'
        </if>
        GROUP BY a.fnsrsbh, a.fnsrmc, fhyml, a.fjdxz

        UNION ALL

        -- 去年数据
        SELECT
        a.fnsrsbh,
        a.fnsrmc,
        fhyml,
        a.fjdxz AS fjdxz,
        0 AS ffhj,
        SUM(fhj) AS fqnhj,
        0 AS fmdt,            -- 增值税（去年）
        0 AS fzzs,            -- 增值税（去年）
        0 AS fyys,            -- 营业税（去年）
        0 AS fqysds,          -- 企业所得税（去年）
        0 AS fgrsds,          -- 个人所得税（去年）
        0 AS ftdzzs,          -- 土地增值税（去年）
        0 AS ffcs,            -- 房产税（去年）
        0 AS fcshjjs,         -- 城市维护建设税（去年）
        0 AS fccs,            -- 车船税（去年）
        0 AS fclgzs,          -- 车辆购置税（去年）
        0 AS fgdzy,           -- 耕地占用税（去年）
        0 AS fihs,            -- 印花税（去年）
        0 AS fhjbhx,          -- 环境保护税（去年）
        0 AS fqshs,           -- 契税（去年）
        0 AS fxs,             -- 消费税（去年）
        0 AS fzyss,           -- 资源税（去年）
        0 AS fczrjybzj,       -- 残疾人就业保障金（去年）
        0 AS fdfjyfj,         -- 地方教育附加（去年）
        0 AS fjyfj,           -- 教育费附加（去年）
        0 AS fqtsr,           -- 其他收入（去年）
        0 AS fsljzxsr,        -- 水利建设专项收入（去年）
        0 AS fwhsyjsf,        -- 文化事业建设费（去年）
        0 AS fcztsys,         -- 城镇土地使用税（去年）
        0 AS fyy,             -- 烟叶税（去年）
        0 AS fswbfmsr,        -- 税务部门罚没收入（去年）
        0 AS fqzzgxxjsr,      -- 其他政府性基金收入（去年）
        0 AS fjyzj,            -- 今年中央级（设置今年的值为 0）
        SUM(fzyj) AS fqnzj,    -- 去年中央级
        0 AS fjsjsj,           -- 今年省市级（设置今年的值为 0）
        SUM(fssj) AS fqnsjsj,  -- 去年省市级
        0 AS fjdsj,            -- 今年地市级（设置今年的值为 0）
        SUM(fdsj) AS fqndsj,   -- 去年地市级
        0 AS fjqxj,            -- 今年区县级（设置今年的值为 0）
        SUM(fqxj) AS fqnxj     -- 去年区县级
        FROM tb_dw_srfx_srfx_main a
        RIGHT JOIN TB_DW_ZHZS_ZDNSRMD_MAIN tmp ON a.FNSRMC = tmp.FNSRMC OR a.fnsrsbh = tmp.fnsrsbh
        WHERE frkrq BETWEEN STR_TO_DATE(CONCAT(#{start_year}-1,'-',#{fstartmonth},'-01'), '%Y-%m-%d')
        AND LAST_DAY(STR_TO_DATE(CONCAT(#{start_year}-1,'-',#{fendmonth},'-01'), '%Y-%m-%d'))
        AND fzsxm IN ('增值税','企业所得税','个人所得税','资源税','城市维护建设税','印花税','消费税','关税','船舶吨税','车辆购置税',
        '土地增值税','烟叶税','城镇土地使用税','契税','耕地占用税','车船税','房产税','环境保护税','营业税','其他收入','教育费附加','地方教育附加'
        ,'水利建设专项收入','文化事业建设费','残疾人就业保障金','税务部门罚没收入','其他政府性基金收入')
        <if test="fzsxmStr != null and fzsxmStr != ''">
            AND fzsxm IN (${fzsxmStr})
        </if>
        <bind name="dataPermissionMap" value="@com.hnbp.common.core.context.SecurityContextHolder@getDataPermission()"/>
        <if test="dataPermissionMap != null and dataPermissionMap.permis_ssqy_list != null">
            AND a.region_id IN
            <foreach collection="dataPermissionMap.permis_ssqy_list" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="ftype == 2">
            AND fyskm NOT LIKE '%免抵调%'
        </if>
        GROUP BY a.fnsrsbh, a.fnsrmc, fhyml, a.fjdxz
        ) AS tmp
        GROUP BY concat(fnsrsbh, fnsrmc, fhyml, fjdxz) WITH ROLLUP
        ORDER BY
        CASE WHEN concat(fnsrsbh, fnsrmc, fhyml, fjdxz) IS NULL THEN 1 ELSE 0 END,
        fpx
    </select>
</mapper>
